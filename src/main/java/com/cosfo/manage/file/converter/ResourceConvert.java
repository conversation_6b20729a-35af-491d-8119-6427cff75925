package com.cosfo.manage.file.converter;

import com.cosfo.manage.file.model.po.CommonResource;
import com.cosfo.manage.file.model.vo.PicResourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/6/25 21:47
 * @Description:
 */
@Mapper
public interface ResourceConvert {
    ResourceConvert INSTANCE = Mappers.getMapper(ResourceConvert.class);

    List<PicResourceVO> convert2Vos(List<CommonResource> entities);

    @Mapping(source = "resourceName",target = "picName")
    @Mapping(source = "id",target = "picResId")
    @Mapping(source = "resourcePath",target = "picUrl")
    PicResourceVO convert2Vo(CommonResource entitiy);

}
