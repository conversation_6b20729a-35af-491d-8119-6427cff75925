package com.cosfo.manage.msg.model.resp;

import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MsgNoticeDetailVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;
    /**
     * 公告内容
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime pushTime;

    /**
     * 发布门店 只有id 如果只有一个元素且id=0  那就是全部门店
     */
    private List<MerchantStoreVO> storeList;
    /**
     * 发布状态0=定时发布,1=立即发布
     */
    private Integer pushType;
    /**
     * 是否开启点赞 0=关闭；1开启
     */
    private Integer supportSwitch;

    /**
     * 阅读数
     */
    private Integer readAmount;

    /**
     * 点赞数
     */
    private Integer supportAmount;
}
