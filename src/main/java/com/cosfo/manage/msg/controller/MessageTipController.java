package com.cosfo.manage.msg.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.msg.model.req.MessageUnReadDTO;
import com.cosfo.manage.msg.model.req.MsgTipMarkDTO;
import com.cosfo.manage.msg.model.req.MessageTipPageDTO;
import com.cosfo.manage.msg.model.resp.MsgTipVO;
import com.cosfo.manage.msg.service.SendLogService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:消息提示
 */
@RestController
@RequestMapping("/msg/tip")
public class MessageTipController extends BaseController {

    @Resource
    private SendLogService sendLogService;

    /**
     * 提醒列表
     */
    @PostMapping("page")
    public CommonResult<PageInfo<MsgTipVO>> page(@Valid @RequestBody MessageTipPageDTO messageTipPageDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(sendLogService.page(messageTipPageDTO, merchantInfoDTO));
    }

    /**
     * 标记
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("markBatch")
    public CommonResult<Void> markBatch(@Valid @RequestBody MsgTipMarkDTO msgTipMarkDTO) {
        sendLogService.markBatch(msgTipMarkDTO, getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 查询未读数量
     */
    @PostMapping("countUnRead")
    public CommonResult<Integer> countUnRead(@RequestBody MessageUnReadDTO messageUnReadDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(sendLogService.countUnRead(messageUnReadDTO, merchantInfoDTO));
    }

    /**
     * 提醒消息列表
     */
    @PostMapping("warn/list")
    public CommonResult<List<MsgTipVO>> warnList() {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(sendLogService.warnList(merchantInfoDTO));
    }

}
