package com.cosfo.manage.msg.service.impl;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsSenderFactory;
import com.cosfo.manage.facade.MessageServiceFacade;
import com.cosfo.manage.msg.model.req.MsgNoticeEditDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeQueryDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeStoreQueryDTO;
import com.cosfo.manage.msg.model.req.ReadOrSupportLogQeryDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeDetailVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeListVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeMerchantStoreVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeStoreAccountVO;
import com.cosfo.manage.msg.service.NoticeService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
public class NoticeServiceImpl implements NoticeService {

    @Autowired
    private MessageServiceFacade messageServiceFacade;
    @Resource
    private SmsSenderFactory smsSenderFactory;
    @Override
    public PageInfo<MsgNoticeListVO> pageNotic(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.pageNotic(req,contextInfoDTO);
    }

    @Override
    public MsgNoticeDetailVO detail(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.detail(req,contextInfoDTO);
    }

    @Override
    public Long edit(MsgNoticeEditDTO req, LoginContextInfoDTO contextInfoDTO) {
       return messageServiceFacade.edit(req,contextInfoDTO);
    }

    @Override
    public void delete(Long id, LoginContextInfoDTO contextInfoDTO) {
        messageServiceFacade.delete(id,contextInfoDTO);
    }

    @Override
    public PageInfo<MsgNoticeMerchantStoreVO> pageStore(MsgNoticeStoreQueryDTO req, LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.pageStore(req,contextInfoDTO);
    }

    @Override
    public PageInfo<MsgNoticeStoreAccountVO> pageReadOrSupportLog(ReadOrSupportLogQeryDTO dto, LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.pageReadOrSupportLog(dto,contextInfoDTO);
    }

    @Override
    public void sendSmsCode(Sms sms) {
        // 发送验证码
        boolean success = smsSenderFactory.getSmsSender().sendSms(sms);
        if (!success) {
            throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
        }
    }
}