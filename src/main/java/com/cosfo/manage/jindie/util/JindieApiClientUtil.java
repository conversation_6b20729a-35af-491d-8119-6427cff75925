package com.cosfo.manage.jindie.util;

import com.cosfo.manage.jindie.facade.JindieFacade;
import com.kingdee.service.ApiClient;
import com.kingdee.service.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 金蝶API客户端工具类
 * 用于获取已配置好token的API客户端
 */
@Component
@Slf4j
public class JindieApiClientUtil {

    @Autowired
    private JindieFacade jindieFacade;

    /**
     * 获取已配置好token的API客户端
     *
     * @return 配置好的API客户端
     */
    public ApiClient getApiClient() {
        ApiClient apiClient = Configuration.getDefaultApiClient();
        String token = jindieFacade.getToken();
        log.info("设置金蝶API客户端token: {}", token);
        apiClient.setAppToken(token);
        return apiClient;
    }
}
