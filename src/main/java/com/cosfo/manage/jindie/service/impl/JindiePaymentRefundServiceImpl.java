package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.jindie.service.JindiePaymentRefundService;
import com.cosfo.manage.jindie.util.JindieApiClientUtil;
import com.cosfo.manage.open.model.po.OpenDocCodeMapping;
import com.cosfo.manage.open.repository.OpenDocCodeMappingRepository;
import com.cosfo.manage.order.model.po.payment.Refund;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.data.api.ArCreditRetApi;
import com.kingdee.service.data.entity.ArCreditRetSaveRequest;
import com.kingdee.service.data.entity.ArCreditRetSaveRequestPayEntry;
import com.kingdee.service.data.entity.ArCreditRetSaveRequestSourceBillEntry;
import com.kingdee.service.data.entity.SaveReply;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 金蝶收款退款单服务实现
 */
@Service
@Slf4j
public class JindiePaymentRefundServiceImpl implements JindiePaymentRefundService {

    @Resource
    private JindieApiClientUtil jindieApiClientUtil;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;
    @Resource
    private OpenDocCodeMappingRepository mappingRepository;

    @Override
    public SaveReply pushPaymentRefundToJindie(Refund refund) {
        try {
            // 构建金蝶收款退款单请求
            ArCreditRetSaveRequest request = buildArCreditRetSaveReq(refund);

            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            ArCreditRetApi arCreditRetApi = new ArCreditRetApi(apiClient);

            // 推送到金蝶
            SaveReply saveReply = arCreditRetApi.arCreditRetSave(request);
            log.info("推送退款单到金蝶成功, refundNo: {}, response: {}", refund.getRefundNo(), saveReply);
            return saveReply;
        } catch (ApiException e) {
            log.error("推送退款单到金蝶异常, refundNo: {}, 错误码: {}, 错误信息: {}",
                    refund.getRefundNo(), e.getCode(), e.getMessage(), e);
            throw new RuntimeException("推送退款单到金蝶异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("推送退款单到金蝶异常, refundNo: {}", refund.getRefundNo(), e);
            throw new RuntimeException("推送退款单到金蝶异常: " + e.getMessage(), e);
        }
    }


    @Override
    public int batchPushPaymentRefundToJindie(List<Refund> refunds) {
        if (CollectionUtils.isEmpty(refunds)) {
            return 0;
        }

        int successCount = 0;
        for (Refund refund : refunds) {
            try {
                SaveReply response = pushPaymentRefundToJindie(refund);
                if (response != null && response.getIds() != null && !response.getIds().isEmpty()) {
                    successCount++;
                    log.info("推送退款单到金蝶成功, refundNo: {}", refund.getRefundNo());
                } else {
                    log.error("推送退款单到金蝶失败, refundNo: {}, response: {}",
                            refund.getRefundNo(), response);
                }
            } catch (Exception e) {
                log.error("推送退款单w到金蝶异常, refundNo: {}", refund.getRefundNo(), e);
            }
        }

        log.info("批量推送退款单到金蝶完成, 总数: {}, 成功数: {}", refunds.size(), successCount);
        return successCount;
    }


    /**
     * 构建金蝶收款退款单请求
     *
     * @param refund 退款单
     * @return 金蝶收款退款单请求
     */
    private ArCreditRetSaveRequest buildArCreditRetSaveReq(Refund refund) {
        ArCreditRetSaveRequest request = new ArCreditRetSaveRequest();

        // 设置基本信息 - 必填字段
        request.setBillDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        // 通过售后单ID获取订单号
        OrderAfterSaleResp afterSaleResp = orderAfterSaleQueryFacade.getOrderAfterSaleById(refund.getAfterSaleId());
        String orderNo = afterSaleResp.getOrderNo();
        request.setBillNo(orderNo);

        request.setCustomerId(String.valueOf(afterSaleResp.getStoreId()));

        // 设置退款信息分录 - 必填字段
        List<ArCreditRetSaveRequestPayEntry> payEntryList = new ArrayList<>();
        ArCreditRetSaveRequestPayEntry payEntry = new ArCreditRetSaveRequestPayEntry();
        payEntry.setAmount(refund.getRefundPrice().toString());
        payEntry.setSettleAccountId("2203092555199972352"); // TODO 金蝶内部的
        payEntryList.add(payEntry);
        request.setPayEntryList(payEntryList);

        // 设置源单信息分录 - 必填字段
        OpenDocCodeMapping mapping = mappingRepository.queryByDocCode(orderNo, refund.getTenantId(), 1, 1);
        if (mapping == null || StringUtils.isEmpty(mapping.getOutCode())) {
            throw new ParamsException("订单映射不存在, orderNo: " + orderNo);
        }

        List<ArCreditRetSaveRequestSourceBillEntry> sourceBillEntryList = new ArrayList<>();
        ArCreditRetSaveRequestSourceBillEntry sourceBillEntry = new ArCreditRetSaveRequestSourceBillEntry();
        sourceBillEntry.setSrcBillId(mapping.getOutCode());
        sourceBillEntry.setSrcBillTypeId("sal_bill_inbound"); // TODO ?
        sourceBillEntryList.add(sourceBillEntry);
        request.setSourceBillEntryList(sourceBillEntryList);

        return request;
    }
}
