package com.cosfo.manage.jindie.service;

import com.kingdee.service.data.entity.MaterialUnitDetailRes;
import com.kingdee.service.data.entity.MaterialUnitListRes;
import com.kingdee.service.data.entity.UnitMaterialUnitListReq;

/**
 * 金蝶计量单位服务接口
 */
public interface JindieUnitService {

    /**
     * 获取物料计量单位详情
     *
     * @param id 物料计量单位ID
     * @return 物料计量单位详情
     */
    MaterialUnitDetailRes getMaterialUnitDetail(String id);

    /**
     * 获取物料计量单位列表
     *
     * @param request 查询请求
     * @return 物料计量单位列表
     */
    MaterialUnitListRes getMaterialUnitList(UnitMaterialUnitListReq request);
}
