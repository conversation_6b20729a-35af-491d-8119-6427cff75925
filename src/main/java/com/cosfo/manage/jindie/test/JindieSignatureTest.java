package com.cosfo.manage.jindie.test;

import com.cosfo.manage.jindie.config.JinDieConfig;
import com.kingdee.service.unit.SHAUtil;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 金蝶签名测试类
 */
public class JindieSignatureTest {

    public static void main(String[] args) {
        try {
            // 模拟配置
            String clientId = "310973";
            String clientSecret = "826f4ba70353d906ca9018f26777b045";
            String appKey = "CzZTCZkB";
            String appSecret = "b0fb816cec51627267e2e953b4b94f7b6ece8fd5";
            
            // 模拟请求参数
            long timestamp = System.currentTimeMillis();
            String nonce = "123456";
            
            // 生成签名
            String signature = generateSignature(timestamp, nonce, clientSecret);
            System.out.println("生成的签名: " + signature);
            
            // 生成app_signature
            String appSignature = SHAUtil.SHA256HMAC(appKey, appSecret);
            appSignature = Base64.getEncoder().encodeToString(appSignature.getBytes());
            System.out.println("生成的app_signature: " + appSignature);
            
            // 构建完整的请求URL
            String tokenUrl = "https://api.kingdee.com/jdyconnector/app_management/kingdee_auth_token";
            String fullUrl = tokenUrl + "?app_key=" + appKey + "&app_signature=" + URLEncoder.encode(appSignature, StandardCharsets.UTF_8.name());
            System.out.println("完整的请求URL: " + fullUrl);
            
            // 打印请求头
            System.out.println("请求头:");
            System.out.println("X-Api-ClientID: " + clientId);
            System.out.println("X-Api-Auth-Version: 2.0");
            System.out.println("X-Api-TimeStamp: " + timestamp);
            System.out.println("X-Api-Nonce: " + nonce);
            System.out.println("X-Api-SignHeaders: X-Api-TimeStamp,X-Api-Nonce");
            System.out.println("X-Api-Signature: " + signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 生成签名
     * 根据金蝶API文档实现X-Api-Signature签名算法
     *
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param clientSecret 客户端密钥
     * @return 签名
     */
    private static String generateSignature(long timestamp, String nonce, String clientSecret) {
        try {
            // 1. 准备签名原文
            // 请求方式（GET）
            String method = "GET";
            
            // 请求地址的path部分，进行URL编码
            String path = URLEncoder.encode("/jdyconnector/app_management/kingdee_auth_token", StandardCharsets.UTF_8.name());
            
            // 构建参数
            String appKey = "CzZTCZkB";
            String appSecret = "b0fb816cec51627267e2e953b4b94f7b6ece8fd5";
            String appSignature = SHAUtil.SHA256HMAC(appKey, appSecret);
            appSignature = Base64.getEncoder().encodeToString(appSignature.getBytes());
            String encodedAppSignature = URLEncoder.encode(URLEncoder.encode(appSignature, StandardCharsets.UTF_8.name()), StandardCharsets.UTF_8.name());
            String params = "app_key=" + appKey + "&app_signature=" + encodedAppSignature;
            
            // headers请求参数（只需要x-api-nonce、x-api-timestamp参与加密，必须小写）
            String headers = "x-api-nonce:" + nonce + "\n" + "x-api-timestamp:" + timestamp + "\n";
            
            // 拼接得到签名原文，每段签名字符拼接均需要换行符
            StringBuilder signatureStr = new StringBuilder()
                    .append(method).append("\n")
                    .append(path).append("\n")
                    .append(params).append("\n")
                    .append(headers);
            
            System.out.println("签名原文: \n" + signatureStr.toString());
            
            // 2. 使用HMAC-SHA256算法对签名字符串进行加密
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(clientSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secretKey);
            byte[] hash = sha256_HMAC.doFinal(signatureStr.toString().getBytes(StandardCharsets.UTF_8));
            
            // 3. 将加密结果转为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            // 4. 进行Base64编码
            return Base64.getEncoder().encodeToString(hexString.toString().getBytes(StandardCharsets.UTF_8));
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
