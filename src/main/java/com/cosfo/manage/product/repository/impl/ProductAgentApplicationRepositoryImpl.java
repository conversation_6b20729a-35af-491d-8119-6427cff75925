package com.cosfo.manage.product.repository.impl;

import com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplication;
import com.cosfo.manage.product.mapper.ProductAgentApplicationMapper;
import com.cosfo.manage.product.repository.ProductAgentApplicationRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 代仓商品申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Service
public class ProductAgentApplicationRepositoryImpl extends ServiceImpl<ProductAgentApplicationMapper, ProductAgentApplication> implements ProductAgentApplicationRepository {

    @Resource
    private ProductAgentApplicationMapper productAgentApplicationMapper;

    @Override
    public List<ProductAgentApplicationDTO> listAll(ProductAgentApplicationQueryDTO productAgentApplicationQueryDto) {
        return productAgentApplicationMapper.listAll(productAgentApplicationQueryDto);
    }
}
