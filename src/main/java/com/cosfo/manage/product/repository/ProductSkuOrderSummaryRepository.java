package com.cosfo.manage.product.repository;

import com.cosfo.manage.product.model.po.ProductSkuOrderSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * sku下单汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface ProductSkuOrderSummaryRepository extends IService<ProductSkuOrderSummary> {

    ProductSkuOrderSummary queryBySkuAndWarehouseNo(Long tenantId, String skucode, Integer warehouseNo, LocalDate orderDate);

    /**
     * 存在tenantId+sku+warehouseNo+order_time记录更新，不存在插入记录
     * @param list
     */
    void batchSaveOrUpdate(List<ProductSkuOrderSummary> list);

    /**
     * 查询 tenantId+sku+warehouseNo前x天的销量
     * @param tenantId
     * @param skucode
     * @param warehouseNo
     * @param saleDays
     * @return
     */
    Integer getSaleAmount(Long tenantId, String skucode, Integer warehouseNo, Integer saleDays);
}
