package com.cosfo.manage.product.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSkuUnitMultiple;
import com.cosfo.manage.product.mapper.ProductSkuUnitMultipleMapper;
import com.cosfo.manage.product.repository.ProductSkuUnitMultipleRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * sku 单位转换表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Service
public class ProductSkuUnitMultipleRepositoryImpl extends ServiceImpl<ProductSkuUnitMultipleMapper, ProductSkuUnitMultiple> implements ProductSkuUnitMultipleRepository {

    @Override
    public List<ProductSkuUnitMultiple> queryBySkuId(Long skuId, Long tenantId) {
        LambdaQueryWrapper<ProductSkuUnitMultiple> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductSkuUnitMultiple::getSkuId, skuId)
                .eq(ProductSkuUnitMultiple::getTenantId, tenantId);
        return list(lambdaQueryWrapper);
    }
}
