package com.cosfo.manage.product.convert;

import com.cosfo.manage.good.model.vo.ProductSkuVo;
import com.cosfo.manage.good.model.vo.ProductSpuDetailVO;
import com.cosfo.manage.good.model.vo.ProductSpuListVo;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductSpuDetailResp;
import net.summerfarm.goods.client.resp.ProductSpuListResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 18:24
 * @Description:
 */
@Mapper
public interface ProductFacadeConvert {
    ProductFacadeConvert INSTANCE = Mappers.getMapper(ProductFacadeConvert.class);

    @Mapping(source = "spuId", target = "id")
    @Mapping(source = "skuDetailList", target = "productSkuVoList")
    ProductSpuDetailVO convert2ProductSpuDetailVO(ProductSpuDetailResp data);

    @Mapping(source = "skuId", target = "id")
    @Mapping(source = "skuMapping.agentSkuId", target = "agentSkuId")
    @Mapping(source = "categoryName", target = "thirdCategory")
    @Mapping(source = "categoryId", target = "thirdCategoryId")
    ProductSkuDTO convert2ProductSKuDetailVO(ProductSkuDetailResp data);
    List<ProductSkuDTO> convert2ProductSkuDTOList(List<ProductSkuDetailResp> list);

    @Mapping(source = "skuId", target = "id")
    @Mapping(source = "associated", target = "havingRelated")
    ProductSkuVo convert2ProductSkuVo(ProductSkuDetailResp data);

    @Mapping(source = "spuId", target = "id")
    @Mapping(source = "skuDetailList", target = "productSkuVoList")
    ProductSpuListVo convert2ProductSpuListVo(ProductSpuListResp list);
    List<ProductSpuListVo> convert2ProductSpuListVos(List<ProductSpuListResp> list);

}
