package com.cosfo.manage.product.model.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/10 17:26
 */
@Data
public class ProductAgentSkuDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 代理供应商租户Id
     */
    private Long agentTenantId;

    /**
     * 代理skuId
     */
    private Long agentSkuId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    /**
     * 最大价格
     */
    private BigDecimal maxPrice;

    /**
     * 供应sku规格
     */
    private String specification;

    /**
     * 供应sku规格单位
     */
    private String specificationUnit;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 一级类目
     */
    private String firstCategoryName;

    /**
     * 二级类目
     */
    private String secondCategoryName;

    /**
     * 三级类目
     */
    private String thirdCategoryName;

    /**
     * 最低供应价
     */
    private BigDecimal minSupplyPrice;

    /**
     * 最高供应价
     */
    private BigDecimal maxSupplyPrice;

    /**
     * 供应城市数量
     */
    private Integer supplyCityCount;

    /**
     * 是否与商品关联 0 没关联 1 已关联
     */
    private Integer isAssociateProduct;

    /**
     * 0 非代仓商品 1 代仓商品
     */
    private Integer productType;

}
