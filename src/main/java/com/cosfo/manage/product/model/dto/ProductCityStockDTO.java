package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @Date: 2023-05-19
 * @Description:
 */
@Data
public class ProductCityStockDTO {

    private Long id;

    /**
     * saas侧skuId
     */
    private Long skuId;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市库存数
     */
    private Long quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
