package com.cosfo.manage.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-19
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentTenantSkuDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 代仓品skuId
     */
    private Long agentSkuId;
    /**
     * 代仓品skuCode
     */
    private String agentSkuCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市编号
     */
    private Long cityId;

    /**
     * 仓库ID列表
     */
    private List<Integer> warehouseNos;
}
