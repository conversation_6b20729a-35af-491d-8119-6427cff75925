package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 代仓商品申请item表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Getter
@Setter
@TableName("product_agent_application_item")
public class ProductAgentApplicationItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 申请id
     */
    @TableField("application_id")
    private Long applicationId;

    /**
     * 规格名称
     */
    @TableField("specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 体重
     */
    @TableField("weight_num")
    private BigDecimal weightNum;

    /**
     * 0 进口 1 国产
     */
    @TableField("domestic_flag")
    private Integer domesticFlag;

    /**
     * 审核状态 0、审核中 1、审核通过 2、审核拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 拒绝原因
     */
    @TableField("refuse_reason")
    private String refuseReason;

    /**
     * 供应商skuId
     */
    @TableField("supplier_sku_id")
    private Long supplierSkuId;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 体积
     */
    @TableField("volume")
    private String volume;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
