package com.cosfo.manage.product.model.dto;

import com.cosfo.manage.product.model.po.ProductCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 13:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductCategoryTreeDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 类目名
     */
    private String name;

    /**
     * 上级类目
     */
    private Long parentId;

    /**
     * 子类目
     */
    private List<ProductCategoryTreeDTO> childList;
}
