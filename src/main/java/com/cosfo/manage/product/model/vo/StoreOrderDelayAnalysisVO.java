package com.cosfo.manage.product.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author: xiaowk
 * @date: 2023/11/14 下午6:31
 */
@Data
public class StoreOrderDelayAnalysisVO implements Serializable {
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 门店编号
     */
    private String storeCode;
    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer storeType;

    private String storeTypeStr;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店 4、拉黑 5、注销
     */
    private Integer storeStatus;

    private String storeStatusStr;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 最后叫货日期
     */
    private LocalDate lastOrderDate;
    /**
     * 最后叫货单价
     */
    private BigDecimal lastOrderPrice;
    /**
     * 最后叫货数量
     */
    private Integer lastOrderQuantity;
    /**
     * 最后叫货金额
     */
    private BigDecimal lastOrderTotalPrice;
    /**
     * 滞叫天数
     */
    private Integer delayDays;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 店长手机号
     */
    private String phone;

}
