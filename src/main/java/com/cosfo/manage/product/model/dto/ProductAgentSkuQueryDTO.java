package com.cosfo.manage.product.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * 描述: 代仓sku查询
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/18
 */
@Data
public class ProductAgentSkuQueryDTO extends BasePageInput {
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 类目集合
     */
    private List<Long> categoryIds;
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * sku
     */
    private String sku;
    /**
     * sku_id
     */
    private Long skuId;


    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 省id
     */
    private Long provinceId;


    /**
     * 品牌
     */
    private String brandName;


    /**
     * 城市ids
     */
    private List<Long> cityIds;

    /**
     * 品牌ids
     */
    private List<Long> brandIds;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 是否关联标识
     */
    private String associated;
}
