package com.cosfo.manage.product.mapper;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
@Deprecated
public interface ProductAgentWarehouseMapper {
//    /**
//     * 删除
//     *
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * 新增
//     *
//     * @param record
//     * @return
//     */
//    int insert(ProductAgentWarehouse record);
//
//    /**
//     * 新增
//     *
//     * @param record
//     * @return
//     */
//    int insertSelective(ProductAgentWarehouse record);
//
//    /**
//     * 查询
//     *
//     * @param id
//     * @return
//     */
//    ProductAgentWarehouse selectByPrimaryKey(Long id);
//
//    /**
//     * 更新
//     *
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(ProductAgentWarehouse record);
//
//    /**
//     * 更新
//     *
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(ProductAgentWarehouse record);
//
//    /**
//     * 查询品牌方代仓商品使用仓
//     *
//     * @param tenantId
//     * @return
//     */
//    List<ProductAgentWarehouse> selectByTenantId(@Param("tenantId") Long tenantId);
//
//    /**
//     * 根据仓库编号查询
//     *
//     * @param tenantId
//     * @param warehouseNo
//     * @return
//     */
//    ProductAgentWarehouse selectByWarehouseNameAndTenantId(@Param("tenantId") Long tenantId, @Param("warehouseNo") Long warehouseNo);
}