package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.po.ProductBrandCategoryMapping;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ProductBrandCategoryMappingMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductBrandCategoryMapping record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductBrandCategoryMapping record);

    /**
     * 查询
     * @param id
     * @return
     */
    ProductBrandCategoryMapping selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductBrandCategoryMapping record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductBrandCategoryMapping record);

    /**
     * 根据品牌id和类目id查询
     * @param brandId
     * @param categoryId
     * @return
     */
    ProductBrandCategoryMapping queryByBrandAndCategory(@Param("brandId") Long brandId, @Param("categoryId") Long categoryId);
}
