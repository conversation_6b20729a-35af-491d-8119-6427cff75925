package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuQueryVO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import com.cosfo.manage.product.service.ProductAgentSkuService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/18
 */
@RestController
@RequestMapping("/productAgentSku")
public class ProductAgentSkuMappingController extends BaseController {

    @Resource
    private ProductAgentSkuService productAgentSkuService;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;

//    /**
//     * 查询所有支持代仓商品 - 新建商品时使用
//     * 弃用，请使用com.cosfo.manage.product.controller.ProductAgentSkuMappingController#listAll(com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO)
//     * @param pageIndex
//     * @param pageSize
//     * @return
//     */
//    @GetMapping("/listAll/{pageIndex}/{pageSize}")
//    @Deprecated
//    public ResultDTO listAll(@PathVariable Integer pageIndex, @PathVariable Integer pageSize,ProductAgentSkuQueryDTO productAgentSkuQueryDTO){
//        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
//        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId())) {
//            return ResultDTO.success (productAgentSkuService.listAll (pageIndex, pageSize, productAgentSkuQueryDTO, getMerchantInfoDTO ()));
//        }else {
//            return productAgentSkuService.listAllOld (pageIndex, pageSize, productAgentSkuQueryDTO, getMerchantInfoDTO ());
//        }
//    }

    /**
     * 查询所有支持代仓商品 - 新建商品时使用
     * @param productAgentSkuQueryDTO
     * @return
     */
    @PostMapping("/listAll")
    public ResultDTO<PageInfo<ProductAgentSkuVO>> listAllNew(ProductAgentSkuQueryDTO productAgentSkuQueryDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId())) {
            return ResultDTO.success (productAgentSkuService.listAll (productAgentSkuQueryDTO.getPageIndex(), productAgentSkuQueryDTO.getPageSize(), productAgentSkuQueryDTO, getMerchantInfoDTO ()));
        }else {
            return productAgentSkuService.listAllOld (productAgentSkuQueryDTO.getPageIndex(), productAgentSkuQueryDTO.getPageSize(), productAgentSkuQueryDTO, getMerchantInfoDTO ());
        }
    }

    /**
     * 代仓市场列表
     * @param queryVO
     * @return
     */
    @Deprecated
    @PostMapping("/query/distribution-market-list")
    public CommonResult<PageInfo<ProductAgentSkuDTO>> listDistributionMarket(@RequestBody ProductAgentSkuQueryVO queryVO) {
        ProductAgentSkuQueryDTO queryDTO = new ProductAgentSkuQueryDTO();
        BeanUtils.copyProperties(queryVO, queryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return productAgentSkuService.listDistributionMarket(queryDTO, loginContextInfoDTO);
    }
}
