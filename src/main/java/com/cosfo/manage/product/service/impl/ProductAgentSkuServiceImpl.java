package com.cosfo.manage.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.common.context.ProductSupplierSkuAssociateEnum;
import com.cosfo.manage.common.context.ProductTypeEnum;
import com.cosfo.manage.common.context.SkuAgentStatusEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.PriceUtil;
import com.cosfo.manage.facade.*;
import com.cosfo.manage.good.convert.ProductInfoConvert;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationRecordQueryConditionDTO;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.market.model.vo.CostPriceRangeVO;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.product.dao.ProductAgentSkuMappingDao;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductBrandMapper;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.model.dto.*;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.po.ProductBrand;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import com.cosfo.manage.product.service.ProductAgentSkuService;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.wms.saleinventory.dto.res.QueryAreaStoreQuantityResp;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceAreaResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceStorageResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/18
 */
@Slf4j
@Service
public class ProductAgentSkuServiceImpl implements ProductAgentSkuService {
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private ProductBrandMapper productBrandMapper;
    @Autowired
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private ProductAgentSkuMappingDao productAgentSkuMappingDao;
    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;
    @Resource
    private TenantService tenantService;
    @Value("${summerfarm.api-host}")
    private String apiHost;
    @Resource
    private SaasInventoryFacade saasInventoryFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private WarehouseStorageFenceQueryFacade warehouseStorageFenceQueryFacade;
    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Resource
    private PriceFacade priceFacade;
    @Resource
    private ProductFacade productFacade;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    @Override
    public ResultDTO<PageInfo<ProductAgentSkuVO>> listAllOld(Integer pageIndex, Integer pageSize, ProductAgentSkuQueryDTO productAgentSkuQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        productAgentSkuQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        // 查询类目集合
        if (productAgentSkuQueryDTO.getCategoryId() != null) {
            List<CategoryVO> categoryVOS = productCategoryService.queryChildCategoryList(productAgentSkuQueryDTO.getCategoryId());
            List<Long> categoryIds = categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
            productAgentSkuQueryDTO.setCategoryIds(categoryIds);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<ProductAgentSkuVO> productAgentSkuVOs = productAgentSkuMappingMapper.listAll(productAgentSkuQueryDTO);
        if (CollectionUtils.isEmpty(productAgentSkuVOs)) {
            return ResultDTO.success(PageInfoHelper.createPageInfo(new ArrayList<>(), pageSize));
        }

        // 查询库存
        List<Long> skuIds = productAgentSkuVOs.stream().map(ProductAgentSkuVO::getSkuId).collect(Collectors.toList());
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS = queryAgentProductWarehouseData(skuIds, loginContextInfoDTO.getTenantId());
        Map<Long, List<ProductAgentWarehouseDataVO>> map = new HashMap<>(NumberConstant.SIXTEEN);
        if (!CollectionUtils.isEmpty(productAgentWarehouseDataVOS)) {
            map = productAgentWarehouseDataVOS.stream().collect(Collectors.groupingBy(ProductAgentWarehouseDataVO::getSkuId));
        }

        productAgentSkuVOs.stream().filter(item -> ObjectUtil.isNotNull(item.getCategoryId())).forEach(item -> {
            // 查询类目
            CategoryVO thirdCategoryVO = productCategoryService.selectByCategoryId(item.getCategoryId());
            item.setThirdCategory (thirdCategoryVO.getName ());
            if (thirdCategoryVO.getParentId () != null && thirdCategoryVO.getParentId () != 0L) {
                CategoryVO secondCategoryVO = productCategoryService.selectByCategoryId (thirdCategoryVO.getParentId ());
                item.setSecondaryCategory (secondCategoryVO.getName ());
                if (secondCategoryVO.getParentId () != null && secondCategoryVO.getParentId () != 0L) {
                    CategoryVO firstCategoryVO = productCategoryService.selectByCategoryId (secondCategoryVO.getParentId ());
                    item.setCategory (firstCategoryVO.getName ());
                }
            }else{
                item.setCategory (thirdCategoryVO.getName ());
                if(CollectionUtil.isNotEmpty (thirdCategoryVO.getCategoryVOS ())) {
                    CategoryVO categoryVO = thirdCategoryVO.getCategoryVOS ().get (0);
                    item.setSecondaryCategory (categoryVO.getName ());
                    List<CategoryVO> categoryVOS = categoryVO.getCategoryVOS ();
                    if(CollectionUtil.isNotEmpty (categoryVOS)) {
                        item.setThirdCategory (categoryVOS.get (0).getName ());
                    }
                }
            }
        });
        //查询 供应价
        Map<Long, CostPriceRangeVO> skuPriceMap = new HashMap<> ();
        List<CostPriceRangeVO> costPriceRangeVOS = priceFacade.queryCostPriceRange (productAgentSkuVOs.stream ().map (ProductAgentSkuVO::getSupplySkuId).collect (Collectors.toSet ()), loginContextInfoDTO.getTenantId());
        Map<Long, Long> agentSkuMap = productAgentSkuVOs.stream ().collect (Collectors.toMap (ProductAgentSkuVO::getSupplySkuId, ProductAgentSkuVO::getSkuId));
        Map<Long, CostPriceRangeVO> skuCityCostPriceMap = costPriceRangeVOS.stream ().collect (Collectors.toMap (CostPriceRangeVO::getSkuId, Function.identity ()));
        skuCityCostPriceMap.forEach ((agentSkuId, costPriceRangeVO) -> {
            Long skuId = agentSkuMap.get (agentSkuId);
            skuPriceMap.put (skuId, costPriceRangeVO);
        });

        Map<Long, List<ProductAgentWarehouseDataVO>> finalMap = map;
        productAgentSkuVOs.forEach(item -> {
            if (finalMap.containsKey(item.getSkuId())) {
                item.setWarehouseDataVos(ProductInfoConvert.INSTANCE.convert2VOs(finalMap.get(item.getSkuId())));
            }
            if(skuPriceMap.containsKey (item.getSkuId())){
                CostPriceRangeVO costPriceRangeVO = skuPriceMap.get (item.getSkuId());
                BigDecimal supplyMaxPrice = costPriceRangeVO.getMaxPrice ();
                BigDecimal supplyMinPrice = costPriceRangeVO.getMinPrice ();
                item.setMinPrice(supplyMinPrice);
                item.setMaxPrice(supplyMaxPrice);
                item.setPriceStr(PriceUtil.buildPriceRange(supplyMaxPrice,supplyMinPrice));
            }
        });

        return ResultDTO.success(PageInfoHelper.createPageInfo(productAgentSkuVOs, pageSize));
    }
    @Override
    public PageInfo<ProductAgentSkuVO> listAll(Integer pageIndex, Integer pageSize, ProductAgentSkuQueryDTO productAgentSkuQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        productAgentSkuQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        ProductQueryInput productQueryInput = new ProductQueryInput ();
        productQueryInput.setPageSize (pageSize);
        productQueryInput.setPageIndex (pageIndex);
        productQueryInput.setPageNum (pageIndex);
        productQueryInput.setTitle (productAgentSkuQueryDTO.getTitle ());
        productQueryInput.setCategoryId (productAgentSkuQueryDTO.getCategoryId ());
        if (Objects.nonNull(productAgentSkuQueryDTO.getSkuId())){
            productQueryInput.setSkuIds(Collections.singletonList(productAgentSkuQueryDTO.getSkuId()));
        }

        PageInfo<ProductSkuDTO> pageInfo = productFacade.pageSku (productQueryInput, loginContextInfoDTO.getTenantId ());
        List<ProductSkuDTO> list = pageInfo.getList ();
        if (CollectionUtils.isEmpty(list)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), pageSize);
        }
        // 查询库存
        List<Long> skuIds = list.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS = queryAgentProductWarehouseData(skuIds, loginContextInfoDTO.getTenantId());
        Map<Long, List<ProductAgentWarehouseDataVO>> map = new HashMap<>(NumberConstant.SIXTEEN);
        if (!CollectionUtils.isEmpty(productAgentWarehouseDataVOS)) {
            map = productAgentWarehouseDataVOS.stream().collect(Collectors.groupingBy(ProductAgentWarehouseDataVO::getSkuId));
        }

        //查询 供应价
        List<CostPriceRangeVO> costPriceRangeVOS = priceFacade.queryCostPriceRange (new HashSet<> (skuIds), loginContextInfoDTO.getTenantId());
        Map<Long, CostPriceRangeVO> skuCityCostPriceMap = costPriceRangeVOS.stream ().collect (Collectors.toMap (CostPriceRangeVO::getSkuId, Function.identity ()));

        // 查询代仓申请记录
        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setSkuIds(skuIds);
        productAgentApplicationRecordQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        Map<Long, List<ProductAgentApplicationRecord>> agentApplicationRecordMap = productAgentApplicationRecords.stream().collect(Collectors.groupingBy(ProductAgentApplicationRecord::getSkuId));


        //拼接结果
        List<ProductAgentSkuVO> resultList = new ArrayList<> (pageSize);
        Map<Long, List<ProductAgentWarehouseDataVO>> finalMap = map;
        list.forEach(item -> {
            Long id = item.getId ();//ft id
            ProductAgentSkuVO productAgentSkuVO = new ProductAgentSkuVO ();
            BeanUtil.copyProperties (item,productAgentSkuVO);
            productAgentSkuVO.setCategory(item.getFirstCategory());
            productAgentSkuVO.setSecondaryCategory (item.getSecondCategory());
            productAgentSkuVO.setThirdCategory(item.getThirdCategory());
            productAgentSkuVO.setSkuId (id);
            Long agentSkuId = null;
            if(ObjectUtil.isNotEmpty (item.getSkuMapping ())){
                agentSkuId = item.getSkuMapping ().getAgentSkuId ();//xm id
                productAgentSkuVO.setId (item.getSkuMapping ().getId ());
            }
            productAgentSkuVO.setSupplySkuId (agentSkuId);
            if (finalMap.containsKey(id)) {
                productAgentSkuVO.setWarehouseDataVos(ProductInfoConvert.INSTANCE.convert2VOs(finalMap.get(id)));
            }
            if(ObjectUtil.isNotEmpty (agentSkuId) && skuCityCostPriceMap.containsKey (agentSkuId)){
                CostPriceRangeVO costPriceRangeVO = skuCityCostPriceMap.get (agentSkuId);
                BigDecimal supplyMaxPrice = costPriceRangeVO.getMaxPrice ();
                BigDecimal supplyMinPrice = costPriceRangeVO.getMinPrice ();
                productAgentSkuVO.setMinPrice(supplyMinPrice);
                productAgentSkuVO.setMaxPrice(supplyMaxPrice);
                productAgentSkuVO.setPriceStr(PriceUtil.buildPriceRange(supplyMaxPrice,supplyMinPrice));
            }
            // 设置代仓申请状态
            productAgentSkuVO.setAgentStatus (getAgentApplicationRecordStatus(id, agentApplicationRecordMap));

            resultList.add (productAgentSkuVO);
        });
        PageInfo result = pageInfo;
        result.setList (resultList);
        return result;
    }

    private Integer getAgentApplicationRecordStatus(Long skuId, Map<Long, List<ProductAgentApplicationRecord>> listMap){
        Integer agentStatus = SkuAgentStatusEnum.UN_APPLY.getCode();
        if(skuId == null || CollectionUtils.isEmpty(listMap)){
            return agentStatus;
        }
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = listMap.get(skuId);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            agentStatus = productAgentApplicationRecords.get(0).getStatus();
        }
        return agentStatus;
    }

    @Override
    @Deprecated
    public CommonResult<PageInfo<ProductAgentSkuDTO>> listDistributionMarket(ProductAgentSkuQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 处理查询条件
        Long tenantId = loginContextInfoDTO.getTenantId();
        queryDTO.setTenantId(tenantId);
        if (Objects.nonNull(queryDTO.getCategoryId())) {
            List<CategoryVO> categoryVOS = productCategoryService.queryChildCategoryList(queryDTO.getCategoryId());
            List<Long> childrenCategoryIds = categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
            queryDTO.setCategoryIds(childrenCategoryIds);
        }
        if (Objects.nonNull(queryDTO.getBrandName())) {
            List<ProductBrand> brandList = productBrandMapper.fuzzyByName(queryDTO.getBrandName());
            queryDTO.setBrandIds(CollectionUtils.isEmpty(brandList) ? null : brandList.stream().map(ProductBrand::getId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(queryDTO.getBrandIds())) {
                return CommonResult.ok();
            }
        }

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<ProductAgentSkuDTO> skuDTOList = productAgentSkuMappingMapper.queryAll(queryDTO);
        for (ProductAgentSkuDTO productAgentSkuDTO : skuDTOList) {
            Long agentSkuId = productAgentSkuDTO.getAgentSkuId();
            ProductSkuDTO productSku = productSkuService.querySkuInfo(agentSkuId);
            productAgentSkuDTO.setSpecification(productSku.getSpecification());
            productAgentSkuDTO.setSpecificationUnit(productSku.getSpecificationUnit());
            productAgentSkuDTO.setMainPicture(productSku.getMainPicture());
            productAgentSkuDTO.setSubTitle(productSku.getSubTitle());
            productAgentSkuDTO.setTitle(productSku.getTitle());
            productAgentSkuDTO.setProductType(ProductTypeEnum.AGENT.getType());

            // 类目层级
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(productSku.getCategoryId());
            productAgentSkuDTO.setFirstCategoryName(categoryDTO.getFirstCategoryName());
            productAgentSkuDTO.setSecondCategoryName(categoryDTO.getSecondCategoryName());
            productAgentSkuDTO.setThirdCategoryName(categoryDTO.getThirdCategoryName());

            productAgentSkuDTO.setBrandName(productSku.getBrandName());

            // 是否已经关联
            productAgentSkuDTO.setIsAssociateProduct(Objects.isNull(productAgentSkuDTO.getSkuId()) ? ProductSupplierSkuAssociateEnum.NOT_ASSOCIATE.getType() : ProductSupplierSkuAssociateEnum.HAS_ASSOCIATE.getType());
        }

        return CommonResult.ok(PageInfoHelper.createPageInfo(skuDTOList, queryDTO.getPageSize()));
    }

    @Override
    public List<ProductSkuDTO> queryTenantAgentSku(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDto, LoginContextInfoDTO loginContextInfoDTO) {
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
            // 查询所有映射的代仓品
            List<ProductsMappingResp> productAgentSkuMappings = productFacade.listProductMappingByTenantId (loginContextInfoDTO.getTenantId ());
            if (CollectionUtils.isEmpty (productAgentSkuMappings)) {
                return new ArrayList<> ();
            }

            List<Long> skuIds = productAgentSkuMappings.stream ().map (ProductsMappingResp::getSkuId).collect (Collectors.toList ());
            // 若上下架状态不为空，查询出满足条件的skuId，进行过滤
            Integer onSale = productAgentWarehouseDateQueryDto.getOnSale ();
            if (Objects.nonNull (onSale)) {
                Map<Long, List<MarketItemOnSaleSimple4StoreResp>> skuMap = marketItemBusinessService.selectSaleMapBySkuIds (loginContextInfoDTO.getTenantId (),skuIds);
                if (OnSaleTypeEnum.ON_SALE.getCode ().equals (onSale)) {
                    List<Long> onSaleSkuIds = skuMap.entrySet ().stream ().filter (entry ->
                            entry.getValue ().stream ().anyMatch (resp -> OnSaleTypeEnum.ON_SALE.getCode ().equals (resp.getOnSale ()))
                    ).map (Map.Entry::getKey).collect (Collectors.toList ());
                    skuIds = onSaleSkuIds;
                }
                if (OnSaleTypeEnum.SOLD_OUT.getCode ().equals (onSale)) {
                    List<Long> soldOutSkuIds = skuMap.entrySet ().stream ().filter (entry ->
                            entry.getValue ().stream ().allMatch (resp -> OnSaleTypeEnum.SOLD_OUT.getCode ().equals (resp.getOnSale ()))
                    ).map (Map.Entry::getKey).collect (Collectors.toList ());
                    skuIds = soldOutSkuIds;
                }
                if(CollectionUtil.isEmpty (skuIds)){
                    return Collections.emptyList ();
                }else{
                    if(ObjectUtil.isNotNull (productAgentWarehouseDateQueryDto.getSkuId ())){
                        if(!skuIds.contains (productAgentWarehouseDateQueryDto.getSkuId ())) {
                            return Collections.emptyList ();
                        }else{
                            skuIds = Collections.singletonList (productAgentWarehouseDateQueryDto.getSkuId ());
                        }
                    }
                }
            }else{
                if(ObjectUtil.isNotNull (productAgentWarehouseDateQueryDto.getSkuId ())) {
                    skuIds = Collections.singletonList (productAgentWarehouseDateQueryDto.getSkuId ());
                }
            }
            ProductQueryInput productQueryInput = new ProductQueryInput();
            productQueryInput.setSkuIds (skuIds);
            productQueryInput.setTitle (productAgentWarehouseDateQueryDto.getTitle ());
            return productFacade.listSku (productQueryInput,loginContextInfoDTO.getTenantId ());
        }else {
            // 查询所有映射的代仓品
            List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.selectHavingMappingByTenantId (loginContextInfoDTO.getTenantId ());
            if (CollectionUtils.isEmpty (productAgentSkuMappings)) {
                return new ArrayList<> ();
            }

            List<Long> skuIds = productAgentSkuMappings.stream ().map (ProductAgentSkuMapping::getSkuId).collect (Collectors.toList ());

            // 若上下架状态不为空，查询出满足条件的skuId，进行过滤
            Integer onSale = productAgentWarehouseDateQueryDto.getOnSale ();
            if (Objects.nonNull (onSale)) {
                Map<Long, List<MarketItemOnSaleSimple4StoreResp>> skuMap = marketItemBusinessService.selectSaleMapBySkuIds (loginContextInfoDTO.getTenantId (), skuIds);

                if (OnSaleTypeEnum.ON_SALE.getCode ().equals (onSale)) {
                    List<Long> onSaleSkuIds = skuMap.entrySet ().stream ().filter (entry ->
                            entry.getValue ().stream ().anyMatch (resp -> OnSaleTypeEnum.ON_SALE.getCode ().equals (resp.getOnSale ()))
                    ).map (Map.Entry::getKey).collect (Collectors.toList ());
                    skuIds = onSaleSkuIds;
                }
                if (OnSaleTypeEnum.SOLD_OUT.getCode ().equals (onSale)) {
                    List<Long> soldOutSkuIds = skuMap.entrySet ().stream ().filter (entry ->
                            entry.getValue ().stream ().allMatch (resp -> OnSaleTypeEnum.SOLD_OUT.getCode ().equals (resp.getOnSale ()))
                    ).map (Map.Entry::getKey).collect (Collectors.toList ());
                    skuIds = soldOutSkuIds;
                }
                if(CollectionUtil.isEmpty (skuIds)){
                    return Collections.emptyList ();
                }
            }

            productAgentWarehouseDateQueryDto.setSkuIds (skuIds);
            // 查询商品信息
            List<ProductSkuDTO> productSkuDtos = productSkuMapper.selectAgentSkuByTenantId (productAgentWarehouseDateQueryDto, loginContextInfoDTO.getTenantId ());
            Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap = productAgentSkuMappings.stream ().collect (Collectors.toMap (ProductAgentSkuMapping::getSkuId, item -> item));
            if (CollectionUtils.isEmpty (productSkuDtos)) {
                return new ArrayList<> ();
            }

            productSkuDtos.forEach (productSkuDto -> {
                productSkuDto.setAgentSkuId (productAgentSkuMappingMap.get (productSkuDto.getId ()).getAgentSkuId ());
                productSkuDto.setAgentSkuCode (productAgentSkuMappingMap.get (productSkuDto.getId ()).getAgentSkuCode ());
            });
            return productSkuDtos;
        }
    }


    @Override
    public List<ProductAgentWarehouseDataVO> queryAgentProductWarehouseData(List<Long> skuIds, Long tenantId) {
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = new SummerfarmAgentSkuWarehouseDataInput();
        summerfarmAgentSkuWarehouseDataInput.setPageNum(NumberConstant.ONE);
        summerfarmAgentSkuWarehouseDataInput.setPageSize(NumberConstant.THOUSAND);
        // 查询代仓货品关联鲜沐品SkuId
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingDao.selectBySkuIds(skuIds, tenantId);
        if(CollectionUtils.isEmpty(productAgentSkuMappings)){
            return new ArrayList<>();
        }

        Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, item -> item));
        List<Long> agentSkuIds = productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getAgentSkuId).collect(Collectors.toList());
        summerfarmAgentSkuWarehouseDataInput.setSkuId(agentSkuIds);
        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(tenantId);
        summerfarmAgentSkuWarehouseDataInput.setAdminId(tenantDTO.getAdminId());
        // 查询品牌方仓库信息 包含自营仓和代仓
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, null, null);
        if (CollectionUtils.isEmpty(warehouseStorageResps)) {
            return new ArrayList<>();
        }

        List<Long> warehouseNos = warehouseStorageResps.stream().map(warehouseStorageResp -> {
            Long warehouseNo = Long.valueOf(warehouseStorageResp.getWarehouseNo());
            return warehouseNo;
        }).collect(Collectors.toList());
        summerfarmAgentSkuWarehouseDataInput.setWarehouseIds(warehouseNos);
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = new ArrayList<>(NumberConstant.TEN);
        // 查询鲜沐代仓实时库存
        try {
            log.info("查询代仓品实时库存请求参数：{}", JSONObject.toJSONString(summerfarmAgentSkuWarehouseDataInput));
            PageInfo<QueryAreaStoreQuantityResp> pageInfo = saasInventoryFacade.pageQueryAgentSkuWarehouseData(summerfarmAgentSkuWarehouseDataInput);
            log.info("查询代仓品实时库存返回数据：{}", pageInfo);
            if (Objects.isNull(pageInfo)) {
                return productAgentWarehouseDataVos;
            }

            List<QueryAreaStoreQuantityResp> summerFarmAgentSkuWarehouseDataResps = pageInfo.getList();
            // 按照sku分组
            Map<Long, List<QueryAreaStoreQuantityResp>> map = summerFarmAgentSkuWarehouseDataResps.stream().collect(Collectors.groupingBy(QueryAreaStoreQuantityResp::getSkuId));
            // 查询sku基于仓维度的覆盖区域
            List<WarehouseSkuFenceResp> warehouseSkuFenceResps = queryWarehouseSkuFence(map, productAgentSkuMappingMap);
            // 转化为map
            Map<String, List<WarehouseSkuFenceStorageResp>> warehouseSkuFenceStorageRespListMap = warehouseSkuFenceResps.stream().collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages));

            // 组装数据
            List<ProductAgentWarehouseDataVO> agentWarehouseDataVos = summerFarmAgentSkuWarehouseDataResps.stream().map(summerfarmAgentSkuWarehouseDataDTO -> {
                ProductAgentWarehouseDataVO productAgentWarehouseDataVo = new ProductAgentWarehouseDataVO();
                BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataDTO, productAgentWarehouseDataVo);
                ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(summerfarmAgentSkuWarehouseDataDTO.getSkuId());
                productAgentWarehouseDataVo.setSkuId(productAgentSkuMapping.getSkuId());
                productAgentWarehouseDataVo.setRoadQuantity(Objects.isNull(summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity()) ? NumberConstant.ZERO : summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity());
                // 获取省市区
                List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = warehouseSkuFenceStorageRespListMap.get(productAgentSkuMapping.getAgentSkuCode());
                if (!CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
                    for (WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp : warehouseSkuFenceStorageResps) {
                        if (summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo().equals(warehouseSkuFenceStorageResp.getWarehouseNo())) {
                            // 获取仓库服务商
                            if(WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())){
                                productAgentWarehouseDataVo.setWarehouseProvider(tenantDTO.getCompanyName());
                            }else {
                                productAgentWarehouseDataVo.setWarehouseProvider("杭州鲜沐科技有限公司");
                            }

                            List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreas = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas();
                            if (!CollectionUtils.isEmpty(warehouseSkuFenceAreas)) {
                                List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceAreas.stream().map(warehouseSkuFenceAreaResp -> {
                                    WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                                    warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                                    warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                                    warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                                    return warehouseSkuFenceAreaDTO;
                                }).collect(Collectors.toList());
                                productAgentWarehouseDataVo.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                                productAgentWarehouseDataVo.setCityNum(warehouseSkuFenceAreaDTOList.size());
                            }
                        }
                    }
                }

                return productAgentWarehouseDataVo;
            }).collect(Collectors.toList());
            productAgentWarehouseDataVos = agentWarehouseDataVos;
        } catch (Exception e) {
            log.error("查询代仓品实时库存数据失败", e);
        }

        return productAgentWarehouseDataVos;
    }

    /**
     * 查询sku基于仓维度的覆盖区域
     *
     * @param map
     * @param productAgentSkuMappingMap
     * @return
     */
    private List<WarehouseSkuFenceResp> queryWarehouseSkuFence(Map<Long, List<QueryAreaStoreQuantityResp>> map, Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap){
        if(CollectionUtils.isEmpty(map)){
            return new ArrayList<>();
        }

        Set<Long> agentSkuIds = map.keySet();

        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = agentSkuIds.stream().map(agentSkuId -> {
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(agentSkuId);
            List<QueryAreaStoreQuantityResp> summerFarmAgentSkuWarehouseDataResps = map.get(agentSkuId);
            List<Integer> warehouseNos = summerFarmAgentSkuWarehouseDataResps.stream().map(QueryAreaStoreQuantityResp::getWarehouseNo).collect(Collectors.toList());
            WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
            warehouseSkuFenceReq.setSku(productAgentSkuMapping.getAgentSkuCode());
            warehouseSkuFenceReq.setWarehouseNos(warehouseNos);
            return warehouseSkuFenceReq;
        }).collect(Collectors.toList());

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(warehouseSkuFenceReqs);
        return warehouseSkuFenceResps;
    }
}
