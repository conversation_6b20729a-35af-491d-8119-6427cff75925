package com.cosfo.manage.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.manage.common.constant.*;
import com.cosfo.manage.common.context.ProductAgentItemStatusEnum;
import com.cosfo.manage.common.context.ProductSkuEnum;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationRecordQueryConditionDTO;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.product.convert.ProductConvert;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.mapper.ProductSpuMapper;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.dto.SummerFarmSynchronizedSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.model.vo.SummerFarmSynchronizedSkuVO;
import com.cosfo.manage.product.repository.ProductSkuRepository;
import com.cosfo.manage.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.summerfarm.model.dto.SummerFarmSkuMsgDTO;
import com.cosfo.summerfarm.model.input.SummerfarmSkuInput;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.saas.req.SummerFarmSynchronizedSkuReq;
import net.summerfarm.manage.client.saas.resp.SummerFarmSynchronizedSkuResp;
import net.xianmu.common.exception.BizException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description product 业务层
 * @date 2022/5/12 15:27
 */
@Slf4j
@Service
public class ProductSkuServiceImpl implements ProductSkuService {

    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private ProductSpuMapper productSpuMapper;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @Resource
    private ProductSkuRepository productSkuRepository;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;
    @Resource
    private ProductFacade productFacade;

    @Resource
    private MarketFacade marketFacade;

//    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//    @Override
//    public ResultDTO<Long> saveSku(ProductSkuDTO productSkuDTO, LoginContextInfoDTO contextInfoDTO, Boolean isCheckPrice) {
//        if(isCheckPrice) {
//            Assert.isTrue(!CollectionUtils.isEmpty(productSkuDTO.getMarketAreaItemMappingVos()), ResultDTOEnum.PRICE_NOT_NULL.getCode(), ResultDTOEnum.PRICE_NOT_NULL.getMessage());
//            checkSkuPrice(productSkuDTO.getMarketAreaItemMappingVos(), productSkuDTO.getPriceType());
//        }
//
//        MarketClassification classification = marketClassificationService.selectByPrimaryKey(productSkuDTO.getClassificationId());
//        if (Objects.nonNull(classification) && Objects.equals(classification.getParentId(), Constants.DEFAULT_CLASSIFICATION_ID)) {
//            return ResultDTO.fail(ResultDTOEnum.CLASSIFICATION_ERROR);
//        }
//
//        Long tenantId = contextInfoDTO.getTenantId();
//        Long spuId = productSkuDTO.getSpuId();
//        ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(spuId);
//
//        // 1、新建货品sku
//        ProductSku productSku = ProductSku.builder().spuId(productSkuDTO.getSpuId()).tenantId(tenantId).specification(productSkuDTO.getSpecification()).specificationUnit(productSkuDTO.getSpecificationUnit()).build();
//        productSkuMapper.insert(productSku);
//
//        // 2、新建商品item
//        Long skuId = productSku.getId();
//        MarketItem item = MarketItem.builder().tenantId(tenantId).skuId(skuId).specification(productSku.getSpecification()).specificationUnit(productSku.getSpecificationUnit()).build();
//        marketItemService.insert(item);
//
//        // 3、供价
//        if (Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal())) {
//            productSkuDTO.setId(skuId);
//            productSkuDTO.setTenantId(tenantId);
//            if (DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//                // 三方配送
//                ProductAgentSkuMapping productAgentSkuMapping = new ProductAgentSkuMapping();
//                productAgentSkuMapping.setId(productSkuDTO.getAgentId());
//                productAgentSkuMapping.setSkuId(skuId);
//                productAgentSkuMappingMapper.updateByPrimaryKeySelective(productAgentSkuMapping);
//            }
//
//            generateCostSupply(productSkuDTO);
//        } else {
//            //关联第三方报价单供价
//            Long supplyId = productSkuDTO.getSupplyId();
//            associatedQuotationSupply(skuId, supplyId);
//        }
//
//        // 4、销售策略（定价、上下架、库存）
//        MarketAreaItem areaItem = MarketAreaItem.builder().tenantId(tenantId).skuId(skuId).itemId(item.getId()).onSale(productSkuDTO.getOnSale()).warehouseType(productSkuDTO.getWarehouseType()).deliveryType(productSkuDTO.getDeliveryType()).miniOrderQuantity(productSkuDTO.getMiniOrderQuantity()).priceType(productSkuDTO.getPriceType()).build();
//        marketAreaItemService.insert(areaItem);
//
//        // 如果是自营仓，并且是品牌仓配送
//        if (Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal()) && DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//            Stock stock = Stock.builder().tenantId(tenantId).skuId(skuId).amount(productSkuDTO.getAmount()).build();
//            stockService.insert(stock);
//            StockRecord record = StockRecord.builder().tenantId(tenantId).stockSkuId(skuId).type(StockRecordType.SAVE_NEW.getType()).beforeAmount(NumberConstants.ZERO).changeAmount(productSkuDTO.getAmount()).afterAmount(productSkuDTO.getAmount()).build();
//            stockRecordService.insert(record);
//        }
//
//        // 5、定价策略配置
//        if (!CollectionUtils.isEmpty(productSkuDTO.getMarketAreaItemMappingVos())) {
//            List<MarketAreaItemMappingVO> marketAreaItemMappingVos = productSkuDTO.getMarketAreaItemMappingVos();
//            List<MarketAreaItemMappingDTO> marketAreaItemMappingDtos = marketAreaItemMappingVos.stream().map(marketAreaItemMappingVo -> {
//                MarketAreaItemMappingDTO marketAreaItemMappingDto = new MarketAreaItemMappingDTO();
//                BeanUtils.copyProperties(marketAreaItemMappingVo, marketAreaItemMappingDto);
//                return marketAreaItemMappingDto;
//            }).collect(Collectors.toList());
//            marketAreaItemMappingService.save(marketAreaItemMappingDtos, tenantId, areaItem.getId());
//        }
//
//        // 6、分类关系映射
//        if (Objects.nonNull(productSkuDTO.getClassificationId())) {
//            MarketItemClassification marketItemClassification = MarketItemClassification.builder().tenantId(tenantId).itemId(item.getId()).classificationId(productSkuDTO.getClassificationId()).build();
//            marketItemClassificationService.insert(marketItemClassification);
//        }
//
//        return ResultDTO.success(skuId);
//    }

    /**
     * 校验sku定价策略
     *
     * @param marketAreaItemMappingVos
     */
    /*private void checkSkuPrice(List<MarketAreaItemMappingVO> marketAreaItemMappingVos, Integer priceType) {
        MarketAreaItemPriceTypeEnum priceTypeEnum = MarketAreaItemPriceTypeEnum.getPriceTypeEnum(priceType);
        switch (priceTypeEnum) {
            case ALL_STORE_UNIFIED_PRICE:
                if (marketAreaItemMappingVos.size() != NumberConstant.ONE && !StorePriceTypeEnum.ALL.getCode().equals(marketAreaItemMappingVos.get(NumberConstant.ZERO).getStorePriceType())) {
                    throw new DefaultServiceException(ResultDTOEnum.ALL_STORE_UNIFIED_PRICE_ERROR.getCode(), ResultDTOEnum.ALL_STORE_UNIFIED_PRICE_ERROR.getMessage());
                }

                break;
            case ALL_STORE_DIFFERENCES_PRICE:
                boolean containsAllPrice = marketAreaItemMappingVos.stream().anyMatch(item -> StorePriceTypeEnum.ALL.getCode().equals(item.getStorePriceType()));
                boolean containsOtherPrice = marketAreaItemMappingVos.stream().anyMatch(item -> StorePriceTypeEnum.OTHER.getCode().equals(item.getStorePriceType()));
                if (!containsAllPrice || !containsOtherPrice) {
                    throw new DefaultServiceException(ResultDTOEnum.ALL_STORE_DIFFERENCES_PRICE_ERROR.getCode(), ResultDTOEnum.ALL_STORE_DIFFERENCES_PRICE_ERROR.getMessage());
                }

                break;
            case PART_STORE_DIFFERENCES_PRICE:
                if (marketAreaItemMappingVos.size() > NumberConstant.ZERO && !marketAreaItemMappingVos.stream().allMatch(item -> StorePriceTypeEnum.OTHER.getCode().equals(item.getStorePriceType()))) {
                    throw new DefaultServiceException(ResultDTOEnum.PART_STORE_DIFFERENCES_PRICE_ERROR.getCode(), ResultDTOEnum.PART_STORE_DIFFERENCES_PRICE_ERROR.getMessage());
                }
                break;

            case ERROR:
                throw new DefaultServiceException(MarketAreaItemPriceTypeEnum.ERROR.getDesc());
        }
    }*/

//    /**
//     * 生成成本供价
//     *
//     * @param skuDTO
//     */
//    private void generateCostSupply(ProductSkuDTO skuDTO) {
//        ProductPricingSupply supply = ProductPricingSupply.builder().tenantId(skuDTO.getTenantId()).supplyTenantId(skuDTO.getTenantId()).build();
//        productPricingSupplyMapper.insert(supply);
//        // 价格信息
//        ProductPricingSupplyCityMapping productPricingSupplyCityMapping = new ProductPricingSupplyCityMapping();
//        productPricingSupplyCityMapping.setPrice(skuDTO.getPrice());
//        productPricingSupplyCityMapping.setType(ProductSkuEnum.PriceType.SPECIFIED_PRICE.ordinal());
//        productPricingSupplyCityMapping.setSupplyType(ProductSkuEnum.SupplyType.COST_SUPPLY.ordinal());
//        productPricingSupplyCityMappingMapper.insertSelective(productPricingSupplyCityMapping);
//    }

//    /**
//     * 关联第三方报价
//     *
//     * @param skuId
//     * @param supplyId
//     */
//    private void associatedQuotationSupply(Long skuId, Long supplyId) {
//        ProductPricingSupply update = ProductPricingSupply.builder().id(supplyId).skuId(skuId).build();
//        productPricingSupplyMapper.updateByPrimaryKeySelective(update);
//    }

    /*@Override
    public ResultDTO changOnSale(ProductSkuDTO productSkuDTO) {
        Long skuId = productSkuDTO.getId();
        MarketAreaItem areaItem = marketAreaItemService.selectByTenantAndSkuId(productSkuDTO.getTenantId(), skuId);
        List<MarketAreaItemMapping> marketAreaItemMappings = marketAreaItemMappingService.selectByAreaItemId(areaItem.getTenantId(), areaItem.getId());
        if (Objects.equals(OnSaleTypeEnum.ON_SALE.getCode(), productSkuDTO.getOnSale()) && CollectionUtils.isEmpty(marketAreaItemMappings)) {
            return ResultDTO.fail("请填写价格，再进行上架");
        }
        MarketItem marketItem = marketItemService.selectBySkuId(productSkuDTO.getTenantId(), skuId);
        if (Objects.isNull(marketItem)) {
            return ResultDTO.fail("未找到货品");
        }

        // 如果是三方仓sku并且修改上架，判断报价单是否有效
        if (productSkuDTO.getOnSale().equals(OnSaleTypeEnum.ON_SALE.getCode()) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItem.getGoodsType())) {
            // 判断报价单是否删除，如果删除，请重新关联报价单
            ProductPricingSupplyDTO productPricingSupplyDTO = productPricingSupplyMapper.selectByTenantAndSkuId(productSkuDTO.getTenantId(), skuId);
            if (Objects.isNull(productPricingSupplyDTO)) {
                return ResultDTO.fail(ResultDTOEnum.PRICING_SUPPLY_FAILURE.getCode(), ResultDTOEnum.PRICING_SUPPLY_FAILURE.getMessage());
            }
        }

        MarketAreaItem update = MarketAreaItem.builder().id(areaItem.getId()).onSale(productSkuDTO.getOnSale()).build();
        marketAreaItemService.updateByPrimaryKeySelective(update);
        return ResultDTO.success();
    }*/

//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public ResultDTO updateSku(ProductSkuDTO productSkuDTO, LoginContextInfoDTO contextInfoDTO) {
//        MarketClassification classification = marketClassificationService.selectByPrimaryKey(productSkuDTO.getClassificationId());
//        if (Objects.equals(classification.getParentId(), Constants.DEFAULT_CLASSIFICATION_ID)) {
//            return ResultDTO.fail(ResultDTOEnum.CLASSIFICATION_ERROR);
//        }
//
//        checkSkuPrice(productSkuDTO.getMarketAreaItemMappingVos(), productSkuDTO.getPriceType());
//        if (Objects.equals(productSkuDTO.getOnSale(), OnSaleTypeEnum.ON_SALE.getCode()) && CollectionUtils.isEmpty(productSkuDTO.getMarketAreaItemMappingVos())) {
//            return ResultDTO.fail("请填写价格，再进行上架");
//        }
//
//        List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList = productSkuDTO.getMarketAreaItemMappingDtoList();
//        List<Long> storeIds = new ArrayList<>();
//        marketAreaItemMappingDtoList.forEach(marketAreaItemMappingDto -> {
//            if (storeIds.contains(marketAreaItemMappingDto.getStoreIds())) {
//                throw new DefaultServiceException(ResultDTOEnum.PRICE_STRATEGY_STORE_NOT_REPEAT.getCode(), ResultDTOEnum.PRICE_STRATEGY_STORE_NOT_REPEAT.getMessage());
//            }
//
//            storeIds.addAll(marketAreaItemMappingDto.getStoreIds());
//        });
//
//        Long tenantId = contextInfoDTO.getTenantId();
//        productSkuDTO.setTenantId(contextInfoDTO.getTenantId());
//        Long skuId = productSkuDTO.getId();
//
//        // 1、更新货品sku信息
//        ProductSku productSku = ProductSku.builder().id(skuId).specification(productSkuDTO.getSpecification()).specificationUnit(productSkuDTO.getSpecificationUnit()).build();
//        productSkuMapper.updateByPrimaryKeySelective(productSku);
//
//        // 2、更新商品item
//        MarketItem marketItem = marketItemService.selectBySkuId(tenantId, skuId);
//        MarketItem item = MarketItem.builder().id(marketItem.getId()).skuId(skuId).specification(productSku.getSpecification()).specificationUnit(productSku.getSpecificationUnit()).build();
//        marketItemService.updateByPrimaryKeySelective(item);
//
//        // 3、销售策略（定价、上下架、库存）
//        MarketAreaItem areaItem = marketAreaItemService.selectByTenantAndSkuId(tenantId, skuId);
//        MarketAreaItem update = MarketAreaItem.builder().id(areaItem.getId()).priceType(productSkuDTO.getPriceType()).onSale(productSkuDTO.getOnSale()).warehouseType(productSkuDTO.getWarehouseType()).deliveryType(productSkuDTO.getDeliveryType()).miniOrderQuantity(productSkuDTO.getMiniOrderQuantity()).build();
//        marketAreaItemService.updateByPrimaryKeySelective(update);
//        Stock stock = stockService.selectBySkuId(tenantId, skuId);
//
//        // 4、定价策略配置
//        ProductPricingSupplyDTO supply = productPricingSupplyMapper.selectByTenantAndSkuId(tenantId, skuId);
//        // 自营改三方
//        if (Objects.equals(areaItem.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal()) && Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.THIRD.ordinal())) {
//            // 删除原先的成本报价数据
//            productPricingSupplyMapper.deleteByPrimaryKey(supply.getId());
//            // 删除城市报价表数据
//            productPricingSupplyCityMappingMapper.deleteByProductPricingSupplyId(supply.getId());
//            //关联报价单数据
//            associatedQuotationSupply(skuId, productSkuDTO.getSupplyId());
//        }
//
//        if (Objects.equals(areaItem.getWarehouseType(), ProductSkuEnum.WarehouseType.THIRD.ordinal()) && Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal())) {
//            //将原有报价解绑
//            productPricingSupplyMapper.unbindSupply(supply.getId());
//            //新增成本供价
//            generateCostSupply(productSkuDTO);
//            // 品牌方配送
//            if (DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//                // 查询是否已经有库存信息，如果有库存累加, 没有新建
//                if (stock != null) {
//                    stock.setAmount(stock.getAmount() + productSkuDTO.getAmount());
//                    stockService.update(stock);
//                } else {
//                    Stock insert = Stock.builder().tenantId(tenantId).skuId(skuId).amount(productSkuDTO.getAmount()).build();
//                    stockService.insert(insert);
//                    StockRecord stockRecord = StockRecord.builder().tenantId(tenantId).stockSkuId(skuId).type(StockRecordType.THIRD_TO_SELF.getType()).beforeAmount(NumberConstants.ZERO).changeAmount(productSkuDTO.getChangeQuantity()).afterAmount(productSkuDTO.getChangeQuantity()).build();
//                    stockRecordService.insert(stockRecord);
//                }
//                // 三方配送
//            } else if (DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//                // 绑定代理关系
//                ProductAgentSkuMapping productAgentSkuMapping = new ProductAgentSkuMapping();
//                productAgentSkuMapping.setId(productSkuDTO.getAgentId());
//                productAgentSkuMapping.setSkuId(skuId);
//                productAgentSkuMappingMapper.updateByPrimaryKeySelective(productAgentSkuMapping);
//            }
//        }
//        // 解绑原先的代仓品
//        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(areaItem.getWarehouseType()) && DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(areaItem.getDeliveryType())) {
//            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndSkuId(productSkuDTO.getTenantId(), productSkuDTO.getId());
//            if (productAgentSkuMapping != null) {
//                productAgentSkuMapping.setSkuId(null);
//                productAgentSkuMappingMapper.updateByPrimaryKey(productAgentSkuMapping);
//            }
//        }
//
//        // 映射新的代仓品
//        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(productSkuDTO.getWarehouseType()) && DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//            // 三方配送
//            ProductAgentSkuMapping productAgentSkuMapping = new ProductAgentSkuMapping();
//            productAgentSkuMapping.setId(productSkuDTO.getAgentId());
//            productAgentSkuMapping.setSkuId(skuId);
//            productAgentSkuMappingMapper.updateByPrimaryKeySelective(productAgentSkuMapping);
//        }
//
//        if (Objects.equals(areaItem.getWarehouseType(), ProductSkuEnum.WarehouseType.THIRD.ordinal()) && Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.THIRD.ordinal())) {
//            if (!Objects.isNull(supply) && !Objects.equals(supply.getId(), productSkuDTO.getSupplyId())) {
//                //解绑
//                productPricingSupplyMapper.unbindSupply(supply.getId());
//                // 关联
//                associatedQuotationSupply(skuId, productSkuDTO.getSupplyId());
//            }
//        }
//
//        // 原先自营 三方配送改成自营品牌配送
//        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(areaItem.getWarehouseType()) && DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(areaItem.getDeliveryType()) && DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(productSkuDTO.getDeliveryType())) {
//            // 查询是否有库存
//            if (stock != null) {
//                stock.setAmount(stock.getAmount() + productSkuDTO.getAmount());
//                stockService.update(stock);
//            } else {
//                Stock insert = Stock.builder().tenantId(tenantId).amount(productSkuDTO.getAmount()).build();
//                stockService.insert(insert);
//                StockRecord stockRecord = StockRecord.builder().tenantId(tenantId).stockSkuId(skuId).type(StockRecordType.THIRD_TO_SELF.getType()).beforeAmount(NumberConstants.ZERO).changeAmount(productSkuDTO.getChangeQuantity()).afterAmount(productSkuDTO.getChangeQuantity()).build();
//                stockRecordService.insert(stockRecord);
//            }
//        }
//
//        if (Objects.equals(areaItem.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal()) && Objects.equals(productSkuDTO.getWarehouseType(), ProductSkuEnum.WarehouseType.SELF.ordinal()) && !Objects.equals(Integer.valueOf(Constants.ZERO.intValue()), productSkuDTO.getChangeQuantity())) {
//            // 减少库存不能少于现有库存数量
//            Stock realStock = stockService.selectBySkuId(tenantId, productSkuDTO.getId());
//            if (NumberUtil.add(realStock.getAmount(), productSkuDTO.getChangeQuantity()).compareTo(BigDecimal.ZERO) < 0) {
//                throw new DefaultServiceException("减少库存数量不能大于现有库存: " + realStock.getAmount());
//            }
//
//            //更换自营库存
//            Stock updateStock = Stock.builder().id(stock.getId()).amount(productSkuDTO.getChangeQuantity()).build();
//            stockService.update(updateStock);
//
//            StockRecord stockRecord = StockRecord.builder().tenantId(tenantId).stockSkuId(skuId).type(StockRecordType.MANUALLY_ADJUST.getType()).beforeAmount(stock.getAmount()).changeAmount(productSkuDTO.getChangeQuantity()).afterAmount(stock.getAmount() + productSkuDTO.getChangeQuantity()).build();
//            stockRecordService.insert(stockRecord);
//        }
//
//        // 5、分类关系映射
//        MarketItemClassification itemClassification = marketItemClassificationService.selectByItemId(tenantId, item.getId());
//        if (Objects.isNull(itemClassification)) {
//            //建立分类关系映射
//            MarketItemClassification insert = MarketItemClassification.builder().tenantId(tenantId).itemId(item.getId()).classificationId(productSkuDTO.getClassificationId()).build();
//            marketItemClassificationService.insert(insert);
//        } else {
//            MarketItemClassification updateClassification = MarketItemClassification.builder().id(itemClassification.getId()).classificationId(productSkuDTO.getClassificationId()).build();
//            marketItemClassificationService.updateByPrimaryKey(updateClassification);
//        }
//
//        // 5、定价策略配置
//        if (!CollectionUtils.isEmpty(productSkuDTO.getMarketAreaItemMappingVos())) {
//            List<MarketAreaItemMappingVO> marketAreaItemMappingVos = productSkuDTO.getMarketAreaItemMappingVos();
//            List<MarketAreaItemMappingDTO> marketAreaItemMappingDtos = marketAreaItemMappingVos.stream().map(marketAreaItemMappingVo -> {
//                MarketAreaItemMappingDTO marketAreaItemMappingDto = new MarketAreaItemMappingDTO();
//                BeanUtils.copyProperties(marketAreaItemMappingVo, marketAreaItemMappingDto);
//                return marketAreaItemMappingDto;
//            }).collect(Collectors.toList());
//
//            marketAreaItemMappingService.update(marketAreaItemMappingDtos, tenantId, areaItem.getId());
//        }
//
//        return ResultDTO.success();
//    }

    @Override
    public List<ProductSkuDTO> querySupplySkuInfo(List<Long> skuIds) {
        List<ProductSku> productSkus = productSkuMapper.selectBatchByPrimaryKey(skuIds);
        if (CollectionUtils.isEmpty(productSkus)) {
            return Collections.emptyList();
        }
        List<Long> spuIds = productSkus.stream().map(ProductSku::getSpuId).collect(Collectors.toList());
        List<ProductSpu> spus = productSpuMapper.selectBatchByPrimaryKey(spuIds);
        Map<Long, ProductSpu> spuMap = spus.stream().collect(Collectors.toMap(ProductSpu::getId, Function.identity()));
        Set<Long> categoryIds = spus.stream()
                .map(ProductSpu::getCategoryId)
                .collect(Collectors.toSet());
        Map<Long, ProductCategoryDTO> productCategoryDTOMap = productCategoryService.batchQueryWholeCategory(categoryIds);
        return productSkus.stream().map(e -> {
            ProductSkuDTO productSkuDTO = ProductConvert.INSTANCE.convert2Dto(e);
            ProductSpu spu = spuMap.get(e.getSpuId());
            productSkuDTO.setTitle(spu.getTitle());
            productSkuDTO.setBrandName (spu.getBrandName ());
            productSkuDTO.setMainPicture(spu.getMainPicture());
            productSkuDTO.setStorageTemperature(spu.getStorageTemperature());
            productSkuDTO.setStorageLocation(spu.getStorageLocation());
            productSkuDTO.setCategoryId(spu.getCategoryId());
            ProductCategoryDTO productCategoryDTO = Optional.ofNullable(productCategoryDTOMap.get(spu.getCategoryId())).orElse(new ProductCategoryDTO());
            productSkuDTO.setFirstCategoryId(productCategoryDTO.getFirstCategoryId());
            productSkuDTO.setSecondCategoryId(productCategoryDTO.getSecondCategoryId());
            productSkuDTO.setThirdCategoryId(productCategoryDTO.getThirdCategoryId());
            return productSkuDTO;
        }).collect(Collectors.toList());
    }

    /*@Override
    public List<ProductSkuDTO> querySkuMallPrice(List<ProductSku> skuList, Long tenantId) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        List<Long> skuIds = skuList.stream().map(ProductSku::getId).collect(Collectors.toList());
        // 查询sku售卖信息
        List<MarketAreaItemDTO> marketAreaItemDTOS = marketItemService.queryMarketAreaItemInfo(skuIds, tenantId);
        Map<Long, MarketAreaItemDTO> marketAreaItemDTOMap = marketAreaItemDTOS.stream().collect(Collectors.toMap(MarketAreaItemDTO::getSkuId, item -> item));
        // 查詢报价单信息
        List<ProductPricingSupplyDTO> productPricingSupplyDTOS = productPricingSupplyMapper.batchQueryByTenantAndSkuIds(tenantId, skuIds);
        Map<Long, ProductPricingSupplyDTO> productPricingSupplyDTOMap = productPricingSupplyDTOS.stream().collect(Collectors.toMap(ProductPricingSupplyDTO::getSkuId, item -> item));
        List<Long> supplySkuIds = productPricingSupplyDTOS.stream().map(ProductPricingSupplyDTO::getSupplySkuId).collect(Collectors.toList());
        // 查询供应商品报价价格
        List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyService.querySkuSupplyPrice(supplySkuIds, tenantId);
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> map = new HashMap(NumberConstant.SIXTEEN);
        Iterator<ProductPricingSupplyCityMappingDTO> iterator = productPricingSupplyCityMappingDTOS.iterator();
        while (iterator.hasNext()) {
            ProductPricingSupplyCityMappingDTO productPricingSupplyCityMappingDTO = iterator.next();
            if (map.containsKey(productPricingSupplyCityMappingDTO.getProductPricingSupplyId())) {
                List<ProductPricingSupplyCityMappingDTO> cityMappings = map.get(productPricingSupplyCityMappingDTO.getProductPricingSupplyId());
                cityMappings.add(productPricingSupplyCityMappingDTO);
                map.put(productPricingSupplyCityMappingDTO.getProductPricingSupplyId(), cityMappings);
            } else {
                List<ProductPricingSupplyCityMappingDTO> cityMappings = new ArrayList<>(10);
                cityMappings.add(productPricingSupplyCityMappingDTO);
                map.put(productPricingSupplyCityMappingDTO.getProductPricingSupplyId(), cityMappings);
            }
        }

        // 计算sku售卖价
        List<ProductSkuDTO> productSkuDtos = calculateSkuPrice(skuList, marketAreaItemDTOMap, productPricingSupplyDTOMap, tenantId, map);
        return productSkuDtos;
    }*/

    /**
     * 计算sku售卖价
     *
     * @return
     */
    /*private List<ProductSkuDTO> calculateSkuPrice(List<ProductSku> skuList, Map<Long, MarketAreaItemDTO> marketAreaItemDTOMap, Map<Long, ProductPricingSupplyDTO> productPricingSupplyDTOMap, Long tenantId, Map<Long, List<ProductPricingSupplyCityMappingDTO>> map) {
        List<ProductSkuDTO> productSkuDTOS = new ArrayList<>(NumberConstant.TEN);
        productSkuDTOS = skuList.stream().map(productSku -> {
            ProductSkuDTO productSkuDto = new ProductSkuDTO();
            BeanUtils.copyProperties(productSku, productSkuDto);
            MarketAreaItemDTO marketAreaItemDTO = marketAreaItemDTOMap.get(productSku.getId());
            // 查询价格策略
            List<MarketAreaItemMappingVO> marketAreaItemMappingVos = marketAreaItemMappingService.queryByAreaItemId(tenantId, marketAreaItemDTO.getId(), productSku.getId());
            // 如果价格策略为空，返回
            AtomicReference<BigDecimal> minPrice = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> maxPrice = new AtomicReference<>(BigDecimal.ZERO);
            if (CollectionUtils.isEmpty(marketAreaItemMappingVos)) {
                productSkuDto.setMinPrice(minPrice.get());
                productSkuDto.setMaxPrice(maxPrice.get());
                productSkuDto.setPrice(BigDecimal.ZERO);
                productSkuDto.setPriceStr(Constants.RUNG);
                productSkuDto.setMarketAreaItemMappingVos(marketAreaItemMappingVos);
            }

            // 自营和代仓品
            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(marketAreaItemDTO.getWarehouseType())) {
                // 价格策略
                for (MarketAreaItemMappingVO marketAreaItemMappingVo : marketAreaItemMappingVos) {
                    // 代仓费用加价
                    BigDecimal price = marketAreaItemMappingVo.getMappingNumber();
                    if (Objects.equals(DeliveryTypeEnum.THIRD_DELIVERY.getCode(), marketAreaItemDTO.getDeliveryType())) {
                        price = calculateProductAgentSkuFee(tenantId, marketAreaItemMappingVo.getMappingNumber(), marketAreaItemDTO.getMiniOrderQuantity());
                    }
                    marketAreaItemMappingVo.setMinPrice(price);
                    marketAreaItemMappingVo.setMaxPrice(price);
                    // 计算最小价最大价
                    minPrice.set(minPrice.get().compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? price : NumberUtil.min(price, minPrice.get()));
                    maxPrice.set(maxPrice.get().compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? price : NumberUtil.max(price, maxPrice.get()));
                }

                String priceStr = minPrice.get().compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? Constants.RUNG : minPrice.get().compareTo(maxPrice.get()) == NumberConstant.ZERO ? maxPrice.get().toString() : minPrice.get().toString() + Constants.RUNG + maxPrice.get().toString();
                productSkuDto.setMinPrice(minPrice.get());
                productSkuDto.setMaxPrice(maxPrice.get());
                productSkuDto.setPriceStr(priceStr);
                productSkuDto.setMarketAreaItemMappingVos(marketAreaItemMappingVos);
                return productSkuDto;
                // 供应商品
            } else if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(marketAreaItemDTO.getWarehouseType())) {
                // 查询商品是否有报价单
                ProductPricingSupplyDTO productPricingSupplyDTO = productPricingSupplyDTOMap.get(productSku.getId());
                if (Objects.isNull(productPricingSupplyDTO)) {
                    productSkuDto.setMinPrice(BigDecimal.ZERO);
                    productSkuDto.setMaxPrice(BigDecimal.ZERO);
                    productSkuDto.setPrice(BigDecimal.ZERO);
                    productSkuDto.setPriceStr(Constants.RUNG);
                    return productSkuDto;
                }

                // 获取供应商品的对应城市报价
                List<ProductPricingSupplyCityMappingDTO> pricingSupplyCityMappingDtos = map.get(productPricingSupplyDTO.getId());
                calculateSkuSupplyPrice(productPricingSupplyDTO, pricingSupplyCityMappingDtos);
                // 最小报价价格
                BigDecimal minSupplyPrice = productPricingSupplyDTO.getMinSupplyPrice();
                // 最大报价价格
                BigDecimal maxSupplyPrice = productPricingSupplyDTO.getMaxSupplyPrice();
                BigDecimal lowestPrice = BigDecimal.ZERO;
                BigDecimal highestPrice = BigDecimal.ZERO;
                // 价格策略
                for (MarketAreaItemMappingVO marketAreaItemMappingVo : marketAreaItemMappingVos) {
                    // 固定价
                    if (AreaItemTypeEnum.FIXED_PRICE.getCode().equals(marketAreaItemMappingVo.getType())) {
                        marketAreaItemMappingVo.setMinPrice(marketAreaItemMappingVo.getMappingNumber());
                        marketAreaItemMappingVo.setMaxPrice(marketAreaItemMappingVo.getMappingNumber());
                        // 计算最小价最大价
                        // 计算最小价最大价
                        lowestPrice = lowestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? marketAreaItemMappingVo.getMappingNumber() : NumberUtil.min(marketAreaItemMappingVo.getMappingNumber(), lowestPrice);
                        highestPrice = highestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? marketAreaItemMappingVo.getMappingNumber() : NumberUtil.max(marketAreaItemMappingVo.getMappingNumber(), highestPrice);
                        // 百分比上浮 或 固定额上涨
                    } else {

                        // 商城价按照报价单百分比上浮
                        if (AreaItemTypeEnum.PERCENTAGE.getCode().equals(marketAreaItemMappingVo.getType())) {
                            // 价格策略
                            BigDecimal itemMinPrice = NumberUtil.div(NumberUtil.mul(minSupplyPrice, NumberUtil.add(Constants.ONE_HUNDRED, marketAreaItemMappingVo.getMappingNumber())), Constants.ONE_HUNDRED, 2);
                            BigDecimal itemMaxPrice = NumberUtil.div(NumberUtil.mul(maxSupplyPrice, NumberUtil.add(Constants.ONE_HUNDRED, marketAreaItemMappingVo.getMappingNumber())), Constants.ONE_HUNDRED, 2);

                            marketAreaItemMappingVo.setMinPrice(itemMinPrice);
                            marketAreaItemMappingVo.setMaxPrice(itemMaxPrice);
                            // 计算最小价最大价
                            lowestPrice = lowestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? itemMinPrice : NumberUtil.min(itemMinPrice, lowestPrice);
                            highestPrice = highestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? itemMaxPrice : NumberUtil.max(itemMaxPrice, highestPrice);
                            // 商城价按照报价单固定价上浮
                        } else if (AreaItemTypeEnum.QUOTA.getCode().equals(marketAreaItemMappingVo.getType())) {
                            // 价格策略
                            BigDecimal itemMinPrice = NumberUtil.add(minSupplyPrice, marketAreaItemMappingVo.getMappingNumber());
                            BigDecimal itemMaxPrice = NumberUtil.add(maxSupplyPrice, marketAreaItemMappingVo.getMappingNumber());
                            marketAreaItemMappingVo.setMinPrice(itemMinPrice);
                            marketAreaItemMappingVo.setMaxPrice(itemMaxPrice);
                            // 计算最小价最大价
                            lowestPrice = lowestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? itemMinPrice : NumberUtil.min(itemMinPrice, lowestPrice);
                            highestPrice = highestPrice.compareTo(BigDecimal.ZERO) == NumberConstant.ZERO ? itemMaxPrice : NumberUtil.max(itemMaxPrice, highestPrice);
                        }
                    }
                }

                productSkuDto.setSupplyDTO(productPricingSupplyDTO);
                productSkuDto.setMarketAreaItemMappingVos(marketAreaItemMappingVos);
                productSkuDto.setPriceStr(Objects.isNull(lowestPrice) ? Constants.RUNG : lowestPrice.toString() + Constants.RUNG + highestPrice.toString());
                productSkuDto.setMinPrice(lowestPrice);
                productSkuDto.setMaxPrice(highestPrice);
            }

            return productSkuDto;
        }).collect(Collectors.toList());

        return productSkuDTOS;
    }*/

//    private BigDecimal calculateProductAgentSkuFee(Long tenantId, BigDecimal price, Integer itemCount) {
//        ProductAgentSkuFeeDTO productAgentSkuFeeDTO = productAgentSkuFeeRuleService.buildAgentSkuFeeRuleList(tenantId, price);
//        if (Objects.isNull(productAgentSkuFeeDTO)) {
//            return price;
//        }
//        // 按比例金额
//        if (Objects.equals(productAgentSkuFeeDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
//            return productAgentSkuFeeDTO.getAfterPercentPrice();
//        }
//        // 按件数金额
//        List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList = productAgentSkuFeeDTO.getCountRuleDTOList();
//        Optional<ProductAgentSkuFeeCountRuleDTO> ruleOptional = countRuleDTOList.stream().sorted(Comparator.comparing(ProductAgentSkuFeeCountRuleDTO::getCount).reversed()).filter(el -> el.getCount() <= itemCount).findFirst();
//        // 没有找到对应的加价规则区间
//        if (Objects.isNull(ruleOptional) || !ruleOptional.isPresent()) {
//            return price;
//        }
//        ProductAgentSkuFeeCountRuleDTO productAgentSkuFeeCountRuleDTO = ruleOptional.get();
//        return productAgentSkuFeeCountRuleDTO.getPrice();
//    }
    @Override
    public ProductSkuDTO querySkuInfo(Long skuId) {
        if (Objects.isNull(skuId)) {
            return null;
        }
        ProductSkuDTO productSkuDTO = productSkuMapper.querySkuInfo(skuId);
        if (Objects.isNull(productSkuDTO)) {
            return null;
        }
        // 类目
        ProductCategoryDTO productCategoryDTO = productCategoryService.selectWholeCategory(productSkuDTO.getCategoryId());
        productSkuDTO.setFirstCategoryId(productCategoryDTO.getFirstCategoryId());
        productSkuDTO.setSecondCategoryId(productCategoryDTO.getSecondCategoryId());
        productSkuDTO.setThirdCategoryId(productCategoryDTO.getThirdCategoryId());
        productSkuDTO.setFirstCategory(productCategoryDTO.getFirstCategoryName());
        productSkuDTO.setSecondCategory(productCategoryDTO.getSecondCategoryName());
        productSkuDTO.setThirdCategory(productCategoryDTO.getThirdCategoryName());
        return productSkuDTO;
    }

    /*@Override
    public MarketClassificationDTO queryClassification(Long tenantId, Long id) {
        MarketClassificationDTO marketClassificationDTO = new MarketClassificationDTO();
        MarketItem marketItem = marketItemService.selectBySkuId(tenantId, id);
        log.error("tenantId:" + tenantId + "  sku:" + JSONObject.toJSONString(marketItem));
        MarketItemClassification itemClassification = marketItemClassificationService.selectByItemId(tenantId, marketItem.getId());
        if (Objects.isNull(itemClassification)) {
            marketClassificationDTO.setName(StringConstants.EMPTY);
            marketClassificationDTO.setFirstClassificationName(StringConstants.EMPTY);
            marketClassificationDTO.setClassificationStr(StringConstants.EMPTY);
            return marketClassificationDTO;
        }
        log.error("itemClassification:" + JSONObject.toJSONString(itemClassification));
        MarketClassification sendCondClassification = marketClassificationService.selectByPrimaryKey(itemClassification.getClassificationId());
        if (Objects.isNull(sendCondClassification)) {
            marketClassificationDTO.setName(StringConstants.EMPTY);
            marketClassificationDTO.setFirstClassificationName(StringConstants.EMPTY);
            marketClassificationDTO.setClassificationStr(StringConstants.EMPTY);
            return marketClassificationDTO;
        }
        log.error("sendCondClassification:" + JSONObject.toJSONString(sendCondClassification));
        MarketClassification firstClassification = marketClassificationService.selectByPrimaryKey(sendCondClassification.getParentId());

        marketClassificationDTO.setName(sendCondClassification.getName());
        marketClassificationDTO.setFirstClassificationName(firstClassification.getName());
        marketClassificationDTO.setClassificationStr(firstClassification.getName() + StringConstants.LEFT_SLASH + sendCondClassification.getName());
        return marketClassificationDTO;
    }*/

    /*@Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult deleteStorePrice(ProductSkuPriceDTO productSkuPriceDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询城市售卖信息
        MarketAreaItemMapping marketAreaItemMapping = marketAreaItemMappingService.selectById(productSkuPriceDTO.getMarketAreaItemMappingId());
        // 删除门店策略和关联门店
        marketAreaItemMappingService.deleteByPrimaryKey(productSkuPriceDTO.getMarketAreaItemMappingId(), loginContextInfoDTO.getTenantId());
        // 是否唯一价格策略
        List<MarketAreaItemMapping> marketAreaItemMappings = marketAreaItemMappingService.selectByAreaItemId(loginContextInfoDTO.getTenantId(), marketAreaItemMapping.getAreaItemId());
        if (CollectionUtils.isEmpty(marketAreaItemMappings)) {
            // 商品下架
            MarketAreaItem marketAreaItem = new MarketAreaItem();
            marketAreaItem.setId(marketAreaItemMapping.getAreaItemId());
            marketAreaItem.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
            marketAreaItemService.updateByPrimaryKeySelective(marketAreaItem);
        }

        return CommonResult.ok();
    }*/

    /*@Override
    public CommonResult<ProductSkuDTO> queryDetail(ProductSkuDTO productSkuDto, LoginContextInfoDTO loginContextInfoDto) {
        ProductSkuDTO skuDto = productSkuMapper.querySkuInfo(productSkuDto.getId());
        ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(skuDto.getSpuId());
        MarketAreaItem marketAreaItem = marketAreaItemService.selectByTenantAndSkuId(loginContextInfoDto.getTenantId(), productSkuDto.getId());
        ProductAgentSkuFeeRule productAgentSkuFeeRule = productAgentSkuFeeRuleService.queryByTenantId(loginContextInfoDto.getTenantId());
        skuDto.setPriceType(marketAreaItem.getPriceType());
        skuDto.setOnSale(marketAreaItem.getOnSale());
        skuDto.setMiniOrderQuantity(marketAreaItem.getMiniOrderQuantity());
        skuDto.setAutomaticIncreasePriceFlag(productAgentSkuFeeRule.getAutomaticIncreasePriceFlag());
        List<ProductSkuDTO> productSkuDtos = Arrays.asList(skuDto);
        productSpuService.assemblySkuInfo(productSkuDtos, productSpu);
        return CommonResult.ok(skuDto);
    }*/

    @Override
    public void updateAssociated(Long skuId, Long tenantId, Integer associated) {
        productSkuMapper.updateAssociated(skuId, tenantId, associated);
    }

//    @Override
//    public Map<Long, SummerfarmProductInfoDTO> callSummerfarmQuerySkuInfo(List<Long> supplyIds) {
//        Map<Long, SummerfarmProductInfoDTO> summerfarmProductInfoDTOMap = new HashMap<>(NumberConstants.TEN);
//        try {
//            List<SummerfarmProductInfoDTO> summerfarmProductInfoDTOList = summerFarmInterfaceServiceFacade.batchQuerySkuInfo(supplyIds);
//            if (CollectionUtils.isEmpty(summerfarmProductInfoDTOList)) {
//                return Collections.emptyMap();
//            }
//            return summerfarmProductInfoDTOList.stream().collect(Collectors.toMap(SummerfarmProductInfoDTO::getSkuId, item -> item));
//        } catch (Exception e) {
//            log.error("调用鲜沐rpc查询商品接口失败", e);
//        }
//        return summerfarmProductInfoDTOMap;
//    }

    @Override
    public void receiveSummerfarmProductInfo(SummerFarmSkuMsgDTO summerFarmSkuMsgDTO) {
        if (Objects.isNull(summerFarmSkuMsgDTO)) {
            log.error("接收到的商品信息为空，流程结束");
            return;
        }
        Long agentSkuId = summerFarmSkuMsgDTO.getSkuId();
        if (Objects.isNull(agentSkuId)) {
            log.error("接收到的商品ID信息为空，流程结束");
            return;
        }
        BigDecimal weightNum = summerFarmSkuMsgDTO.getWeightNum();
        String volume = summerFarmSkuMsgDTO.getVolume();
        if (Objects.isNull(weightNum) && Objects.isNull(volume)) {
            log.error("接收到的商品信息为空，流程结束");
            return;
        }
        List<ProductAgentSkuMapping> mappings = productAgentSkuMappingMapper.batchQueryByAgentSkuId(Arrays.asList(agentSkuId));
        if (CollectionUtils.isEmpty(mappings)) {
            log.error("未查询到对应的映射关系");
            return;
        }
        ProductAgentSkuMapping productAgentSkuMapping = mappings.get(NumberConstant.ZERO);
        Long skuId = productAgentSkuMapping.getSkuId();
        ProductSku update = new ProductSku();
        update.setId(skuId);
        if (Objects.nonNull(weightNum)) {
            update.setWeight(weightNum.doubleValue());
        }
        if (Objects.nonNull(volume)) {
            update.setVolume(volume);
        }
        productSkuRepository.updateById(update);
        log.info("接收商品信息完毕");
    }

    @Override
    public List<ProductSkuDTO> queryByConditionOld(ProductSkuQueryConditionDTO productSkuQueryConditionDTO) {
        if (Objects.nonNull(productSkuQueryConditionDTO.getCategoryId())) {
            // 查询全部三级类目
            List<Long> categoryIds = productCategoryService.queryChildCategoryIdsOld (productSkuQueryConditionDTO.getCategoryId());
            if (CollectionUtils.isEmpty(categoryIds)) {
                return new ArrayList<>();
            }

            productSkuQueryConditionDTO.setCategoryIds(categoryIds);
        }

        List<ProductSkuDTO> productSkuDTOS = productSkuMapper.listByCondition(productSkuQueryConditionDTO);
        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            return new ArrayList<>();
        }

//        Map<Long, List<Long>> skuMap;
//        Map<Long, SummerfarmProductInfoDTO> summerfarmProductInfoDTOMap;
//
//        Set<Long> skuIds = productSkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toSet());
//        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.batchQueryBySkuIds (new ArrayList<>(skuIds));
//
//        if(CollectionUtil.isNotEmpty (productAgentSkuMappings)) {
//            skuMap = productAgentSkuMappings.stream().collect(Collectors.groupingBy(ProductAgentSkuMapping::getSkuId, Collectors.mapping(ProductAgentSkuMapping::getAgentSkuId, Collectors.toList())));
//            // 调用鲜沐rpc接口查询商品信息
//            summerfarmProductInfoDTOMap = callSummerfarmQuerySkuInfo (productAgentSkuMappings.stream ().map (ProductAgentSkuMapping::getAgentSkuId).collect (Collectors.toList ()));
//        } else {
//            skuMap = Collections.emptyMap ();
//            summerfarmProductInfoDTOMap = Collections.emptyMap ();
//        }

        List<Long> skuIdList = productSkuDTOS.stream().filter(e -> XianmuSupplyTenant.TENANT_ID.equals(e.getTenantId())).map(ProductSkuDTO::getId).distinct().collect(Collectors.toList());
        Map<Long, MarketItemInfoResp> marketItemMap = marketFacade.queryQuotationMarketItemMap(skuIdList);

        // 查询完整类目
        Set<Long> categorySet = productSkuDTOS.stream().map(ProductSkuDTO::getCategoryId).collect(Collectors.toSet());
        Map<Long, ProductCategoryDTO> longProductCategoryDTOMap = productCategoryService.batchQueryWholeCategory(new ArrayList<>(categorySet));

        productSkuDTOS.forEach(productSkuDTO -> {
            productSkuDTO.setMaxAfterSaleAmount(NumberConstants.ONE);
            productSkuDTO.setAfterSaleUnit(StringConstants.DEFAULT_AFTER_SALE_UNIT);

            if (!Objects.equals(productSkuDTO.getTenantId(), XianmuSupplyTenant.TENANT_ID)) {
                productSkuDTO.setAfterSaleUnit(productSkuDTO.getSpecificationUnit());
            } else {
                MarketItemInfoResp marketItemInfoResp = marketItemMap.get(productSkuDTO.getId());
                if (Objects.nonNull(marketItemInfoResp)) {
                    productSkuDTO.setMaxAfterSaleAmount(Objects.isNull(marketItemInfoResp.getMaxAfterSaleAmount()) ? NumberConstants.ONE : marketItemInfoResp.getMaxAfterSaleAmount());
                    productSkuDTO.setAfterSaleUnit(StringUtils.isBlank(marketItemInfoResp.getAfterSaleUnit()) ? StringConstants.DEFAULT_AFTER_SALE_UNIT : marketItemInfoResp.getAfterSaleUnit());
                }

//                List<Long> agentSkuIds = skuMap.get(productSkuDTO.getId());
//                if (CollectionUtil.isNotEmpty(agentSkuIds)) {
//                    SummerfarmProductInfoDTO summerfarmProductInfoDTO = summerfarmProductInfoDTOMap.get(agentSkuIds.get(0));
//                    if (Objects.nonNull(summerfarmProductInfoDTO)) {
//                        productSkuDTO.setMaxAfterSaleAmount(Objects.isNull(summerfarmProductInfoDTO.getMaxAfterSaleAmount()) ? NumberConstants.ONE : summerfarmProductInfoDTO.getMaxAfterSaleAmount());
//                        productSkuDTO.setAfterSaleUnit(StringUtils.isBlank(summerfarmProductInfoDTO.getAfterSaleUnit()) ? StringConstants.DEFAULT_AFTER_SALE_UNIT : summerfarmProductInfoDTO.getAfterSaleUnit());
//                    }
//                }
            }

            if(!CollectionUtils.isEmpty(longProductCategoryDTOMap)){
                ProductCategoryDTO productCategoryDTO = longProductCategoryDTOMap.get(productSkuDTO.getCategoryId());
                if (Objects.nonNull(productCategoryDTO)) {
                    productSkuDTO.setFirstCategoryId(productCategoryDTO.getFirstCategoryId());
                    productSkuDTO.setSecondCategoryId(productCategoryDTO.getSecondCategoryId());
                    productSkuDTO.setThirdCategoryId(productCategoryDTO.getThirdCategoryId());
                    productSkuDTO.setFirstCategory(productCategoryDTO.getFirstCategoryName());
                    productSkuDTO.setSecondCategory(productCategoryDTO.getSecondCategoryName());
                    productSkuDTO.setThirdCategory(productCategoryDTO.getThirdCategoryName());
                }
            }

        });

        return productSkuDTOS;
    }
    @Override
    public List<ProductSkuDTO> queryByCondition(ProductSkuQueryConditionDTO productSkuQueryConditionDTO) {
        ProductQueryInput productQueryInput = new ProductQueryInput();
        BeanUtil.copyProperties (productSkuQueryConditionDTO,productQueryInput);
        List<ProductSkuDTO> productSkuDTOS = productFacade.listSku (productQueryInput, productSkuQueryConditionDTO.getTenantId ());
        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            return new ArrayList<>();
        }
        setAftersaleInfo(productSkuDTOS);
        return productSkuDTOS;
    }

    private void setAftersaleInfo(List<ProductSkuDTO> productSkuDTOS) {
        List<Long> skuIdList = productSkuDTOS.stream().filter(e -> XianmuSupplyTenant.TENANT_ID.equals(e.getTenantId())).map(ProductSkuDTO::getId).distinct().collect(Collectors.toList());
        Map<Long, MarketItemInfoResp> marketItemMap = marketFacade.queryQuotationMarketItemMap(skuIdList);

        productSkuDTOS.forEach(productSkuDTO -> {
            productSkuDTO.setMaxAfterSaleAmount(NumberConstants.ONE);
            productSkuDTO.setAfterSaleUnit(StringConstants.DEFAULT_AFTER_SALE_UNIT);

            if (!Objects.equals(productSkuDTO.getTenantId(),  XianmuSupplyTenant.TENANT_ID)) {
                productSkuDTO.setAfterSaleUnit(productSkuDTO.getSpecificationUnit());
            } else {
                if (ObjectUtil.isNotNull (productSkuDTO.getSkuMapping ()) && ObjectUtil.isNotNull (productSkuDTO.getAgentSkuId ())) {
                    MarketItemInfoResp marketItemInfoResp = marketItemMap.get(productSkuDTO.getId());
                    if (Objects.nonNull(marketItemInfoResp)) {
                        productSkuDTO.setMaxAfterSaleAmount(Objects.isNull(marketItemInfoResp.getMaxAfterSaleAmount()) ? NumberConstants.ONE : marketItemInfoResp.getMaxAfterSaleAmount());
                        productSkuDTO.setAfterSaleUnit(StringUtils.isBlank(marketItemInfoResp.getAfterSaleUnit()) ? StringConstants.DEFAULT_AFTER_SALE_UNIT : marketItemInfoResp.getAfterSaleUnit());
                    }
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SummerFarmSynchronizedSkuVO doneSkuSynchronized(SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO, Long tenantId) {
        if(Objects.isNull(tenantId)){
            tenantId = XianmuSupplyTenant.TENANT_ID;
        }

        checkParam(summerFarmSynchronizedSkuDTO);
        Long firstCategoryId = summerFarmSynchronizedSkuDTO.getFirstCategoryId();
        if (!StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getFirstCategoryName())) {
            // 根据Id查询类目
            CategoryVO categoryVO = categoryServiceFacade.selectByCategoryId(summerFarmSynchronizedSkuDTO.getFirstCategoryId());
            if (categoryVO == null) {
                firstCategoryId = productCategoryService.add(summerFarmSynchronizedSkuDTO.getFirstCategoryId(), summerFarmSynchronizedSkuDTO.getFirstCategoryName(), null, null);
            } else if (!categoryVO.getName().equals(summerFarmSynchronizedSkuDTO.getFirstCategoryName())) {
                // 更新类目
                firstCategoryId = productCategoryService.edit(summerFarmSynchronizedSkuDTO.getFirstCategoryId(), summerFarmSynchronizedSkuDTO.getFirstCategoryName(), null, null);
            }
        }

        // 二级类目
        Long secondCategoryId = summerFarmSynchronizedSkuDTO.getSecondCategoryId();
        if (!StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getSecondCategoryName())) {
            // 根据Id查询类目
            CategoryVO categoryVO = categoryServiceFacade.selectByCategoryId(summerFarmSynchronizedSkuDTO.getSecondCategoryId());
            if (categoryVO == null) {
                secondCategoryId = productCategoryService.add(summerFarmSynchronizedSkuDTO.getSecondCategoryId(), summerFarmSynchronizedSkuDTO.getSecondCategoryName(), firstCategoryId, null);
            } else if (!categoryVO.getName().equals(summerFarmSynchronizedSkuDTO.getSecondCategoryName())) {
                // 更新类目
                secondCategoryId = productCategoryService.edit(summerFarmSynchronizedSkuDTO.getSecondCategoryId(), summerFarmSynchronizedSkuDTO.getSecondCategoryName(), firstCategoryId, null);
            }
        }

        // 二级类目
        Long thirdCategoryId = summerFarmSynchronizedSkuDTO.getThirdCategoryId();
        if (!StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getThirdCategoryName())) {
            // 根据Id查询类目
            CategoryVO categoryVO = categoryServiceFacade.selectByCategoryId(summerFarmSynchronizedSkuDTO.getThirdCategoryId());
            if (categoryVO == null) {
                thirdCategoryId = productCategoryService.add(summerFarmSynchronizedSkuDTO.getThirdCategoryId(), summerFarmSynchronizedSkuDTO.getThirdCategoryName(), secondCategoryId, null);
            } else if (!categoryVO.getName().equals(summerFarmSynchronizedSkuDTO.getThirdCategoryName())) {
                // 更新类目
                thirdCategoryId = productCategoryService.edit(summerFarmSynchronizedSkuDTO.getThirdCategoryId(), summerFarmSynchronizedSkuDTO.getThirdCategoryName(), secondCategoryId, null);
            }
        }

        // 根据spuId查询所有鲜沐skuId
        List<ProductAgentSkuMapping> productAgentSkuMappingList = productAgentSkuMappingMapper.queryAgentSkuInfoByAgentSkuIds(summerFarmSynchronizedSkuDTO.getSkuIds(), XianmuSupplyTenant.TENANT_ID, tenantId);
        // 根据鲜沐skuIds查询是否建过spuId
        Long spuId = null;
        if (!CollectionUtils.isEmpty(productAgentSkuMappingList)) {
            List<Long> skuIds = productAgentSkuMappingList.stream().map(ProductAgentSkuMapping::getSkuId).collect(Collectors.toList());
            // 根据鲜沐skuIds查询是否建过spuId
            ProductSku productSku = productSkuMapper.selectByPrimaryKey(skuIds.get(NumberConstant.ZERO));
            if (Objects.nonNull(productSku)) {
                spuId = productSku.getSpuId();
            }
        }

        // 上传图片到saas七牛云服务器
        String mainPicture = summerFarmSynchronizedSkuDTO.getMainPicture();
        String detailPicture = summerFarmSynchronizedSkuDTO.getDetailPicture();
        String skuPicture = summerFarmSynchronizedSkuDTO.getSkuPicture();

        pictureUpload(Lists.newArrayList(mainPicture, detailPicture, skuPicture));

        // 查询是否同步过商品
        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByAgentSkuIdAndAgentTenantIdAndTenantId(XianmuSupplyTenant.TENANT_ID, summerFarmSynchronizedSkuDTO.getSkuId(), tenantId);
        // 创建spu
        spuId = addProductSpu(summerFarmSynchronizedSkuDTO, spuId, thirdCategoryId, tenantId);
        // 创建sku
        Long skuId = addProductSku(summerFarmSynchronizedSkuDTO, spuId, tenantId, productAgentSkuMapping);
        // 创建货品映射
        addProductAgentSkuMapping(productAgentSkuMapping, tenantId, skuId, spuId, summerFarmSynchronizedSkuDTO);
        // 增加审核记录
        // 增加代仓服务申请记录
        if(!Objects.equals(tenantId, XianmuSupplyTenant.TENANT_ID)) {
            addProductAgentApplicationRecord(tenantId, skuId, XianmuSupplyTenant.TENANT_ID);
        }

        SummerFarmSynchronizedSkuVO summerFarmSynchronizedSkuVO = new SummerFarmSynchronizedSkuVO();
        summerFarmSynchronizedSkuVO.setSkuId(skuId);
        summerFarmSynchronizedSkuVO.setSpuId(spuId);
        summerFarmSynchronizedSkuVO.setSku(summerFarmSynchronizedSkuDTO.getSku());
        summerFarmSynchronizedSkuVO.setInvId(summerFarmSynchronizedSkuDTO.getSkuId());
        return summerFarmSynchronizedSkuVO;
    }

    /**
     * 创建代仓服务申请记录
     *
     * @param tenantId
     * @param skuId
     * @param agentTenantId
     */
    private void addProductAgentApplicationRecord(Long tenantId, Long skuId, Long agentTenantId){
        ProductAgentApplicationRecordQueryConditionDTO conditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        conditionDTO.setSkuId(skuId);
        conditionDTO.setTenantId(tenantId);
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(conditionDTO);
        if(CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            ProductAgentApplicationRecord productAgentApplicationRecord = new ProductAgentApplicationRecord();
            productAgentApplicationRecord.setTenantId(tenantId);
            productAgentApplicationRecord.setSkuId(skuId);
            productAgentApplicationRecord.setAgentTenantId(agentTenantId);
            productAgentApplicationRecord.setStatus(ProductAgentItemStatusEnum.SUCCESS.getStatus());
            productAgentApplicationRecordDao.save(productAgentApplicationRecord);
        }
    }

    /**
     * 创建货品映射
     * @param productAgentSkuMapping
     * @param tenantId
     * @param skuId
     * @param spuId
     * @param summerFarmSynchronizedSkuDTO
     */
    private void addProductAgentSkuMapping(ProductAgentSkuMapping productAgentSkuMapping, Long tenantId, Long skuId, Long spuId, SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO) {
        // 代理商品
        if (productAgentSkuMapping != null) {
            productAgentSkuMapping.setTenantId(tenantId);
            productAgentSkuMapping.setSkuId(skuId);
            productAgentSkuMapping.setAgentTenantId(XianmuSupplyTenant.TENANT_ID);
            productAgentSkuMapping.setAgentSkuId(summerFarmSynchronizedSkuDTO.getSkuId());
            productAgentSkuMapping.setAgentSkuCode(summerFarmSynchronizedSkuDTO.getSku());
            productAgentSkuMapping.setSpuId(spuId);
            productAgentSkuMapping.setAgentSpuId(summerFarmSynchronizedSkuDTO.getSpuId());
            productAgentSkuMapping.setAgentSpuCode(summerFarmSynchronizedSkuDTO.getSpu());
            productAgentSkuMappingMapper.updateByPrimaryKeySelective(productAgentSkuMapping);
        } else {
            productAgentSkuMapping = new ProductAgentSkuMapping();
            // 查詢是否已同步
            productAgentSkuMapping.setTenantId(tenantId);
            productAgentSkuMapping.setAgentTenantId(XianmuSupplyTenant.TENANT_ID);
            productAgentSkuMapping.setAgentSkuId(summerFarmSynchronizedSkuDTO.getSkuId());
            productAgentSkuMapping.setSkuId(skuId);
            productAgentSkuMapping.setAgentSkuCode(summerFarmSynchronizedSkuDTO.getSku());
            productAgentSkuMapping.setSpuId(spuId);
            productAgentSkuMapping.setAgentSpuId(summerFarmSynchronizedSkuDTO.getSpuId());
            productAgentSkuMapping.setAgentSpuCode(summerFarmSynchronizedSkuDTO.getSpu());
            productAgentSkuMappingMapper.insertSelective(productAgentSkuMapping);
        }
    }

    /**
     * 新建sku
     *
     * @param summerFarmSynchronizedSkuDTO
     * @param spuId
     * @param tenantId
     * @return
     */
    private Long addProductSku(SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO, Long spuId, Long tenantId, ProductAgentSkuMapping productAgentSkuMapping) {
        // 判断租户Id，如果是鲜沐自营产品 tenantId = XianmuSupplyTenant.TENANT_ID; 如果是代仓产品，判断是是否租户已存在，如果已存在添加租户，如果不存在，租户Id为鲜沐租户
        Long skuId = null;
        if (productAgentSkuMapping == null || Objects.isNull(productAgentSkuMapping.getSkuId())) {
            ProductSku productSku = new ProductSku();
            assembleProductSku(productSku, summerFarmSynchronizedSkuDTO, spuId, tenantId);
            productSkuMapper.insertSelective(productSku);
            skuId = productSku.getId();
        } else {
            ProductSku productSku = productSkuMapper.selectByPrimaryKey(productAgentSkuMapping.getSkuId());
            assembleProductSku(productSku, summerFarmSynchronizedSkuDTO, spuId, tenantId);
            productSkuMapper.updateByPrimaryKeySelective(productSku);
            skuId = productSku.getId();
        }

        return skuId;
    }

    /**
     * 组装productSku
     *
     * @param productSku
     * @param summerFarmSynchronizedSkuDTO
     */
    private void assembleProductSku(ProductSku productSku, SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO, Long spuId, Long tenantId){
        productSku.setSpuId(spuId);
        productSku.setTenantId(tenantId);
        productSku.setSpecification(summerFarmSynchronizedSkuDTO.getSpecification().substring(summerFarmSynchronizedSkuDTO.getSpecification().indexOf(Constants.UNDERLINE) + NumberConstant.ONE));
        productSku.setSpecificationUnit(summerFarmSynchronizedSkuDTO.getSpecificationUnit());
        productSku.setSku(summerFarmSynchronizedSkuDTO.getSku());
        productSku.setVolume(summerFarmSynchronizedSkuDTO.getVolume());
        productSku.setWeight(summerFarmSynchronizedSkuDTO.getWeightNum().doubleValue());
        productSku.setAgentType(summerFarmSynchronizedSkuDTO.getType());
        productSku.setCreateType(summerFarmSynchronizedSkuDTO.getCreateType());
        productSku.setSkuPicture(summerFarmSynchronizedSkuDTO.getSkuPicture());
        productSku.setSkuTitle(summerFarmSynchronizedSkuDTO.getSkuTitle());
        productSku.setUseFlag(summerFarmSynchronizedSkuDTO.getUseFlag());
        productSku.setSubAgentType(summerFarmSynchronizedSkuDTO.getSubAgentType());
        productSku.setPlaceType(summerFarmSynchronizedSkuDTO.getPlaceType());
        if (summerFarmSynchronizedSkuDTO.getSkuId() != null && XianmuSupplyTenant.TENANT_ID.equals(tenantId)) {
            productSku.setCustomSkuCode(summerFarmSynchronizedSkuDTO.getSkuId().toString());
        }
        if (summerFarmSynchronizedSkuDTO.getAdminId() != null) {
            productSku.setOwnerId(summerFarmSynchronizedSkuDTO.getAdminId().intValue());
        }
        if (Objects.nonNull(summerFarmSynchronizedSkuDTO.getWeightNum())) {
            productSku.setWeight(summerFarmSynchronizedSkuDTO.getWeightNum().doubleValue());
        }
    }

    private Long addProductSpu(SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO, Long spuId, Long thirdCategoryId, Long tenantId) {
        // 获取储存温度
        String storageTemperature = summerFarmSynchronizedSkuDTO.getStorageTemperature();
        if (StringUtils.isEmpty(storageTemperature)) {
            storageTemperature = ProductSkuEnum.STORAGE_LOCATION.getStorageTemperature(summerFarmSynchronizedSkuDTO.getStorageLocation());
        }
        // spu信息
        ProductSpu productSpu;
        if (spuId == null) {
            productSpu = new ProductSpu();
            convertToProductSpu(productSpu, summerFarmSynchronizedSkuDTO);
            productSpu.setTenantId(tenantId);
            productSpu.setCategoryId(thirdCategoryId);
            productSpu.setStorageTemperature(storageTemperature);
            productSpu.setCustomSpuCode(String.valueOf(summerFarmSynchronizedSkuDTO.getSpuId()));
            productSpuMapper.insertSelective(productSpu);
            spuId = productSpu.getId();
        } else {
            ProductSpu spu = productSpuMapper.selectByPrimaryKey(spuId);
            if (spu == null) {
                spu = new ProductSpu();
                convertToProductSpu(spu, summerFarmSynchronizedSkuDTO);
                spu.setId(spuId);
                spu.setTenantId(tenantId);
                spu.setCategoryId(thirdCategoryId);
                spu.setStorageTemperature(storageTemperature);
                spu.setCustomSpuCode(String.valueOf(summerFarmSynchronizedSkuDTO.getSpuId()));
                productSpuMapper.insert(spu);
            } else {
                ProductSpu updateSpu = new ProductSpu();
                convertToProductSpu(updateSpu, summerFarmSynchronizedSkuDTO);
                updateSpu.setId(spuId);
                // updateSpu.setTenantId(tenantId);
                updateSpu.setCategoryId(thirdCategoryId);
                updateSpu.setStorageTemperature(storageTemperature);
                updateSpu.setCustomSpuCode(String.valueOf(summerFarmSynchronizedSkuDTO.getSpuId()));
                productSpuMapper.updateByPrimaryKeySelective(updateSpu);
            }
        }

        return spuId;
    }

    private void convertToProductSpu(ProductSpu ProductSpu, SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO) {
        ProductSpu.setMainPicture(summerFarmSynchronizedSkuDTO.getMainPicture());
        ProductSpu.setDetailPicture(summerFarmSynchronizedSkuDTO.getDetailPicture());
        ProductSpu.setBrandName(summerFarmSynchronizedSkuDTO.getBrandName());
        ProductSpu.setTitle(summerFarmSynchronizedSkuDTO.getTitle());
        ProductSpu.setSubTitle(summerFarmSynchronizedSkuDTO.getSubTitle());
        ProductSpu.setStorageLocation(summerFarmSynchronizedSkuDTO.getStorageLocation());
        ProductSpu.setGuaranteePeriod(summerFarmSynchronizedSkuDTO.getGuaranteePeriod());
        ProductSpu.setGuaranteeUnit(summerFarmSynchronizedSkuDTO.getGuaranteeUnit());
        ProductSpu.setOrigin(summerFarmSynchronizedSkuDTO.getOrigin());
    }

    /**
     * 鲜沐图片上传到帆台文件空间
     * @param pictureList
     */
    public void pictureUpload(List<String> pictureList) {
        if (CollectionUtil.isEmpty(pictureList)) {
            return;
        }
        List<String> pictures = Lists.newArrayList();
        for (String originPicture : pictureList) {
            if (!StringUtils.isEmpty(originPicture)) {
                String[] detailPictures = originPicture.split(Constants.COMMA);
                pictures.addAll(new ArrayList<>(Arrays.asList(detailPictures)));
            }
        }

        List<String> reslut = pictures.stream ().filter (item -> !StringUtils.isEmpty (item)).collect (Collectors.toList ());
        if(CollectionUtil.isNotEmpty (reslut)) {
            String[] strings = new String[reslut.size()];
            QiNiuUtils.copyFileBatch (reslut.toArray(strings));
        }
    }

    /**
     * 同步货品参数校验
     *
     * @param summerFarmSynchronizedSkuDTO
     */
    private void checkParam(SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO) {
        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getSpuId())) {
            throw new BizException("商品spuId不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getTitle())) {
            throw new BizException("商品名称不能为空");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getFirstCategoryId())) {
            throw new BizException("商品一级类目Id不能为空");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getSecondCategoryId())) {
            throw new BizException("商品二级类目Id不能为空");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getThirdCategoryId())) {
            throw new BizException("商品三级类目Id不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getFirstCategoryName())) {
            throw new BizException("一级类目名称不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getSecondCategoryName())) {
            throw new BizException("二级类目名称不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getThirdCategoryName())) {
            throw new BizException("三级类目名称不能为空");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getStorageLocation())) {
            throw new BizException("储存区域不能为空");
        }

        String desc = ProductSkuEnum.STORAGE_LOCATION.getDesc(summerFarmSynchronizedSkuDTO.getStorageLocation());
        if (StringUtils.isEmpty(desc)) {
            throw new BizException("储存区域类型错误");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getGuaranteeUnit())) {
            throw new BizException("保质期时长不能为空");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getGuaranteeUnit())) {
            throw new BizException("保质期单位不能为空");
        }

        String guaranteeUnitDesc = ProductSkuEnum.GUARANTEE_UNIT.getDesc(summerFarmSynchronizedSkuDTO.getGuaranteeUnit());
        if (StringUtils.isEmpty(guaranteeUnitDesc)) {
            throw new BizException("保质期单位错误");
        }

        if (Objects.isNull(summerFarmSynchronizedSkuDTO.getSkuId())) {
            throw new BizException("skuId不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getSku())) {
            throw new BizException("sku编码不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getSpecification())) {
            throw new BizException("规格不能为空");
        }

        if (StringUtils.isEmpty(summerFarmSynchronizedSkuDTO.getSpecificationUnit())) {
            throw new BizException("规格单位不能为空");
        }
    }

    @Override
    public void synchronizedSupplySku(SummerfarmSkuInput summerfarmSkuInput, Long tenantId) {
        List<Long> skuIds = summerfarmSkuInput.getSkuIds();
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        skuIds.stream().forEach(skuId -> {
            SummerFarmSynchronizedSkuReq input = new SummerFarmSynchronizedSkuReq();
            input.setSkuIds(Arrays.asList(skuId));
            // 查询需要同步的sku信息
            try {
                SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp = summerFarmInterfaceServiceFacade.queryNeedSynchronizedSku(input);
                log.info("同步sku信息，skuId: {} , 具体信息: {}", skuId, JSON.toJSONString(summerFarmSynchronizedSkuResp));
                // 处理需要同步的sku信息
                ProductSkuServiceImpl productSkuService = (ProductSkuServiceImpl) AopContext.currentProxy();
                SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO = ProductConverter.convertSummerFarmSynchronizedSkuDTO(summerFarmSynchronizedSkuResp);
                productSkuService.doneSkuSynchronized(summerFarmSynchronizedSkuDTO, tenantId);
            } catch (Exception e) {
                log.error("异常信息：{}", e.getMessage(), e);
                log.error("查询需要同步的sku信息失败");
            }
        });
    }

    @Override
    public ProductSkuDTO selectProductSkuDetailById(Long skuId) {
        ProductSkuDTO productSkuDTO = productFacade.selectProductSkuDetailById (skuId);
        setAftersaleInfo(Collections.singletonList (productSkuDTO));
        return productSkuDTO;
    }
}
