package com.cosfo.manage.product.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.po.ProductCategory;
import com.cosfo.manage.product.model.vo.CategoryVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 12:44
 */
public interface ProductCategoryService {

    /**
     * 类目树
     * @return
     */
    ResultDTO listCategoryTree();

    /**
     * 查询子集类目集合
     *
     * @param id
     * @return
     */
    List<CategoryVO> queryChildCategoryList(Long id);

    /**
     * 查询子集类目集合
     *
     * @param id
     * @return
     */
    List<Long> queryChildCategoryIdsOld(Long id);
    List<Long> queryChildCategoryIds(Long categoryId);

    /**
     * 根据类目Id查询类目
     *
     * @param id
     * @return
     */
    CategoryVO selectByCategoryId(Long id);

    /**
     * 根据三级类目id查询整个类目级别
     * @param id
     * @return
     */
    ProductCategoryDTO selectWholeCategory(Long id);

    /**
     * 根据父级类目id和名称查询
     * @param parentId
     * @param name
     * @return
     */
    ProductCategory selectByParentIdAndName(Long parentId, String name);
    List<CategoryVO> selectByParentIdAndNameNew(Long parentId, String name);

    /**
     * 根据三级类目查询整个类目树
     *
     * @param categoryIds
     * @return
     */
    Map<Long, ProductCategoryDTO> batchQueryWholeCategory(Collection<Long> categoryIds);

    /**
     * 新增
     *
     * @param name
     * @param parentId
     * @return
     */
    Long add(Long id,String name, Long parentId, Integer outDated);

    /**
     * 修改
     *
     * @param id
     * @param name
     * @param parentId
     * @return
     */
    Long edit(Long id, String name, Long parentId, Integer outDated);

    /**
     * 同步鲜沐类目
     *
     * @param categoryIds
     */
    void synchronizedXianmuCategory(List<Long> categoryIds);

}
