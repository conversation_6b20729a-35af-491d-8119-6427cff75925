package com.cosfo.manage.product.service;

/**
 * <AUTHOR>
 */
//public interface ProductAgentApplicationService {
//
//    /**
//     * 查询申请列表
//     * @param productAgentApplicationQueryDTO
//     * @param loginContextInfoDTO
//     * @return
//     */
////    CommonResult<PageInfo<ProductAgentApplicationDTO>> listAll(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, LoginContextInfoDTO loginContextInfoDTO);
//
//    /**
//     * 查看详情
//     * @param id
//     * @return
//     */
////    ProductAgentApplicationDTO queryDetail(Long id);
//
//    /**
//     * 创建一个申请
//     * @param productAgentApplicationDTO
//     * @param loginContextInfoDTO
//     * @return
//     */
////    CommonResult createApplication(ProductAgentApplicationDTO productAgentApplicationDTO, LoginContextInfoDTO loginContextInfoDTO);
//
//    /**
//     * 创建一个申请项
//     * @param productAgentApplicationItemDto
//     * @param loginContextInfoDto
//     * @return
//     */
////    CommonResult createApplicationItem(ProductAgentApplicationItemDTO productAgentApplicationItemDto, LoginContextInfoDTO loginContextInfoDto);
//
//    /**
//     * 删除申请项
//     * @param id
//     * @param loginContextInfoDto
//     * @return
//     */
////    CommonResult deleteItem(Long id, LoginContextInfoDTO loginContextInfoDto);
//
//    /**
//     * 重新提交申请项
//     * @param productAgentApplicationItemDto
//     * @param loginContextInfoDto
//     * @return
//     */
////    CommonResult recreateApplicationItem(ProductAgentApplicationItemDTO productAgentApplicationItemDto, LoginContextInfoDTO loginContextInfoDto);
//
//    /**
//     * 收到商品审核结果
//     * @param summerfarmProductAuditResultDto
//     */
////    void receiveSummerfarmProductAuditResult(SummerfarmProductAuditResultDTO summerfarmProductAuditResultDto);
//
//    /**
//     * 收到申请商品回调
//     * @param summerfarmProductApplicationDto
//     */
////    void receiveCallBackData(SummerfarmProductApplicationDTO summerfarmProductApplicationDto);
//}
