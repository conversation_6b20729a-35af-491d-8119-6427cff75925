package com.cosfo.manage.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.common.mapper.CommonLocationCityMapper;
import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
import com.cosfo.manage.product.service.LocationCityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/20
 */
@Service
public class LocationCityServiceImpl implements LocationCityService {
    @Resource
    private CommonLocationCityMapper commonLocationCityMapper;

    @Override
    public List<CommonLocationCityDTO> queryByCityIds(List<Long> cityIds) {
        return commonLocationCityMapper.queryByCityIds(cityIds);
    }
    @Override
    public List<CommonLocationCityDTO> queryByCityNames(Set<String> cityNames) {
        if(CollectionUtil.isEmpty (cityNames)){
            return Collections.emptyList ();
        }
        return commonLocationCityMapper.queryByCityNames (cityNames);
    }

    @Override
    public CommonLocationCity selectByPrimaryKey(Long id) {
        return commonLocationCityMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<String> queryAllCity() {
        return commonLocationCityMapper.queryAllCommonLocationCity().stream().map (CommonLocationCityDTO::getName).collect(Collectors.toList());
    }
}
