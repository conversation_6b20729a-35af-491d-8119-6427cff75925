package com.cosfo.manage.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.product.mapper.ProductCityStockSyncMapper;
import com.cosfo.manage.product.model.dto.AgentTenantSkuDTO;
import com.cosfo.manage.product.model.dto.ProductCityStockDTO;
import com.cosfo.manage.product.model.po.ProductCityStock;
import com.cosfo.manage.product.service.ProductCityStockSyncService;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-05-18
 * @Description:
 */
@Slf4j
@Service
public class ProductCityStockSyncServiceImpl implements ProductCityStockSyncService {

    @Resource
    private ProductCityStockSyncMapper productCityStockSyncMapper;

    @Override
    public ProductCityStock selectOneByParam(Long cityId, Long skuId) {
        return productCityStockSyncMapper.selectOneByParam(cityId, skuId);
    }

    @Override
    public int upsertCityStock(AgentTenantSkuDTO agentTenantSkuDto, long totalQuantity) {
        ProductCityStockDTO updateCityStockDto = new ProductCityStockDTO();
        updateCityStockDto.setCityId(agentTenantSkuDto.getCityId());
        updateCityStockDto.setSkuId(agentTenantSkuDto.getId());
        updateCityStockDto.setQuantity(totalQuantity);
        // 存在就更新
        ProductCityStock productCityStock = selectOneByParam(agentTenantSkuDto.getCityId(), agentTenantSkuDto.getId());
        if (Objects.nonNull(productCityStock)) {
            updateCityStockDto.setId(productCityStock.getId());
            return productCityStockSyncMapper.updateByPrimaryKey(updateCityStockDto);
        }

        try {
            return productCityStockSyncMapper.insert(updateCityStockDto);
        } catch (DuplicateKeyException e) {
            log.info("ProductCityStockSyncService DuplicateKeyException 进行兜底更新数据,e", e);
            return productCityStockSyncMapper.updateByUniqueKey(updateCityStockDto);
        }
    }

    @Override
    public void upsertCityStockList(List<ProductCityStockDTO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        Table<Long, Long, ProductCityStockDTO> updateTable = HashBasedTable.create();
        insertList.forEach(productCityStockDTO -> updateTable.put(productCityStockDTO.getSkuId(), productCityStockDTO.getCityId(), productCityStockDTO));

        // 组装更新、新增数据
        List<ProductCityStockDTO> updateList = Lists.newArrayList();
        List<ProductCityStock> productCityStockList = productCityStockSyncMapper.batchSelectByParam(insertList);
        for (ProductCityStock productCityStock : productCityStockList) {
            ProductCityStockDTO productCityStockDTO = updateTable.get(productCityStock.getSkuId(), productCityStock.getCityId());
            insertList.remove(productCityStockDTO);
            productCityStockDTO.setId(productCityStock.getId());
            updateList.add(productCityStockDTO);
        }

        // 批量更新,通常数据都是在此时处理完成
        if (!CollectionUtils.isEmpty(updateList)) {
            productCityStockSyncMapper.batchUpdate(updateList);
        }

        // 可能存在并发新增的情况，捕获输出异常信息
        for (ProductCityStockDTO productCityStockDTO : insertList) {
            try {
                productCityStockSyncMapper.insert(productCityStockDTO);
            } catch (Exception e) {
                log.error("upsertCityStockList,errorMsg:{},e", e.getMessage(), e);
            }
        }
    }
}
