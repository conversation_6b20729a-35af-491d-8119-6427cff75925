package com.cosfo.manage.product.service;

import com.cosfo.manage.product.model.dto.PreferentialCostPriceQueryDTO;
import com.cosfo.manage.product.model.vo.ProductPreferentialCostPriceBasicDataVO;
import com.cosfo.manage.product.model.vo.ProductPreferentialCostPriceVO;
import com.github.pagehelper.PageInfo;

/**
 * @Author: fansongsong
 * @Date: 2024-02-21
 * @Description:
 */
public interface ProductPreferentialCostPriceService {

    /**
     * 分页查询省心订列表
     *
     * @param queryDTO
     * @return
     */
    PageInfo<ProductPreferentialCostPriceVO> listPreferentialCostPrice(PreferentialCostPriceQueryDTO queryDTO);

    /**
     * 查询基础数据汇总
     *
     * @param queryDTO
     * @return
     */
    ProductPreferentialCostPriceBasicDataVO queryBasicData(PreferentialCostPriceQueryDTO queryDTO);
}
