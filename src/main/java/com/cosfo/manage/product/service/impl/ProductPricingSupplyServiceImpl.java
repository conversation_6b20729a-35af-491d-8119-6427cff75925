package com.cosfo.manage.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.common.result.PageResultDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.FormatUtils;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.PriceUtil;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.*;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductBrandMapper;
import com.cosfo.manage.product.mapper.ProductPricingSupplyCityMappingMapper;
import com.cosfo.manage.product.mapper.ProductPricingSupplyMapper;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyCityQueryDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyCityRangeDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyEffectDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyEffectQueryDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.dto.SkuSupplyCityCountDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.po.ProductBrand;
import com.cosfo.manage.product.model.po.ProductPricingSupply;
import com.cosfo.manage.product.model.po.ProductPricingSupplyCityMapping;
import com.cosfo.manage.product.model.vo.*;
import com.cosfo.manage.product.service.LocationCityService;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.req.product.saas.SummerFarmSkuPriceInfoReq;
import net.summerfarm.client.resp.product.saas.SummerFarmSkuPriceInfoResp;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.wms.saleinventory.dto.dto.CitySupplyStatusDTO;
import net.summerfarm.wms.saleinventory.dto.res.QueryCitySupplyStatusResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 供价服务层
 * @date 2022/5/12 19:46
 */
@Slf4j
@Service
public class ProductPricingSupplyServiceImpl implements ProductPricingSupplyService {

    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;
    @Resource
    private ProductBrandMapper productBrandMapper;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private TenantService tenantService;
    @Value("${summerfarm.api-host}")
    private String apiHost;
    @Resource
    private ProductPricingSupplyCityMappingMapper productPricingSupplyCityMappingMapper;
    @Resource
    private LocationCityService locationCityService;
    @Resource
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private ProductCategoryService categoryService;
    @Resource
    private CommonService commonService;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private MarketItemFacade marketItemFacade;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private ProductFacade productFacade;

    @Resource
    private SfInterfaceServiceFacade sfInterfaceServiceFacade;
    @Resource
    private WmsSaasInventoryFacade wmsSaasInventoryFacade;
    @Resource
    private ProductSkuPreferentialCostPriceFacade preferentialCostPriceFacade;

    private static FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

    /**
     * 鲜沐
     */
    private static final String WAREHOUSE_NAME = "鲜沐";

    private void setAftersaleInfo(List<ProductSkuDTO> productSkuDTOS) {
        List<Long> skuIdList = productSkuDTOS.stream().filter(e -> XianmuSupplyTenant.TENANT_ID.equals(e.getTenantId())).map(ProductSkuDTO::getId).distinct().collect(Collectors.toList());
        Map<Long, MarketItemInfoResp> marketItemMap = marketFacade.queryQuotationMarketItemMap(skuIdList);

        productSkuDTOS.forEach(productSkuDTO -> {
            productSkuDTO.setMaxAfterSaleAmount(NumberConstants.ONE);
            productSkuDTO.setAfterSaleUnit(StringConstants.DEFAULT_AFTER_SALE_UNIT);

            if (!Objects.equals(productSkuDTO.getTenantId(),  XianmuSupplyTenant.TENANT_ID)) {
                productSkuDTO.setAfterSaleUnit(productSkuDTO.getSpecificationUnit());
            } else {
                if (ObjectUtil.isNotNull (productSkuDTO.getSkuMapping ()) && ObjectUtil.isNotNull (productSkuDTO.getAgentSkuId ())) {
                    MarketItemInfoResp marketItemInfoResp = marketItemMap.get(productSkuDTO.getId());
                    if (Objects.nonNull(marketItemInfoResp)) {
                        productSkuDTO.setMaxAfterSaleAmount(Objects.isNull(marketItemInfoResp.getMaxAfterSaleAmount()) ? NumberConstants.ONE : marketItemInfoResp.getMaxAfterSaleAmount());
                        productSkuDTO.setAfterSaleUnit(StringUtils.isBlank(marketItemInfoResp.getAfterSaleUnit()) ? StringConstants.DEFAULT_AFTER_SALE_UNIT : marketItemInfoResp.getAfterSaleUnit());
                    }
                }
            }
        });
    }

    @Override
    public PageInfo<ProductPricingSupplyDTO> listDistributionMarket(ProductPricingSupplyQueryDTO query, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.nonNull(query.getProvinceId())) {
            List<CommonLocationCity> cities = commonService.queryCitiesByProvinceId(query.getProvinceId());
            List<Long> citiesIds = cities.stream().map(CommonLocationCity::getId).collect(Collectors.toList());
            query.setCityIds(citiesIds);
        }
        if (Objects.nonNull(query.getCityId())) {
            query.setCityIds(Arrays.asList(query.getCityId()));
        }
        Long tenantId = loginContextInfoDTO.getTenantId();
        query.setTenantId(tenantId);

        ProductPricingSupplyEffectQueryDTO supplyEffectQueryDTO = new ProductPricingSupplyEffectQueryDTO ();
        BeanUtil.copyProperties (query,supplyEffectQueryDTO);
        List<ProductPricingSupplyEffectDTO> productPricingSupplies = productPricingSupplyMapper.queryEffectSkuInfo(supplyEffectQueryDTO);

        if(CollectionUtil.isEmpty (productPricingSupplies)){
            return PageInfoHelper.createPageInfo(Collections.emptyList(),query.getPageSize ());
        }
        Map<Long, ProductPricingSupplyEffectDTO> productPricingSuppliyMap = productPricingSupplies.stream().collect(Collectors.toMap(ProductPricingSupplyEffectDTO::getSupplySkuId, Function.identity()));

        List<Long> supplyIds = productPricingSupplies.stream().map(ProductPricingSupplyEffectDTO::getSupplySkuId).collect(Collectors.toList());

        ProductQueryInput productQueryInput = new ProductQueryInput();
        productQueryInput.setTitle(query.getTitle ());
        productQueryInput.setCategoryId(query.getCategoryId ());
        productQueryInput.setSkuIds(supplyIds);
        productQueryInput.setBrandName (query.getBrandName ());
        productQueryInput.setPageNum(query.getPageNum());
        productQueryInput.setPageSize(query.getPageSize());
        PageInfo<ProductSkuDTO> productSkuDTOPageInfo = productFacade.pageSku (productQueryInput, 1L);
        List<ProductSkuDTO> list = productSkuDTOPageInfo.getList ();
        if(CollectionUtil.isEmpty (list)){
            return PageInfoHelper.createPageInfo(Collections.emptyList(),query.getPageSize ());
        }

        List<Long> supplierSkuIds = list.stream ().map (ProductSkuDTO::getId).collect (Collectors.toList ());
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> supplierSkuPriceMap = querySupplyPriceSkuIdMap (supplierSkuIds, tenantId);
        Map<Long, ProductSkuPreferentialCostPriceRangeResp> costPriceRangeRespMap = Collections.emptyMap ();
        if(query.getWithPreferentailFlag ()){  // NOSONAR
            // 查询省心定价格信息
            Map<Long, List<Long>> preferentialCostPriceRangeQueryMap = new HashMap<> ();
            supplierSkuPriceMap.forEach ((skuId,citys)-> preferentialCostPriceRangeQueryMap.put (skuId,citys.stream().map (ProductPricingSupplyCityMappingDTO::getCityId).collect(Collectors.toList())));
            costPriceRangeRespMap = queryPreferentialCostPriceRange (tenantId, preferentialCostPriceRangeQueryMap);
        }
        List<ProductPricingSupplyDTO> resultList = new ArrayList<> (query.getPageSize ());
        setAftersaleInfo(list);
        // 查询关联销售商品
        for (ProductSkuDTO productSku : list) {
            ProductPricingSupplyDTO supply = new ProductPricingSupplyDTO();
            BeanUtil.copyProperties (productSku,supply);
            Long supplySkuId = productSku.getId ();
            ProductPricingSupplyEffectDTO pricingSupplyEffectDTO = productPricingSuppliyMap.get (supplySkuId);
            supply.setId (pricingSupplyEffectDTO.getId ());
            supply.setHavingRelated(pricingSupplyEffectDTO.getHavingRelated ());
            supply.setStartTime(pricingSupplyEffectDTO.getStartTime ());
            supply.setEndTime(pricingSupplyEffectDTO.getEndTime ());
            supply.setSupplyCityCount(pricingSupplyEffectDTO.getSupplyCityCount ());
            supply.setCityNum (pricingSupplyEffectDTO.getSupplyCityCount ());


            supply.setSpecification(productSku.getSpecification());
            supply.setSpecificationUnit(productSku.getSpecificationUnit());
            supply.setProductType(ProductTypeEnum.DISTRIBUTION.getType());
            supply.setMainPicture(productSku.getMainPicture());
            supply.setTitle(productSku.getTitle());
            supply.setSubTitle(productSku.getSubTitle());
            supply.setBrandName(productSku.getBrandName());
            supply.setFirstCategoryName(productSku.getFirstCategory());
            supply.setCategory(productSku.getFirstCategory());
            supply.setSecondCategoryName(productSku.getSecondCategory());
            supply.setSecondaryCategory (productSku.getSecondCategory());
            supply.setThirdCategoryName(productSku.getThirdCategory());
            supply.setThirdCategory(productSku.getThirdCategory());

            // 城市售卖信息
            List<ProductPricingSupplyCityMappingDTO> citySupplyList = supplierSkuPriceMap.get(pricingSupplyEffectDTO.getSupplySkuId ());
            BigDecimal maxPrice = citySupplyList.stream().filter(e -> ObjectUtil.isNotNull (e.getMaxPrice()) && e.getMaxPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMaxPrice).max((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
            BigDecimal minPrice = citySupplyList.stream().filter(e -> ObjectUtil.isNotNull (e.getMinPrice()) && e.getMinPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMinPrice).min((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
            ProductSkuPreferentialCostPriceRangeResp costPriceRange = costPriceRangeRespMap.get (pricingSupplyEffectDTO.getSupplySkuId ());
            supply.setMaxSupplyPrice(getMaxPrice (maxPrice.compareTo(BigDecimal.ZERO) == NumberConstants.ZERO ? null : maxPrice,costPriceRange));
            supply.setMinSupplyPrice(getMinPrice (minPrice.compareTo(BigDecimal.ZERO) == NumberConstants.ZERO ? null : minPrice,costPriceRange));
            supply.setMinPrice (supply.getMinSupplyPrice ());
            supply.setMaxPrice (supply.getMaxSupplyPrice ());
            supply.setSupplyCityCount(citySupplyList.size());
            supply.setPriceStr(PriceUtil.buildPriceRange(supply.getMaxPrice(),supply.getMinPrice()));
            supply.setSupplySkuId (supplySkuId);
            resultList.add (supply);
        }
        PageInfo pageInfo = productSkuDTOPageInfo;
        pageInfo.setList (resultList);
        return pageInfo;
    }

//    private Pair<LocalDateTime, LocalDateTime> getAggregationTime(List<ProductPricingSupplyDTO> supplyTimeList, LocalDateTime now) {
//        if (CollectionUtil.isEmpty(supplyTimeList)) {
//            return Pair.of(null, null);
//        }
//        LocalDateTime startTime = null;
//        LocalDateTime endTime = null;
//        List<ProductPricingSupplyDTO> timeList = supplyTimeList.stream().sorted(Comparator.comparing(ProductPricingSupplyDTO::getStartTime)).collect(Collectors.toList());
//        for (ProductPricingSupplyDTO productPricingSupplyDTO : timeList) {
//            // 获取第一个包含当前时间的生效时间
//            if (Objects.isNull(startTime) && productPricingSupplyDTO.getStartTime().isBefore(now) && productPricingSupplyDTO.getEndTime().isAfter(now)) {
//                startTime = productPricingSupplyDTO.getStartTime();
//                endTime = productPricingSupplyDTO.getEndTime();
//            }
//
//            // 开始和结束时间相同也是连续!isAfter
//            if (Objects.nonNull(startTime) && !productPricingSupplyDTO.getStartTime().isAfter(endTime) && productPricingSupplyDTO.getEndTime().isAfter(endTime)) {
//                endTime = productPricingSupplyDTO.getEndTime();
//            }
//        }
//        return Pair.of(startTime, endTime);
//    }


    @Override
    public CommonResult<ProductPricingSupplyVO> queryMarketDetail(Long id, Integer productType, LoginContextInfoDTO loginContextInfoDTO) {
        Long supplierSkuId;
        ProductPricingSupplyVO data = new ProductPricingSupplyVO();
        if (Objects.equals(productType, ProductTypeEnum.DISTRIBUTION.getType())) {
            ProductPricingSupply supply = productPricingSupplyMapper.selectByPrimaryKey(id);
            supplierSkuId = supply.getSupplySkuId();
            data.setId(supply.getId());
            data.setSupplyTenantId(supply.getSupplyTenantId());
            data.setHavingRelated(supply.getAssociated());
            if(!Objects.equals (supply.getTenantId (),loginContextInfoDTO.getTenantId ())){
                throw new DefaultServiceException("货品不存在！！");
            }
        } else {
            throw new DefaultServiceException("没有代仓货品详情了哦！！");
        }

        ProductSkuDTO supplierSkuInfo = productFacade.selectProductSkuDetailById (supplierSkuId);
        if (Objects.isNull(supplierSkuInfo)) {
            throw new DefaultServiceException("未查询到该商品信息");
        }
        BeanUtils.copyProperties(supplierSkuInfo, data);
        data.setId(id);
        data.setSpuId(supplierSkuInfo.getSpuId());
        data.setSkuId(supplierSkuId);

        data.setFirstCategoryName(supplierSkuInfo.getFirstCategory());
        data.setSecondCategoryName(supplierSkuInfo.getSecondCategory());
        data.setThirdCategoryName(supplierSkuInfo.getThirdCategory());
        data.setBrandName(supplierSkuInfo.getBrandName());
        // 供应商
        TenantDTO tenantDTO = tenantService.queryTenantById(data.getSupplyTenantId());
        data.setSupplierName(tenantDTO.getTenantName());
        data.setProductType(productType);

        // 供应价
        if (Objects.equals(productType, ProductTypeEnum.DISTRIBUTION.getType())) {
            List<Long> supplierSkuIds = Arrays.asList(supplierSkuId);
            Map<Long, List<ProductPricingSupplyCityMappingDTO>> supplierSkuPriceMap = querySupplyPriceSkuIdMap (supplierSkuIds, loginContextInfoDTO.getTenantId ());
            List<ProductPricingSupplyCityMappingDTO> citySupplyList = supplierSkuPriceMap.get(supplierSkuId);

            if (CollectionUtils.isEmpty(citySupplyList)) {
                data.setMaxSupplyPrice(null);
                data.setMinSupplyPrice(null);
            } else {
                BigDecimal maxPrice = citySupplyList.stream().filter(e -> e.getMaxPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMaxPrice).max((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
                BigDecimal minPrice = citySupplyList.stream().filter(e -> e.getMinPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMinPrice).min((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
                data.setMaxSupplyPrice(maxPrice.compareTo(BigDecimal.ZERO) == NumberConstants.ZERO ? null : maxPrice);
                data.setMinSupplyPrice(minPrice.compareTo(BigDecimal.ZERO) == NumberConstants.ZERO ? null : minPrice);
            }
        }

        return CommonResult.ok(data);
    }
    @Override
    public ResultDTO<PageInfo<ProductPricingSupplyCityVO>> queryCitySupplyPriceDetail(ProductPricingSupplyCityQueryDTO productPricingSupplyCityQueryDTO) {
        AssertCheckParams.notNull(productPricingSupplyCityQueryDTO.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "报价单Id不能为空");
        PageHelper.startPage(productPricingSupplyCityQueryDTO.getPage(), productPricingSupplyCityQueryDTO.getPageSize());
        List<ProductPricingSupplyCityVO> productPricingSupplyCityVos = productPricingSupplyCityMappingMapper.queryByIdAndCityName(productPricingSupplyCityQueryDTO.getId(), productPricingSupplyCityQueryDTO.getCityName());
        ProductPricingSupply productPricingSupply = productPricingSupplyMapper.selectByPrimaryKey(productPricingSupplyCityQueryDTO.getId());
        if (Objects.isNull(productPricingSupply)){
            throw new BizException("未找到id:" + productPricingSupplyCityQueryDTO.getId() + "的报价单！");
        }
        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
        if(Objects.nonNull(productPricingSupply.getSupplySkuId())) {
            productSkuDTO = productFacade.selectProductSkuDetailById (productPricingSupply.getSupplySkuId());
        }
        // 供应价
        List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = querySkuSupplyPrice(Collections.singletonList(productPricingSupply.getSupplySkuId()), productPricingSupply.getTenantId());
        Map<Long, ProductPricingSupplyCityMappingDTO> skuSupplyPriceMap = productPricingSupplyCityMappingDTOS.stream().collect(Collectors.toMap(ProductPricingSupplyCityMappingDTO::getId, item -> item));
        Long skuId = productSkuDTO.getSkuMapping ().getAgentSkuId ();

        List<CitySupplyStatusDTO> citySupplyStatusDTOList = new ArrayList<>();
        for (ProductPricingSupplyCityVO productPricingSupplyCityVO : productPricingSupplyCityVos) {
            CitySupplyStatusDTO citySupplyStatusDTO = new CitySupplyStatusDTO();
            citySupplyStatusDTO.setCitySupplyPriceId(productPricingSupplyCityVO.getId());
            citySupplyStatusDTO.setCityName(productPricingSupplyCityVO.getCityName());
            citySupplyStatusDTO.setSkuId(skuId);
            citySupplyStatusDTOList.add(citySupplyStatusDTO);
        }

        Map<Long, QueryCitySupplyStatusResp> summerfarmSkuSupplyStatusDtoMap = new HashMap<>(16);
        try {
            List<QueryCitySupplyStatusResp> summerFarmSkuSupplyStatusResps = wmsSaasInventoryFacade.queryCitySupplyStatus(citySupplyStatusDTOList);
            // 查询商品供应状态
            summerfarmSkuSupplyStatusDtoMap = summerFarmSkuSupplyStatusResps.stream().collect(Collectors.toMap(
                    QueryCitySupplyStatusResp::getCitySupplyPriceId, item -> item
            ));
        } catch (Exception e) {
            log.error("查询鲜沐品供应状态失败，失败原因：", e.getMessage());
        }
        Map<Long, ProductSkuCityPreferentialCostPriceResp> preferentialCostPriceMap = Collections.emptyMap ();
        //查询省心定
        try {
            List<ProductSkuCityPreferentialCostPriceResp> preferentialCostPriceResps = preferentialCostPriceFacade.queryCityPreferentialCostPrice (productPricingSupply.getTenantId (), productPricingSupply.getSupplySkuId (), productPricingSupplyCityVos.stream ().map (ProductPricingSupplyCityVO::getCityId).collect (Collectors.toList ()));
            preferentialCostPriceMap = preferentialCostPriceResps.stream ().collect (Collectors.toMap (ProductSkuCityPreferentialCostPriceResp::getCityId, item -> item));
        } catch (Exception e) {
            log.error("查询省心定失败，失败原因：", e.getMessage());
        }

        Map<Long, QueryCitySupplyStatusResp> finalSummerfarmSkuSupplyStatusDtoMap = summerfarmSkuSupplyStatusDtoMap;
        ProductSkuDTO finalProductSkuDTO = productSkuDTO;
        Map<Long, ProductSkuCityPreferentialCostPriceResp> finalPreferentialCostPriceMap = preferentialCostPriceMap;
        productPricingSupplyCityVos.forEach(productPricingSupplyCityVO -> {
            // 供应价
            if (PriceTypeEnum.SPECIFIED_PRICE.getCode().equals(productPricingSupplyCityVO.getType())) {
                productPricingSupplyCityVO.setPriceStr(productPricingSupplyCityVO.getPrice().toString());
            } else {
                ProductPricingSupplyCityMappingDTO productPricingSupplyCityMappingDTO = skuSupplyPriceMap.get(productPricingSupplyCityVO.getId());
                if (Objects.nonNull(productPricingSupplyCityMappingDTO)) {
                    String priceStr = PriceUtil.buildPriceRange(productPricingSupplyCityMappingDTO.getMaxPrice(), productPricingSupplyCityMappingDTO.getMinPrice());
                    productPricingSupplyCityVO.setPriceStr(priceStr);
                }
            }

            // 供应状态
            if (finalSummerfarmSkuSupplyStatusDtoMap.containsKey(productPricingSupplyCityVO.getId())) {
                QueryCitySupplyStatusResp summerfarmSkuSupplyStatusDTO = finalSummerfarmSkuSupplyStatusDtoMap.get(productPricingSupplyCityVO.getId());
                productPricingSupplyCityVO.setSupplyStatus(summerfarmSkuSupplyStatusDTO.getSupplyStatus());
            } else {
                productPricingSupplyCityVO.setSupplyStatus(SupplyStatusEnum.NOT_HAVING_INVENTORY.getCode());
            }

            productPricingSupplyCityVO.setTitle(finalProductSkuDTO.getTitle());
            productPricingSupplyCityVO.setMainPicture(finalProductSkuDTO.getMainPicture());
            productPricingSupplyCityVO.setSpecification(finalProductSkuDTO.getSpecification());
            productPricingSupplyCityVO.setSpecificationUnit(finalProductSkuDTO.getSpecificationUnit());
            productPricingSupplyCityVO.setSku(finalProductSkuDTO.getSku());
            productPricingSupplyCityVO.setSkuId(productPricingSupply.getSupplySkuId());
            ProductSkuCityPreferentialCostPriceResp preferentialCostPrice = finalPreferentialCostPriceMap.get (productPricingSupplyCityVO.getCityId ());
            if(ObjectUtil.isNotEmpty (preferentialCostPrice)){
                productPricingSupplyCityVO.setProductSkuPreferentialCostPrice (preferentialCostPrice.getPrice ());
                productPricingSupplyCityVO.setProductSkuPreferentialCostPriceEndTime (preferentialCostPrice.getEndTime ());
                productPricingSupplyCityVO.setProductSkuPreferentialCostPriceStartTime (preferentialCostPrice.getStartTime ());
            }
        });

        return ResultDTO.success(PageInfoHelper.createPageInfo(productPricingSupplyCityVos, productPricingSupplyCityQueryDTO.getPageSize()));
    }
//
//    /**
//     * 构建货品供应价
//     * @param skuSupplyPriceMap
//     * @param productPricingSupplyCityVO
//     */
//    private void builderPriceStr(Map<Long, ProductPricingSupplyCityMappingDTO> skuSupplyPriceMap, ProductPricingSupplyCityVO productPricingSupplyCityVO) {
//        // TODO: 2024/3/15 供应价生成
//        ProductPricingSupplyCityMappingDTO productPricingSupplyCityMappingDTO = skuSupplyPriceMap.get(productPricingSupplyCityVO.getId());
//        BigDecimal minPrice = Optional.ofNullable(productPricingSupplyCityMappingDTO.getMinPrice()).orElse(BigDecimal.ZERO);
//        BigDecimal maxPrice = Optional.ofNullable(productPricingSupplyCityMappingDTO.getMaxPrice()).orElse(BigDecimal.ZERO);
//        // 供应价
//        if (PriceTypeEnum.SUMMERFATM_PRICE.getCode().equals(productPricingSupplyCityVO.getType())) {
//            String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(maxPrice, minPrice);
//            productPricingSupplyCityVO.setPriceStr(priceStr);
//        } else if (PriceTypeEnum.SUMMER_FARM_PRICE_UP.getCode().equals(productPricingSupplyCityVO.getType())) {
//            BigDecimal rateValue = NumberUtil.add(BigDecimal.ONE, NumberUtil.mul(productPricingSupplyCityVO.getStrategyValue(), NumberConstants.CENTS));
//            String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(NumberUtil.mul(maxPrice, rateValue), NumberUtil.mul(minPrice, rateValue));
//            productPricingSupplyCityVO.setPriceStr(priceStr);
//        } else if (PriceTypeEnum.SUMMER_FARM_PRICE_DOWN.getCode().equals(productPricingSupplyCityVO.getType())) {
//            BigDecimal rateValue = NumberUtil.sub(BigDecimal.ONE, NumberUtil.mul(productPricingSupplyCityVO.getStrategyValue(), NumberConstants.CENTS));
//            // 兜底策略，当报价低于0.01时(如鲜沐商城价变化)，取鲜沐供应价
//            if (NumberUtil.mul(minPrice, rateValue).setScale(2, RoundingMode.HALF_UP).compareTo(NumberConstants.CENTS) <= 0) {
//                String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(maxPrice, minPrice);
//                productPricingSupplyCityVO.setPriceStr(priceStr);
//                return;
//            }
//
//            String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(NumberUtil.mul(maxPrice, rateValue), NumberUtil.mul(minPrice, rateValue));
//            productPricingSupplyCityVO.setPriceStr(priceStr);
//        } else if (PriceTypeEnum.SUMMER_FARM_PRICE_INCREASE.getCode().equals(productPricingSupplyCityVO.getType())) {
//            String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(NumberUtil.add(maxPrice, productPricingSupplyCityVO.getStrategyValue()),
//                    NumberUtil.add(minPrice, productPricingSupplyCityVO.getStrategyValue()));
//            productPricingSupplyCityVO.setPriceStr(priceStr);
//        } else if (PriceTypeEnum.SUMMER_FARM_PRICE_DECREASE.getCode().equals(productPricingSupplyCityVO.getType())) {
//            // 兜底策略，当报价低于0时(如鲜沐商城价变化)，取鲜沐供应价
//            if (NumberUtil.sub(minPrice, productPricingSupplyCityVO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP).compareTo(NumberConstants.CENTS) <= 0) {
//                String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(maxPrice, minPrice);
//                productPricingSupplyCityVO.setPriceStr(priceStr);
//                return;
//            }
//
//            String priceStr = PriceUtil.buildPriceRangeForSupplyPrice(NumberUtil.sub(maxPrice, productPricingSupplyCityVO.getStrategyValue()),
//                    NumberUtil.sub(minPrice, productPricingSupplyCityVO.getStrategyValue()));
//            productPricingSupplyCityVO.setPriceStr(priceStr);
//        } else {
//            productPricingSupplyCityVO.setPriceStr(productPricingSupplyCityVO.getPrice().toString());
//        }
//    }
//
    @Override
    public List<ProductPricingSupplyCityMappingDTO> querySkuSupplyPrice(List<Long> supplySkuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(supplySkuIds)) {
            return new ArrayList<>();
        }

        List<SummerFarmSkuPriceInfoReq> summerfarmSkuPriceInfoInputs = new ArrayList<>();
        TenantDTO tenantDTO = tenantService.queryTenantById(tenantId);
        List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDtos = productPricingSupplyCityMappingMapper.queryByTenantIdAndSupplySkuId(tenantId, supplySkuIds);
        if(!CollectionUtils.isEmpty(productPricingSupplyCityMappingDtos)) {
            Set<Long> productSkuIds = productPricingSupplyCityMappingDtos.stream().map(ProductPricingSupplyCityMappingDTO::getSupplySkuId).collect(Collectors.toSet());
            List<ProductsMappingResp> productAgentSkuMappings = productFacade.listProductMappingBySkuIdAndTenantId(productSkuIds,SupplierTenantEnum.SUMMERFARM.getId());
            Map<Long, ProductsMappingResp> productAgentSkuMappingMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductsMappingResp::getSkuId, item -> item));
            productPricingSupplyCityMappingDtos.forEach(productPricingSupplyCityMappingDTO -> {
                if (!PriceTypeEnum.SPECIFIED_PRICE.getCode().equals(productPricingSupplyCityMappingDTO.getType())) {
                    SummerFarmSkuPriceInfoReq summerfarmSkuPriceInfoInput = new SummerFarmSkuPriceInfoReq();
                    summerfarmSkuPriceInfoInput.setAdminId(tenantDTO.getAdminId());
                    summerfarmSkuPriceInfoInput.setCitySupplyPriceId(productPricingSupplyCityMappingDTO.getId());
                    ProductsMappingResp productAgentSkuMapping = productAgentSkuMappingMap.get(productPricingSupplyCityMappingDTO.getSupplySkuId());
                    summerfarmSkuPriceInfoInput.setSkuId(productAgentSkuMapping.getAgentSkuId());
                    summerfarmSkuPriceInfoInput.setCityName(Arrays.asList(productPricingSupplyCityMappingDTO.getCityName()));
                    summerfarmSkuPriceInfoInputs.add(summerfarmSkuPriceInfoInput);
                }
            });

            Map<Long, SummerFarmSkuPriceInfoResp> summerfarmSkuPriceInfoDTOMap = new HashMap<Long, SummerFarmSkuPriceInfoResp>(16);
            if (!CollectionUtils.isEmpty(summerfarmSkuPriceInfoInputs)) {
                // 调用rpc服务
                try {
                    List<SummerFarmSkuPriceInfoResp> summerFarmSkuPriceInfoResps = sfInterfaceServiceFacade.queryAdminSkuPricingInfo(summerfarmSkuPriceInfoInputs);
                    summerfarmSkuPriceInfoDTOMap = summerFarmSkuPriceInfoResps.stream().collect(Collectors.toMap(SummerFarmSkuPriceInfoResp::getCitySupplyPriceId, item -> item));
                } catch (Exception e) {
                    log.error("tenantId:{}, supplySkuIds:{} 获取鲜沐报价单失败,失败原因：{}", tenantId, JSON.toJSONString(supplySkuIds), e.getMessage());
                }
            }

            for (ProductPricingSupplyCityMappingDTO mappingDTO : productPricingSupplyCityMappingDtos) {
                SummerFarmSkuPriceInfoResp summerfarmSkuPriceInfoDTO = summerfarmSkuPriceInfoDTOMap.get(mappingDTO.getId());
                BigDecimal minPrice = Optional.ofNullable(summerfarmSkuPriceInfoDTO).map(SummerFarmSkuPriceInfoResp::getMinPrice).orElse(BigDecimal.ZERO);
                BigDecimal maxPrice = Optional.ofNullable(summerfarmSkuPriceInfoDTO).map(SummerFarmSkuPriceInfoResp::getMaxPrice).orElse(BigDecimal.ZERO);

                if (PriceTypeEnum.SUMMERFATM_PRICE.getCode().equals(mappingDTO.getType())) {
                    mappingDTO.setMinPrice(minPrice.setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(maxPrice.setScale(2, RoundingMode.HALF_UP));
                } else if (PriceTypeEnum.SUMMER_FARM_PRICE_UP.getCode().equals(mappingDTO.getType())) {
                    BigDecimal rateValue = NumberUtil.add(BigDecimal.ONE, NumberUtil.mul(mappingDTO.getStrategyValue(), NumberConstants.CENTS));
                    mappingDTO.setMinPrice(NumberUtil.mul(minPrice, rateValue).setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(NumberUtil.mul(maxPrice, rateValue).setScale(2, RoundingMode.HALF_UP));
                } else if (PriceTypeEnum.SUMMER_FARM_PRICE_DOWN.getCode().equals(mappingDTO.getType())) {
                    BigDecimal rateValue = NumberUtil.sub(BigDecimal.ONE, NumberUtil.mul(mappingDTO.getStrategyValue(), NumberConstants.CENTS));
                    // 兜底策略，当报价低于0.01时(如鲜沐商城价变化)，取鲜沐供应价
                    if (NumberUtil.mul(minPrice, rateValue).compareTo(NumberConstants.CENTS) <= 0) {
                        mappingDTO.setMinPrice(minPrice.setScale(2, RoundingMode.HALF_UP));
                        mappingDTO.setMaxPrice(maxPrice.setScale(2, RoundingMode.HALF_UP));
                        continue;
                    }
                    mappingDTO.setMinPrice(NumberUtil.mul(minPrice, rateValue).setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(NumberUtil.mul(maxPrice, rateValue).setScale(2, RoundingMode.HALF_UP));
                } else if (PriceTypeEnum.SUMMER_FARM_PRICE_INCREASE.getCode().equals(mappingDTO.getType())) {
                    mappingDTO.setMinPrice(NumberUtil.add(minPrice, mappingDTO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(NumberUtil.add(maxPrice, mappingDTO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP));
                } else if (PriceTypeEnum.SUMMER_FARM_PRICE_DECREASE.getCode().equals(mappingDTO.getType())) {
                    // 兜底策略，当报价低于0时(如鲜沐商城价变化)，取鲜沐供应价
                    if (NumberUtil.sub(minPrice, mappingDTO.getStrategyValue()).compareTo(NumberConstants.CENTS) <= 0) {
                        mappingDTO.setMinPrice(minPrice.setScale(2, RoundingMode.HALF_UP));
                        mappingDTO.setMaxPrice(maxPrice.setScale(2, RoundingMode.HALF_UP));
                        continue;
                    }
                    mappingDTO.setMinPrice(NumberUtil.sub(minPrice, mappingDTO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(NumberUtil.sub(maxPrice, mappingDTO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP));
                } else {
                    mappingDTO.setMinPrice(mappingDTO.getPrice().setScale(2, RoundingMode.HALF_UP));
                    mappingDTO.setMaxPrice(mappingDTO.getPrice().setScale(2, RoundingMode.HALF_UP));
                }
            }
        }

        return productPricingSupplyCityMappingDtos;
    }
    @Override
    public Map<Long, List<ProductPricingSupplyCityMappingDTO>> querySupplyPriceSkuIdMap(List<Long> supplySkuIds, Long tenantId) {
        List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = querySkuSupplyPrice (supplySkuIds, tenantId);
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> supplierSkuPriceMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)){
            supplierSkuPriceMap = productPricingSupplyCityMappingDTOS.stream().collect(Collectors.groupingBy(ProductPricingSupplyCityMappingDTO::getSupplySkuId));
        }
        return supplierSkuPriceMap;
    }

    @Override
    public Map<Long, ProductSkuPreferentialCostPriceRangeResp> queryPreferentialCostPriceRange(Long tenantId, Map<Long, List<Long>> preferentialCostPriceRangeQueryMap) {
        return preferentialCostPriceFacade.queryPreferentialCostPriceRange (tenantId,preferentialCostPriceRangeQueryMap);
    }
    @Override
    public List<ProductPricingSupplyCityMappingDTO> querySupplyCity(Long supplySkuId, Long tenantId) {
        return productPricingSupplyMapper.querySupplyCity(supplySkuId, tenantId);
    }

    @Override
    public List<ProductPricingSupplyCityMappingDTO> querySupplyCityBySkuId(List<Long> skuIds, Long tenantId) {
        return productPricingSupplyMapper.querySupplyCityBySkuId(skuIds, tenantId);
    }

    @Override
    public ProductPricingSupplyCityRangeDTO queryCitySupplyPriceRange(Long productPricingSupplyId) {
        return productPricingSupplyCityMappingMapper.queryCitySupplyPriceRange(productPricingSupplyId);
    }

    @Override
    public void updateAssociated(Long skuId, Long tenantId, Integer associated) {
        productPricingSupplyMapper.updateAssociated(skuId, tenantId, associated);
    }

    @Override
    public CommonResult exportList(LoginContextInfoDTO contextInfoDTO, ProductPricingSupplyQueryDTO supplyQueryDTO) {
        Long tenantId = contextInfoDTO.getTenantId();

        //生成对应的查询条件
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.SIX);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty (supplyQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.GOODS_NAME, supplyQueryDTO.getTitle());
        }
        if (Objects.nonNull(supplyQueryDTO.getCategoryId())) {
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(supplyQueryDTO.getCategoryId());
            if(ObjectUtil.isNull (categoryDTO)){
                throw new BizException("查询没有记录");
            }
            queryParamsMap.put(Constants.CATEGORY, categoryDTO.getCategoryStr());
        }
        if (Objects.nonNull(supplyQueryDTO.getSkuId())) {
            queryParamsMap.put(Constants.SKU_ID_UPPER, supplyQueryDTO.getSkuId().toString());
        }
        if (Objects.nonNull(supplyQueryDTO.getAssociated())) {
            queryParamsMap.put(Constants.ASSOCIATED_DESC, SkuAssociatedItemEnum.getByCode(Integer.parseInt (supplyQueryDTO.getAssociated())).getDesc());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty (supplyQueryDTO.getBrandName ())) {
            queryParamsMap.put(Constants.BRAND_NAME, supplyQueryDTO.getBrandName ());
        }
        if (Objects.nonNull (supplyQueryDTO.getCityId ())) {
            CommonLocationCity commonLocationCity = locationCityService.selectByPrimaryKey (supplyQueryDTO.getCityId ());
            if(ObjectUtil.isNull (commonLocationCity)){
                throw new BizException("查询没有记录");
            }
            queryParamsMap.put(Constants.CITY, commonLocationCity.getName ());
        }
        // 构建其他查询参数
        if (Objects.nonNull(supplyQueryDTO.getProvinceId())) {
            List<CommonLocationCity> cities = commonService.queryCitiesByProvinceId(supplyQueryDTO.getProvinceId());
            List<Long> citiesIds = cities.stream().map(CommonLocationCity::getId).collect(Collectors.toList());
            supplyQueryDTO.setCityIds(citiesIds);
        }
        supplyQueryDTO.setTenantId(tenantId);
        LocalDateTime now = LocalDateTime.now();
        supplyQueryDTO.setEffectTime(now);

        String fileName = "供应商直发货品表" + LocalDateTimeUtil.format (LocalDateTime.now(),"yyyyMMddHHmmss") + ".xlsx";

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PRODUCT_SKU_SUPPLY.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(fileName);
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(supplyQueryDTO, ee -> {
            // 1、表格处理
            String filePath = generateProductSpuExportFile(supplyQueryDTO, tenantId);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        // 保存记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(tenantId);
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        record.setType(FileDownloadTypeEnum.PRODUCT_SKU_SUPPLY.getType());
//        Long recordId = fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        try {
//            //发送导出异步消息
//            ExecutorFactory.generateExcelExecutor.execute(() -> {
//                try {
//                    generateProductSpuExportFile(supplyQueryDTO, tenantId, recordId);
//                } catch (Exception e) {
//                    fileDownloadRecordService.updateFailStatus(record.getId());
//                    log.error("供应商直发货品导出失败", e);
//                }
//            });
//        } catch (RejectedExecutionException e) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "导出任务繁忙，稍后再试");
//        }
        return CommonResult.ok();
    }

    @Override
    public BigDecimal getMinPrice(BigDecimal minPrice, ProductSkuPreferentialCostPriceRangeResp productSkuPreferentialCostPriceRange) {
        BigDecimal result = minPrice;
        if(ObjectUtil.isNotEmpty (productSkuPreferentialCostPriceRange)){
            BigDecimal pMinPrice = productSkuPreferentialCostPriceRange.getMinPrice ();
            if(ObjectUtil.isNotEmpty (minPrice)){
                if(ObjectUtil.isNotEmpty (pMinPrice)) {
                    result = minPrice.compareTo (pMinPrice)<0?minPrice:pMinPrice;
                }
            }else{
                if(ObjectUtil.isNotEmpty (pMinPrice)) {
                    result = pMinPrice;
                }
            }
        }
        return ObjectUtil.isNotEmpty (result) && result.compareTo (BigDecimal.ZERO)<0 ?BigDecimal.ZERO:result;
    }
    @Override
    public BigDecimal getMaxPrice(BigDecimal maxPrice, ProductSkuPreferentialCostPriceRangeResp productSkuPreferentialCostPriceRange) {
        BigDecimal result =  maxPrice;
        if(ObjectUtil.isNotEmpty (productSkuPreferentialCostPriceRange)){
            BigDecimal pMaxPrice = productSkuPreferentialCostPriceRange.getMaxPrice ();
            if(ObjectUtil.isNotEmpty (maxPrice)){
                if(ObjectUtil.isNotEmpty (pMaxPrice)) {
                    result = maxPrice.compareTo (pMaxPrice)>0?maxPrice:pMaxPrice;
                }
            }else{
                if(ObjectUtil.isNotEmpty (pMaxPrice)) {
                    result = pMaxPrice;
                }
            }
        }
        return ObjectUtil.isNotEmpty (result) && result.compareTo (BigDecimal.ZERO)<0 ?BigDecimal.ZERO:result;
    }


    //    FinancialBillServiceImpl.storeBillCreate()
//    ProductServiceImpl.export
    private String generateProductSpuExportFile(ProductPricingSupplyQueryDTO supplyQueryDTO, Long tenantId){
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.PRODUCT_SKU_SUPPLY.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        Map<Long,Set<BigDecimal>> skuMap = generateProductSpuExportFileSheet0 (supplyQueryDTO,filePath, excelWriter, tenantId);
        generateProductSpuExportFileSheet1 (skuMap, excelWriter, tenantId);
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, fileName, ExcelTypeEnum.PRODUCT_SKU_SUPPLY, recordId);
    }


    private void generateProductSpuExportFileSheet1(Map<Long,Set<BigDecimal>> skuMap, ExcelWriter excelWriter, Long tenantId) {
        ArrayList<ProductSkuMarketItemExportVO> collect = new ArrayList<> ();

        if(CollectionUtil.isNotEmpty (skuMap)) {

            Set<Long> skuIds = skuMap.keySet ();
            Map<Long, List<MarketItemInfoResp>> map = marketItemFacade.queryAssociatedItemBySkuIds (skuIds, tenantId);
            map.values ().stream ().sorted ((o1, o2) -> o2.size () - o1.size ());
            List<ProductSkuDTO> skuDTOS;
            if (grayReleaseConfig.executeProductCenterGray(tenantId)) {
                skuDTOS = productFacade.listSkuByIds (new ArrayList<> (skuIds),1L);
            }else {
                skuDTOS = productSkuService.querySupplySkuInfo (new ArrayList<> (skuIds));
            }
            Map<Long, ProductSkuDTO> skuDTOMap = skuDTOS.stream ().collect (Collectors.toMap (ProductSkuDTO::getId, Function.identity ()));

            map.forEach ((skuId, itemInfoResps) -> {
                ProductSkuDTO productSkuDTO = skuDTOMap.get (skuId);

                itemInfoResps.forEach (item -> {
                    ProductSkuMarketItemExportVO vo = new ProductSkuMarketItemExportVO ();
                    vo.setSupplySkuId (skuId);
                    vo.setTitle (productSkuDTO.getTitle ());
                    vo.setSpecification (productSkuDTO.getSpecification ());
                    vo.setSpecificationUnit (productSkuDTO.getSpecificationUnit ());
                    if (CollectionUtil.isNotEmpty (skuMap) && CollectionUtil.isNotEmpty (skuMap.get (vo.getSupplySkuId ()))) {
                        Set<BigDecimal> bigDecimals = skuMap.get (vo.getSupplySkuId ());
                        BigDecimal maxPrice = bigDecimals.stream ()
                                .max (BigDecimal::compareTo)
                                .get ();

                        // 获取最小值
                        BigDecimal minPrice = bigDecimals.stream ()
                                .min (BigDecimal::compareTo)
                                .get ();
                        vo.setPrice (minPrice.compareTo (maxPrice) == 0 ? String.format ("%.2f", minPrice) : String.format ("%.2f", minPrice) + "-" + String.format ("%.2f", maxPrice));
                    } else {
                        vo.setPrice ("-");
                    }
                    vo.setItemCount (itemInfoResps.size ());
                    vo.setItemId (item.getItemId ());
                    vo.setOnsaleFlag (OnSaleTypeEnum.getDesc (item.getOnSale ()));
                    vo.setItemName (item.getItemTitle ());
                    vo.setItemSpecification (item.getSpecification ());
                    vo.setItemSpecificationUnit (item.getSpecificationUnit ());
                    vo.setItemPrice (item.getPriceStr ());
                    collect.add (vo);
                });
                skuDTOMap.remove (skuId);
            });
            skuDTOMap.forEach ((skuId, productSkuDTO) -> {
                ProductSkuMarketItemExportVO vo = new ProductSkuMarketItemExportVO ();
                vo.setTitle (productSkuDTO.getTitle ());
                vo.setSpecification (productSkuDTO.getSpecification ());
                vo.setSpecificationUnit (productSkuDTO.getSpecificationUnit ());
                vo.setSupplySkuId (skuId);
                vo.setItemCount (0);
                collect.add (vo);
            });
        }
        WriteSheet summarySheet = EasyExcel.writerSheet(1).build();
        excelWriter.fill(collect, summarySheet);
    }

    private Map<Long,Set<BigDecimal>> generateProductSpuExportFileSheet0(ProductPricingSupplyQueryDTO supplyQueryDTO,String tempExcelFilePath, ExcelWriter excelWriter,Long tenantId) {
        WriteSheet writeSheet0 = EasyExcel.writerSheet(0).build();
        if (grayReleaseConfig.executeProductCenterGray(tenantId)) {
            buildSupplyExportDto (supplyQueryDTO);
        }else {
            buildSupplyExportDtoOld (supplyQueryDTO);
        }
        Map<Long,Set<BigDecimal>> ids = new ConcurrentHashMap<> ();
        ExcelLargeDataSetExporter<ProductPricingSupplyExportVO, ProductPricingSupplyExportVO> handler = new ExcelLargeDataSetExporter<ProductPricingSupplyExportVO, ProductPricingSupplyExportVO>(excelWriter, writeSheet0, tempExcelFilePath) {
            @Override
            protected List<ProductPricingSupplyExportVO> convert(ProductPricingSupplyExportVO data) {
                return Lists.newArrayList(data);
            }

            @Override
            protected void flushData(List<ProductPricingSupplyExportVO> data) {
                if (grayReleaseConfig.executeProductCenterGray(tenantId)) {
                    fillProductPricingExportList(tenantId, data);
                }else {
                    fillProductPricingExportListOld (tenantId, data);
                }
                excelWriter.fill(data, fillConfig, writeSheet0);
                Map<Long, List<ProductPricingSupplyExportVO>> skuCityCostPriceMap = data.stream().collect(Collectors.groupingBy(ProductPricingSupplyExportVO::getSupplySkuId));
                skuCityCostPriceMap.forEach ((skuId,list)->{
                    Set<BigDecimal> bigDecimals = ids.get (skuId);
                    if(CollectionUtils.isEmpty (bigDecimals)){
                        bigDecimals = new HashSet<> ();
                    }
                    Set<BigDecimal> price = list.stream ().filter (vo -> ObjectUtil.isNotNull (vo.getPrice ())).map (ProductPricingSupplyExportVO::getPrice).collect (Collectors.toSet ());
                    Set<BigDecimal> minPrice = list.stream ().filter (vo -> ObjectUtil.isNotNull (vo.getMinPrice ())).map (ProductPricingSupplyExportVO::getMinPrice).collect (Collectors.toSet ());
                    Set<BigDecimal> maxPrice = list.stream ().filter (vo -> ObjectUtil.isNotNull (vo.getMaxPrice ())).map (ProductPricingSupplyExportVO::getMaxPrice).collect (Collectors.toSet ());
                    if(CollectionUtil.isNotEmpty (price)){
                        bigDecimals.addAll (price);
                    }
                    if(CollectionUtil.isNotEmpty (minPrice)){
                        bigDecimals.addAll (minPrice);
                    }
                    if(CollectionUtil.isNotEmpty (maxPrice)){
                        bigDecimals.addAll (maxPrice);
                    }
                    if(CollectionUtil.isNotEmpty (bigDecimals)){
                        ids.put(skuId,bigDecimals);
                    }
                });
                log.info("导出数据 size=" + data.size());
            }
        };
        productPricingSupplyMapper.exportList(supplyQueryDTO, handler);
        handler.finish (false);
        return ids;
    }

    /**
     * 构建查询对象
     *
     * @param dto
     */
    private void buildSupplyExportDtoOld(ProductPricingSupplyQueryDTO dto) {
        dto.setTitle(FormatUtils.makeFuzzySearchTerm(dto.getTitle()));
        if (!StringUtils.isEmpty(dto.getBrandName()) || !StringUtils.isEmpty(dto.getTitle()) || dto.getCategoryId() != null) {
            if (dto.getCategoryId() != null) {
                // 查询类目集合
                List<CategoryVO> categoryVoS = categoryServiceFacade.queryChildCategoryList(dto.getCategoryId());
                List<Long> categoryIds = categoryVoS.stream().map(CategoryVO::getId).collect(Collectors.toList());
                dto.setCategoryIds(categoryIds);
            }

            // 品牌名称、商品名称、类目、sku
            ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO ();
            productSkuQueryConditionDTO.setTitle(dto.getTitle());
            productSkuQueryConditionDTO.setBrandName(dto.getBrandName());
            productSkuQueryConditionDTO.setCategoryIds(dto.getCategoryIds());
            List<ProductSkuDTO> skuDTOS = productSkuService.queryByConditionOld (productSkuQueryConditionDTO);
            if (CollectionUtils.isEmpty(skuDTOS)) {
                dto.setTenantId(-1L);
                return;
            }

            List<Long> supplySkuIds = skuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
            dto.setSupplySkuIds(supplySkuIds);
        }
    }

    /**
     * 构建查询对象
     *
     * @param dto
     */
    private void buildSupplyExportDto(ProductPricingSupplyQueryDTO dto) {
        if (!StringUtils.isEmpty(dto.getBrandName()) || !StringUtils.isEmpty(dto.getTitle())) {
            // 品牌名称、商品名称、类目、sku
            ProductQueryInput productSkuQueryConditionDTO = new ProductQueryInput ();
            productSkuQueryConditionDTO.setTitle(dto.getTitle());
            productSkuQueryConditionDTO.setBrandName(dto.getBrandName());
            productSkuQueryConditionDTO.setCategoryId(dto.getCategoryId ());
            List<ProductSkuDTO> skuDTOS = productFacade.listSku (productSkuQueryConditionDTO,1L);
            if (CollectionUtils.isEmpty(skuDTOS)) {
                dto.setTenantId(-1L);
                return;
            }

            List<Long> supplySkuIds = skuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
            dto.setSupplySkuIds(supplySkuIds);
        }
    }

    private void fillProductPricingExportListOld(Long tenantId,
            List<ProductPricingSupplyExportVO> productPricingSupplyVOList) {
        if (CollectionUtils.isEmpty(productPricingSupplyVOList)) {
            return;
        }
        List<Long> supplySkuIds = productPricingSupplyVOList.stream()
                .map(ProductPricingSupplyExportVO::getSupplySkuId)
                .collect(Collectors.toList());
        List<ProductSkuDTO> skuDTOS = productSkuService.querySupplySkuInfo (supplySkuIds);
        Map<Long, ProductSkuDTO> productSkuDTOMap = skuDTOS.stream()
                .collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));
        // 城市信息
        List<Long> cityIds = productPricingSupplyVOList.stream()
                .map(ProductPricingSupplyExportVO::getCityId)
                .collect(Collectors.toList());
        List<CommonLocationCityDTO> locationCityVOS = locationCityService.queryByCityIds(cityIds);
        Map<Long, String> cityMap = locationCityVOS.stream()
                .collect(Collectors.toMap(CommonLocationCityDTO::getId, CommonLocationCityDTO::getName));
        // 大客户信息
        productPricingSupplyVOList.stream().forEach(productPricingSupplyVO -> {
            if (productSkuDTOMap.containsKey(productPricingSupplyVO.getSupplySkuId())) {
                ProductSkuDTO productSkuDTO = productSkuDTOMap.get(productPricingSupplyVO.getSupplySkuId());
                productPricingSupplyVO.setTitle(productSkuDTO.getTitle());
                productPricingSupplyVO.setSpecification(productSkuDTO.getSpecification());
                productPricingSupplyVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
                String categoryName = categoryServiceFacade.queryCategoryTreeByThirdCategoryId(productSkuDTO.getCategoryId());
                productPricingSupplyVO.setCategoryName(categoryName);
                productPricingSupplyVO.setBrandName(productSkuDTO.getBrandName());
                productPricingSupplyVO.setCityName(cityMap.get(productPricingSupplyVO.getCityId()));
                productPricingSupplyVO.setSupplySku(productSkuDTO.getSku());
                if (productPricingSupplyVO.getType()
                        .equals(PriceTypeEnum.SPECIFIED_PRICE.getCode())) {
                    productPricingSupplyVO.setPriceStr(
                            productPricingSupplyVO.getPrice().toString());
                }
                productPricingSupplyVO.setSupplierName ("鲜沐");
            }
        });

        // 判断是否有按照鲜沐报价单报价
        List<ProductPricingSupplyExportVO> pricingSupplyVOS = productPricingSupplyVOList.stream()
                .filter(productPricingSupplyVO -> productPricingSupplyVO.getType()
                        .equals(PriceTypeEnum.SUMMERFATM_PRICE.getCode())).collect(Collectors.toList());

        Map<Long, SummerFarmSkuPriceInfoResp> summerfarmSkuPriceInfoDTOMap = new HashMap<Long, SummerFarmSkuPriceInfoResp> ();
        if (!CollectionUtils.isEmpty(pricingSupplyVOS)) {
            Set<Long> skuIds = pricingSupplyVOS.stream ().map (ProductPricingSupplyExportVO::getSupplySkuId).collect (Collectors.toSet ());
            List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.selectBySkuIds (new ArrayList<> (skuIds));
            Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap = productAgentSkuMappings.stream ().collect (Collectors.toMap (ProductAgentSkuMapping::getSkuId, item -> item));

            TenantDTO tenantDTO = tenantService.queryTenantById (tenantId);
            List<SummerFarmSkuPriceInfoReq> summerfarmSkuPriceInfoInputs = pricingSupplyVOS.stream ()
                    .map (productPricingSupplyVO -> {
                        SummerFarmSkuPriceInfoReq summerfarmSkuPriceInfoInput = new SummerFarmSkuPriceInfoReq ();
                        summerfarmSkuPriceInfoInput.setAdminId (tenantDTO.getAdminId ());
                        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get (productPricingSupplyVO.getSupplySkuId ());
                        summerfarmSkuPriceInfoInput.setSkuId (
                                productAgentSkuMapping.getAgentSkuId ());
                        summerfarmSkuPriceInfoInput.setCitySupplyPriceId (
                                productPricingSupplyVO.getCitySupplyPriceId ());
                        summerfarmSkuPriceInfoInput.setCityName (
                                Arrays.asList (productPricingSupplyVO.getCityName ()));
                        return summerfarmSkuPriceInfoInput;
                    }).collect (Collectors.toList ());
            // 调用接口
            try {
                List<SummerFarmSkuPriceInfoResp> summerFarmSkuPriceInfoResps = sfInterfaceServiceFacade.queryAdminSkuPricingInfo (
                        summerfarmSkuPriceInfoInputs);
                summerfarmSkuPriceInfoDTOMap = summerFarmSkuPriceInfoResps.stream ().collect (
                        Collectors.toMap (SummerFarmSkuPriceInfoResp::getCitySupplyPriceId,
                                item -> item));
            } catch (Exception e) {
                log.error ("tenantId:{} 获取鲜沐报价单失败, msg = {}", tenantId, e.getMessage (), e);
            }
        }
        Map<Long, Integer> cityCountMap = null;
        List<SkuSupplyCityCountDTO> skuSupplyCityCountDTOS = productPricingSupplyMapper.querySupplyCityCountBySkuIds (supplySkuIds, tenantId);
        if(CollectionUtil.isNotEmpty (skuSupplyCityCountDTOS)){
           cityCountMap = skuSupplyCityCountDTOS.stream ().collect (Collectors.toMap (SkuSupplyCityCountDTO::getSupplySkuId, SkuSupplyCityCountDTO::getCityCount));
        }
        // 价格
        for (ProductPricingSupplyExportVO productPricingSupplyVO : productPricingSupplyVOList) {
            if (productPricingSupplyVO.getTenantId().equals(tenantId)) {
                if (summerfarmSkuPriceInfoDTOMap.containsKey(productPricingSupplyVO.getCitySupplyPriceId())) {
                    SummerFarmSkuPriceInfoResp summerfarmSkuPriceInfoDTO = summerfarmSkuPriceInfoDTOMap.get(
                            productPricingSupplyVO.getCitySupplyPriceId());
                    if (Objects.isNull(summerfarmSkuPriceInfoDTO) || Objects.isNull(
                            summerfarmSkuPriceInfoDTO.getMinPrice())) {
                        productPricingSupplyVO.setPriceStr("-");
                    } else {
                        BigDecimal minPrice = summerfarmSkuPriceInfoDTO.getMinPrice();
                        BigDecimal maxPrice = summerfarmSkuPriceInfoDTO.getMaxPrice();
                        String minPriceStr = String.format("%.2f", minPrice);
                        String maxPriceStr = String.format("%.2f", maxPrice);
                        productPricingSupplyVO.setMinPrice (minPrice);
                        productPricingSupplyVO.setMaxPrice (maxPrice);
                        productPricingSupplyVO.setPriceStr(minPrice.compareTo(maxPrice) == 0 ? minPriceStr : minPriceStr + "-" + maxPriceStr);
                    }
                } else {
                    productPricingSupplyVO.setPriceStr(
                            Objects.isNull(productPricingSupplyVO.getPrice()) ? "-"
                                    : String.format("%.2f",productPricingSupplyVO.getPrice()));
                }
            }
            if(CollectionUtil.isNotEmpty (cityCountMap)){
                productPricingSupplyVO.setCityCount (cityCountMap.get (productPricingSupplyVO.getSupplySkuId ()));
            }
        }
    }
    private void fillProductPricingExportList(Long tenantId,
            List<ProductPricingSupplyExportVO> productPricingSupplyVOList) {
        if (CollectionUtils.isEmpty(productPricingSupplyVOList)) {
            return;
        }
        List<Long> supplySkuIds = productPricingSupplyVOList.stream()
                .map(ProductPricingSupplyExportVO::getSupplySkuId)
                .collect(Collectors.toList());
        List<ProductSkuDTO> skuDTOS = productFacade.listSkuByIds (supplySkuIds,1L);
        Map<Long, ProductSkuDTO> productSkuDTOMap = skuDTOS.stream()
                .collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));
        // 城市信息
        List<Long> cityIds = productPricingSupplyVOList.stream()
                .map(ProductPricingSupplyExportVO::getCityId)
                .collect(Collectors.toList());
        List<CommonLocationCityDTO> locationCityVOS = locationCityService.queryByCityIds(cityIds);
        Map<Long, String> cityMap = locationCityVOS.stream()
                .collect(Collectors.toMap(CommonLocationCityDTO::getId, CommonLocationCityDTO::getName));
        // 大客户信息
        productPricingSupplyVOList.stream().forEach(productPricingSupplyVO -> {
            if (productSkuDTOMap.containsKey(productPricingSupplyVO.getSupplySkuId())) {
                ProductSkuDTO productSkuDTO = productSkuDTOMap.get(productPricingSupplyVO.getSupplySkuId());
                productPricingSupplyVO.setTitle(productSkuDTO.getTitle());
                productPricingSupplyVO.setSpecification(productSkuDTO.getSpecification());
                productPricingSupplyVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
                productPricingSupplyVO.setCategoryName(productSkuDTO.getFirstCategory ()+StringConstants.LEFT_SLASH+productSkuDTO.getSecondCategory ()+StringConstants.LEFT_SLASH+productSkuDTO.getThirdCategory ());
                productPricingSupplyVO.setBrandName(productSkuDTO.getBrandName());
                productPricingSupplyVO.setCityName(cityMap.get(productPricingSupplyVO.getCityId()));
                productPricingSupplyVO.setSupplySku(productSkuDTO.getSku());
                if (productPricingSupplyVO.getType()
                        .equals(PriceTypeEnum.SPECIFIED_PRICE.getCode())) {
                    productPricingSupplyVO.setPriceStr(
                            productPricingSupplyVO.getPrice().toString());
                }
                productPricingSupplyVO.setSupplierName ("鲜沐");
            }
        });
        Map<Long, SummerFarmSkuPriceInfoResp> summerfarmSkuPriceInfoDTOMap = new HashMap<Long, SummerFarmSkuPriceInfoResp>();
        // 判断是否有按照鲜沐报价单报价
        List<ProductPricingSupplyExportVO> pricingSupplyVOS = productPricingSupplyVOList.stream()
                .filter(productPricingSupplyVO -> productPricingSupplyVO.getType()
                        .equals(PriceTypeEnum.SUMMERFATM_PRICE.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pricingSupplyVOS)) {
                TenantDTO tenantDTO = tenantService.queryTenantById (tenantId);
                List<SummerFarmSkuPriceInfoReq> summerfarmSkuPriceInfoInputs = pricingSupplyVOS.stream ()
                        .map (productPricingSupplyVO -> {
                            ProductSkuDTO productSkuDTO = productSkuDTOMap.get (productPricingSupplyVO.getSupplySkuId ());
                            if (ObjectUtil.isNotNull (productSkuDTO)) {
                                SummerFarmSkuPriceInfoReq summerfarmSkuPriceInfoInput = new SummerFarmSkuPriceInfoReq ();
                                summerfarmSkuPriceInfoInput.setAdminId (tenantDTO.getAdminId ());
                                summerfarmSkuPriceInfoInput.setSkuId (productSkuDTO.getSkuMapping ().getAgentSkuId ());
                                summerfarmSkuPriceInfoInput.setCitySupplyPriceId (productPricingSupplyVO.getCitySupplyPriceId ());
                                summerfarmSkuPriceInfoInput.setCityName (Collections.singletonList (productPricingSupplyVO.getCityName ()));
                                return summerfarmSkuPriceInfoInput;
                            } else {
                                return null;
                            }
                        }).filter (ObjectUtil::isNotNull).collect (Collectors.toList ());

                // 调用接口
                try {
                    List<SummerFarmSkuPriceInfoResp> summerFarmSkuPriceInfoResps = sfInterfaceServiceFacade.queryAdminSkuPricingInfo (
                            summerfarmSkuPriceInfoInputs);
                    summerfarmSkuPriceInfoDTOMap = summerFarmSkuPriceInfoResps.stream ().collect (
                            Collectors.toMap (SummerFarmSkuPriceInfoResp::getCitySupplyPriceId,
                                    item -> item));
                } catch (Exception e) {
                    log.error ("tenantId:{} 获取鲜沐报价单失败, msg = {}", tenantId, e.getMessage (), e);
                }
            }
            Map<Long, Integer> cityCountMap = null;
            List<SkuSupplyCityCountDTO> skuSupplyCityCountDTOS = productPricingSupplyMapper.querySupplyCityCountBySkuIds (supplySkuIds, tenantId);
            if(CollectionUtil.isNotEmpty (skuSupplyCityCountDTOS)){
               cityCountMap = skuSupplyCityCountDTOS.stream ().collect (Collectors.toMap (SkuSupplyCityCountDTO::getSupplySkuId, SkuSupplyCityCountDTO::getCityCount));
            }
            // 价格
            for (ProductPricingSupplyExportVO productPricingSupplyVO : productPricingSupplyVOList) {
                if (productPricingSupplyVO.getTenantId().equals(tenantId)) {
                    if (summerfarmSkuPriceInfoDTOMap.containsKey(productPricingSupplyVO.getCitySupplyPriceId())) {
                        SummerFarmSkuPriceInfoResp summerfarmSkuPriceInfoDTO = summerfarmSkuPriceInfoDTOMap.get(
                                productPricingSupplyVO.getCitySupplyPriceId());
                        if (Objects.isNull(summerfarmSkuPriceInfoDTO) || Objects.isNull(
                                summerfarmSkuPriceInfoDTO.getMinPrice())) {
                            productPricingSupplyVO.setPriceStr("-");
                        } else {
                            BigDecimal minPrice = summerfarmSkuPriceInfoDTO.getMinPrice();
                            BigDecimal maxPrice = summerfarmSkuPriceInfoDTO.getMaxPrice();
                            String minPriceStr = String.format("%.2f", minPrice);
                            String maxPriceStr = String.format("%.2f", maxPrice);
                            productPricingSupplyVO.setMinPrice (minPrice);
                            productPricingSupplyVO.setMaxPrice (maxPrice);
                            productPricingSupplyVO.setPriceStr(minPrice.compareTo(maxPrice) == 0 ? minPriceStr : minPriceStr + "-" + maxPriceStr);
                        }
                    } else {
                        productPricingSupplyVO.setPriceStr(
                                Objects.isNull(productPricingSupplyVO.getPrice()) ? "-"
                                        : String.format("%.2f",productPricingSupplyVO.getPrice()));
                    }
                }
                if(CollectionUtil.isNotEmpty (cityCountMap)){
                    productPricingSupplyVO.setCityCount (cityCountMap.get (productPricingSupplyVO.getSupplySkuId ()));
                }
            }
        }
}
