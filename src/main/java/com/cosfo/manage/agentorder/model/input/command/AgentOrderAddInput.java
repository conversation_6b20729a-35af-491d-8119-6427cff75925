package com.cosfo.manage.agentorder.model.input.command;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 15:01
 * @Description: 代门店下单入参
 */
@Data
public class AgentOrderAddInput implements Serializable {
    private static final long serialVersionUID = 7067846889756519716L;

    /**
     * 推荐理由
     */
    @Length(max = 100, message = "推荐理由最长100个字符")
    private String recommendReason;

    /**
     * 商品列表 不分页
     */
    @NotEmpty(message = "商品列表不可为空")
    @Size(max = 100, message = "单次下单，商品不可超过100个")
    @Valid
    private List<AgentOrderItemInput> itemInputs;

    /**
     * 门店列表 不分页
     */
    @NotEmpty(message = "门店列表不可为空")
    @Size(max = 9999, message = "单次下单，门店不可超过9999个")
    @Valid
    private List<AgentOrderStoreInput> storeInputs;

}
