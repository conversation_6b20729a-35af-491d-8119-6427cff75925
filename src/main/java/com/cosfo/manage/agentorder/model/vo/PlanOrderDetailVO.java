package com.cosfo.manage.agentorder.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:04
 * @Description:
 */
@Data
public class PlanOrderDetailVO implements Serializable {
    private static final long serialVersionUID = -7473075353023683285L;


    /**
     * 代下单编号
     */
    private String agentOrderNo;

    /**
     * 计划单ID
     */
    private Long planOrderId;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 计划单状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    private Integer planOrderStatus;

    /**
     * 商品数量（不同商品的个数）
     */
    private Integer itemCount;

    /**
     * 商品件数（不同商品个数*每件商品下单件数）
     */
    private Integer itemTotalAmount;

    /**
     * 计划单创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planOrderCreateTime;

    /**
     * 总部操作人ID
     */
    private Long agentOperatorAuthId;

    /**
     * 总部操作人名称
     */
    private String agentOperatorName;

    /**
     * 总部操作人手机号
     */
    private String agentOperatorPhone;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 是否可提醒
     */
    private boolean enableNotify;

    /**
     * 最后一次提醒时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastNotifyTime;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

}
