package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.product.model.dto.ProductMovementQueryDTO;
import com.cosfo.manage.product.model.po.ProductMovement;
import com.cosfo.manage.report.mapper.ProductMovementMapper;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
@Slf4j
public abstract class MeasureItemSalesRateStrategy implements TenantMeasureItemStrategy {

    @Resource
    private ProductMovementMapper productMovementMapper;

    protected abstract Pair<String, Integer> getTimeTagAndDimensionType();

    @Override
    public void doMeasureItem(TenantMeasureItemResult tenantMeasureItemResult) {
        Pair<String, Integer> timeTagAndDimensionType = getTimeTagAndDimensionType();
        ProductMovementQueryDTO productMovementQueryDTO = ProductMovementQueryDTO.builder()
                .tenantId(tenantMeasureItemResult.getTenantId())
                .timeTag(timeTagAndDimensionType.getKey())
                .type(timeTagAndDimensionType.getValue())
                .build();
        ProductMovement productMovement = productMovementMapper.querySummary(productMovementQueryDTO);
        String saleRateStr = "0.00";
        if (productMovement != null && productMovement.getOnSaleNum() != 0) {
            BigDecimal saleRate = NumberUtil.div(productMovement.getPaySuccessNum(), productMovement.getOnSaleNum()).setScale(NumberConstants.TWO, RoundingMode.HALF_EVEN);
            saleRateStr = saleRate.multiply(NumberConstants.ONE_HUNDRED).toPlainString();
        }

        // 商品动销率
        String itemResult = String.format("%s %s%%", tenantMeasureItemResult.getItemTitle(), saleRateStr);
        tenantMeasureItemResult.setItemResult(itemResult);
        tenantMeasureItemResult.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
