package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Service
public class MeasureItemWaitAuditStoreStrategy implements TenantMeasureItemStrategy {

    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        MerchantStorePageQueryReq merchantStorePageQueryReq = new MerchantStorePageQueryReq();
        merchantStorePageQueryReq.setTenantId(result.getTenantId());
        merchantStorePageQueryReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(1);
        PageInfo<MerchantStorePageResultResp> merchantStorePage = userCenterMerchantStoreFacade.getMerchantStorePage(merchantStorePageQueryReq, pageQueryReq);
        long waitAuditStoreCount = merchantStorePage.getTotal();
        String itemResult = String.format("%s（%s）", result.getItemTitle(), waitAuditStoreCount);
        result.setItemResult(itemResult);
        result.setItemResultState(waitAuditStoreCount > 0 ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
