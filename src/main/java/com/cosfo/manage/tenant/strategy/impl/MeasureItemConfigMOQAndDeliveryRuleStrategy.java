package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.merchant.dao.MerchantOrderQuantityRuleDao;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigMOQAndDeliveryRuleStrategy implements TenantMeasureItemStrategy {

    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;
    @Resource
    private MerchantOrderQuantityRuleDao merchantOrderQuantityRuleDao;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        List<MerchantDeliveryFeeRule> merchantDeliveryFeeRules = merchantDeliveryFeeRuleMapper.listAll(result.getTenantId());
        List<MerchantOrderQuantityRule> merchantOrderQuantityRules = merchantOrderQuantityRuleDao.listOrderQuantityRule(result.getTenantId());
        boolean hasConfigured = (merchantDeliveryFeeRules != null && !merchantDeliveryFeeRules.isEmpty()) && (merchantOrderQuantityRules != null && !merchantOrderQuantityRules.isEmpty());
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(hasConfigured ? TenantConfigStatusEnum.NORMAL.getStatus() : TenantConfigStatusEnum.ABNORMAL.getStatus());
    }
}
