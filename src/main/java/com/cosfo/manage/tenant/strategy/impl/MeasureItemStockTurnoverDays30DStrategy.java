package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-11-23
 **/
@Service("measureItemStockTurnoverDays30DStrategy")
public class MeasureItemStockTurnoverDays30DStrategy implements TenantMeasureItemStrategy {

    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(result.getTenantId(), timeTag);
        String stockTurnoverDays = Objects.isNull(tenantMetricsSummary.getStockTurnoverDays30d()) ? StringConstants.SEPARATING_IN_LINE : tenantMetricsSummary.getStockTurnoverDays30d().toPlainString();
        String itemResult = String.format("%s%s天", result.getItemTitle(), stockTurnoverDays);
        result.setItemResult(itemResult);
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
