package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigAfterSaleRuleStrategy implements TenantMeasureItemStrategy {

//    @DubboReference
//    private OrderAfterSaleRuleService orderAfterSaleRuleService;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
//        List<OrderAfterSaleRuleDTO> orderAfterSaleRules = RpcResultUtil.handle(orderAfterSaleRuleService.queryByTenantId(result.getTenantId()));
//        boolean configFlag = orderAfterSaleRules == null || orderAfterSaleRules.isEmpty();
//        result.setItemResult(configFlag ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
        // 有默认值，不需要检查
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
