package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.TimeDimensionEnum;
import com.cosfo.manage.report.mapper.ProductMovementMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
@Slf4j
public class MeasureItemSalesRateMonthStrategy extends MeasureItemSalesRateStrategy {

    @Resource
    private ProductMovementMapper productMovementMapper;

    @Override
    protected Pair<String, Integer> getTimeTagAndDimensionType() {
        return Pair.of(DateUtil.beginOfMonth(DateUtil.date()).toString("yyyyMMdd"), TimeDimensionEnum.MONTH.getType());
    }
}
