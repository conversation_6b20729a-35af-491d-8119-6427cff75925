package com.cosfo.manage.tenant.service.impl;


import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.report.model.dto.StockTurnoverSummaryExcelDTO;
import com.cosfo.manage.report.model.po.StockTurnoverSummary;
import com.cosfo.manage.report.repository.StockTurnoverSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TenantStockTurnoverDaysExcelGenerator implements ExcelGenerator<Void> {

    @Resource
    private StockTurnoverSummaryRepository stockTurnoverSummaryRepository;
    @Resource
    private CommonService commonService;
    @Resource
    private ProductFacade productFacade;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("近30天库存周转天数截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        String timeTag = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<StockTurnoverSummaryExcelDTO> stockTurnoverSummaries = queryStockTurnoverDays(tenantId, timeTag);
        return commonService.exportExcel(stockTurnoverSummaries, ExcelTypeEnum.STOCK_TURNOVER_DAYS.getName());
    }

    private List<StockTurnoverSummaryExcelDTO> queryStockTurnoverDays(Long tenantId, String timeTag) {
        List<StockTurnoverSummary> stockTurnoverSummaries = stockTurnoverSummaryRepository.listByTenantAndTimeTag(tenantId, timeTag);
        if (CollectionUtils.isEmpty(stockTurnoverSummaries)) {
            return Collections.emptyList();
        }

        List<Long> skuIds = stockTurnoverSummaries.stream().map(StockTurnoverSummary::getSkuId).collect(Collectors.toList());
        List<ProductSkuDTO> productSkuList = productFacade.listSkuByIds(skuIds, tenantId);
        if (CollectionUtils.isEmpty(productSkuList)) {
            return Collections.emptyList();
        }
        Map<Long, ProductSkuDTO> skuInfoMap = productSkuList.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item, (item1, item2) -> item1));
        ProductSkuDTO emptyProductSkuDTO = new ProductSkuDTO();
        return stockTurnoverSummaries.stream().map(item -> {
            ProductSkuDTO skuInfo = skuInfoMap.getOrDefault(item.getSkuId(), emptyProductSkuDTO);
            StockTurnoverSummaryExcelDTO stockTurnoverSummaryExcelDTO = new StockTurnoverSummaryExcelDTO();
            stockTurnoverSummaryExcelDTO.setSkuId(item.getSkuId());
            stockTurnoverSummaryExcelDTO.setTitle(skuInfo.getTitle());
            stockTurnoverSummaryExcelDTO.setSpecification(Global.subSpecification(skuInfo.getSpecification()));
            stockTurnoverSummaryExcelDTO.setSpecificationUnit(skuInfo.getSpecificationUnit());
            stockTurnoverSummaryExcelDTO.setFirstCategory(skuInfo.getFirstCategory());
            stockTurnoverSummaryExcelDTO.setSecondCategory(skuInfo.getSecondCategory());
            stockTurnoverSummaryExcelDTO.setThirdCategory(skuInfo.getThirdCategory());
            stockTurnoverSummaryExcelDTO.setWarehouseName(item.getWarehouseName());
            stockTurnoverSummaryExcelDTO.setStockTurnoverDays(item.getTurnoverDays());
            return stockTurnoverSummaryExcelDTO;
        }).collect(Collectors.toList());
    }
}
