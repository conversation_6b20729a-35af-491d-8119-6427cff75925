package com.cosfo.manage.tenant.convert;

import com.cosfo.manage.tenant.model.po.TenantCompany;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Mapper
public interface TenantCompanyMapperConvert {

    TenantCompanyMapperConvert INSTANCE = Mappers.getMapper(TenantCompanyMapperConvert.class);

    /**
     * resp to company
     *
     * @param resp
     * @return
     */
    @Mapping(target = "createTime", expression = "java(com.cosfo.common.util.TimeUtils.localDateTimeConvertDate(resp.getCreateTime()))")
    @Mapping(target = "updateTime", expression = "java(com.cosfo.common.util.TimeUtils.localDateTimeConvertDate(resp.getUpdateTime()))")
    TenantCompany respToTenantCompany(BusinessInformationResultResp resp);
}
