package com.cosfo.manage.tenant.repository;

import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 度量项结果 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface TenantMeasureItemResultRepository extends IService<TenantMeasureItemResult> {

    /**
     * 根据度量报告id查询
     * @param tenantId
     * @param reportId
     * @return
     */
    List<TenantMeasureItemResult> listByReportId(Long tenantId, Long reportId);

    /**
     * 根据条件查询
     * @param tenantId
     * @param id
     * @param itemType
     * @param itemResultState
     * @return
     */
    List<TenantMeasureItemResult> listByCondition(Long tenantId, Long id, Integer itemType, String itemResultState);
}
