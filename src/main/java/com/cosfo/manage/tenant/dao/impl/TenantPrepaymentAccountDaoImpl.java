package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.bill.model.dto.PrepaymentAccountQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentAmountVO;
import com.cosfo.manage.common.context.prepay.TenantPrepayPayableTargetEnum;
import com.cosfo.manage.tenant.dao.TenantPrepaymentAccountDao;
import com.cosfo.manage.tenant.mapper.TenantPrepaymentAccountMapper;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentDailySnapshot;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 预付账户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Slf4j
@Service
public class TenantPrepaymentAccountDaoImpl extends ServiceImpl<TenantPrepaymentAccountMapper, TenantPrepaymentAccount> implements TenantPrepaymentAccountDao {
    @Override
    public PrepaymentAmountVO queryTotalAmount(Long tenantId) {
        List<TenantPrepaymentAccount> tenantPrepaymentAccounts = baseMapper.calAccountSumGroupByPayTargetType(tenantId);
        PrepaymentAmountVO amountVO = new PrepaymentAmountVO();
        if (CollectionUtils.isEmpty(tenantPrepaymentAccounts)) {
            return amountVO;
        }
        Map<Integer, TenantPrepaymentAccount> sumMap = tenantPrepaymentAccounts.stream().collect(Collectors.toMap(TenantPrepaymentAccount::getPayableTarget, t -> t));
        sumMap.forEach((target, sumInfo) -> {
            BigDecimal availableAmount = sumInfo.getAvailableAmount();
            amountVO.setTotalAmount(amountVO.getTotalAmount().add(availableAmount));
            TenantPrepayPayableTargetEnum targetEnum = TenantPrepayPayableTargetEnum.valueOf(target);
            switch (Objects.requireNonNull(targetEnum)) {
                case DIRECT_SUPPLY:
                    amountVO.setProductAmount(availableAmount);
                    break;
                case AGENT_WAREHOUSE_EXPENSE:
                    amountVO.setAgentAmount(availableAmount);
                    break;
                case DIRECT_SUPPLY_AND_AGENT:
                    amountVO.setCommonAmount(availableAmount);
                    break;
                default:
            }
        });
        return amountVO;
    }

    @Override
    public List<TenantPrepaymentAccount> queryAccountList(PrepaymentAccountQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(TenantPrepaymentAccount::getTenantId, queryDTO.getTenantId());
        accountLambdaQueryWrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentAccount::getLastChangeTime, TenantPrepaymentAccount::getId));
        return list(accountLambdaQueryWrapper);
    }

    @Override
    public Page<TenantPrepaymentAccount> queryAccountPage(PrepaymentAccountQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(TenantPrepaymentAccount::getTenantId, queryDTO.getTenantId());
        accountLambdaQueryWrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentAccount::getLastChangeTime, TenantPrepaymentAccount::getId));
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), accountLambdaQueryWrapper);
    }

    @Override
    public List<TenantPrepaymentAccount> calAccount() {
        QueryWrapper<TenantPrepaymentAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("tenant_id as tenantId, sum(ifnull(available_amount,0)) as totalAmount");
        queryWrapper.groupBy("tenant_id");
        List<TenantPrepaymentAccount> list = list(queryWrapper);
        return list;
    }

//    @Override
//    public List<Long> getAllTenantId() {
//        QueryWrapper<TenantPrepaymentAccount> queryWrapper = new QueryWrapper<>();
//        queryWrapper.select("DISTINCT tenant_id");
//        return listObjs(queryWrapper, t -> (Long) t);
//    }
}
