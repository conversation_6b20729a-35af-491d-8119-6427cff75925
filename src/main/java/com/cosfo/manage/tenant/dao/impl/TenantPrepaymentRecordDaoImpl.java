package com.cosfo.manage.tenant.dao.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.bill.converter.PrepaymentRecordMapper;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTotalVO;
import com.cosfo.manage.common.context.prepay.TenantPrepayRecordStatusEnum;
import com.cosfo.manage.common.context.prepay.TenantPrepayRecordTypeEnum;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.dao.TenantPrepaymentRecordDao;
import com.cosfo.manage.tenant.mapper.TenantPrepaymentRecordMapper;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentRecord;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预付记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class TenantPrepaymentRecordDaoImpl extends ServiceImpl<TenantPrepaymentRecordMapper, TenantPrepaymentRecord> implements TenantPrepaymentRecordDao {

    @Override
    public Long addPrepaymentRecord(PrepaymentRecordDTO dto) {
        TenantPrepaymentRecord tenantPrepaymentRecord = PrepaymentRecordMapper.INSTANCE.recordDTOToRecord(dto);
        tenantPrepaymentRecord.setCreatorId(UserLoginContextUtils.getAuthUserId());
        tenantPrepaymentRecord.setId(IdUtil.getSnowflakeNextId());
        this.save(tenantPrepaymentRecord);
        return tenantPrepaymentRecord.getId();
    }

    @Override
    public TenantPrepaymentRecord getPrepaymentRecord(Long id) {
        return this.getById(id);
    }

    @Override
    public Page<TenantPrepaymentRecord> getPrepaymentRecordPage(PrepaymentRecordQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentRecord> queryWrapper = buildRecordQueryWrapper(null, queryDTO);
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), queryWrapper);
    }

    @Override
    public List<TenantPrepaymentRecord> getPrepaymentRecordList(PrepaymentRecordQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentRecord> queryWrapper = buildRecordQueryWrapper(null, queryDTO);
        return list(queryWrapper);
    }

    private static LambdaQueryWrapper<TenantPrepaymentRecord> buildRecordQueryWrapper(LambdaQueryWrapper<TenantPrepaymentRecord> lambdaWrapper, PrepaymentRecordQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentRecord> queryWrapper = lambdaWrapper == null ? new LambdaQueryWrapper<>() : lambdaWrapper;
        if (queryDTO.getDateStart() != null && queryDTO.getDateEnd() != null) {
//            queryWrapper.between(TenantPrepaymentRecord::getPayTime, queryDTO.getDateStart(), queryDTO.getDateEnd().plusDays(1));
            queryWrapper.between(TenantPrepaymentRecord::getPayTime, DateUtil.startOfDay(queryDTO.getDateStart()), DateUtil.endOfDay(queryDTO.getDateEnd()));
        }
        // 区分预付和预收
        // 预收只有 status = 1 的才展示
        queryWrapper.eq(TenantPrepaymentRecord::getTenantId, queryDTO.getTenantId());
        queryWrapper.eq(queryDTO.getCreatorId() != null, TenantPrepaymentRecord::getCreatorId, queryDTO.getCreatorId());
        queryWrapper.eq(queryDTO.getSupplierTenantId() != null, TenantPrepaymentRecord::getSupplierTenantId, queryDTO.getSupplierTenantId());
        queryWrapper.eq(queryDTO.getType() != null, TenantPrepaymentRecord::getPayableTarget, queryDTO.getType());

        // 所有状态或者审核通过
//        if (queryDTO.getStatus() == null || queryDTO.getStatus() == 1) {
//            queryWrapper.eq(TenantPrepaymentRecord::getTransactionType, 0).eq(TenantPrepaymentRecord::getAuditStatus, 1);
//            queryWrapper.or().eq(TenantPrepaymentRecord::getTransactionType, 0).eq(TenantPrepaymentRecord::getAuditStatus, 1);
//        } else {
//
//        }
        queryWrapper.eq(queryDTO.getStatus() != null, TenantPrepaymentRecord::getAuditStatus, queryDTO.getStatus());
        queryWrapper.eq(queryDTO.getTransactionType() != null, TenantPrepaymentRecord::getTransactionType, queryDTO.getTransactionType());
        queryWrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentRecord::getPayTime, TenantPrepaymentRecord::getId));
        return queryWrapper;
    }

    @Override
    public PrepaymentRecordTotalVO queryPrepaymentTotal(PrepaymentRecordQueryDTO queryDTO) {
        PrepaymentRecordTotalVO prepaymentRecordTotalVO = queryTotalByTypeList(queryDTO, Lists.newArrayList(TenantPrepayRecordTypeEnum.PREPAY.getRecordType(), TenantPrepayRecordTypeEnum.PREPAY_SUPPLIER.getRecordType()));
        PrepaymentRecordTotalVO returnTotal = queryTotalByTypeList(queryDTO, Lists.newArrayList(TenantPrepayRecordTypeEnum.PREPAY_REFUND.getRecordType()));

        prepaymentRecordTotalVO.setSuccessAmount(prepaymentRecordTotalVO.getSuccessAmount().add(returnTotal.getSuccessAmount().negate()));
        prepaymentRecordTotalVO.setAuditAmount(prepaymentRecordTotalVO.getAuditAmount().add(returnTotal.getAuditAmount().negate()));
        prepaymentRecordTotalVO.setFailAmount(prepaymentRecordTotalVO.getFailAmount().add(returnTotal.getFailAmount().negate()));
        return prepaymentRecordTotalVO;
    }

    private PrepaymentRecordTotalVO queryTotalByTypeList(PrepaymentRecordQueryDTO queryDTO, List<Integer> transactionTypeList) {
        QueryWrapper<TenantPrepaymentRecord> prepaymentRecordQueryWrapper = new QueryWrapper<>();
        prepaymentRecordQueryWrapper.select("audit_status as auditStatus, sum(ifnull(transaction_amount, 0)) AS amount");
        LambdaQueryWrapper<TenantPrepaymentRecord> queryWrapper = buildRecordQueryWrapper(prepaymentRecordQueryWrapper.lambda(), queryDTO);
        queryWrapper.in(TenantPrepaymentRecord::getTransactionType, transactionTypeList);
        queryWrapper.groupBy(TenantPrepaymentRecord::getAuditStatus);
        List<Map<String, Object>> result = listMaps(queryWrapper);
        PrepaymentRecordTotalVO prepaymentRecordTotalVO = new PrepaymentRecordTotalVO();
        if (CollectionUtils.isEmpty(result)) {
            return prepaymentRecordTotalVO;
        }
        result.forEach(map -> {
            Integer auditStatus = (Integer) map.get("auditStatus");
            TenantPrepayRecordStatusEnum statusEnum = TenantPrepayRecordStatusEnum.fromStatus(auditStatus);
            BigDecimal amount = (BigDecimal) map.getOrDefault("amount", BigDecimal.ZERO);
            switch (statusEnum) {
                case AUDIT_SUCCESS:
                    prepaymentRecordTotalVO.setSuccessAmount(amount);
                    break;
                case AUDIT_FAIL:
                    prepaymentRecordTotalVO.setFailAmount(amount);
                    break;
                case WAIT_AUDIT:
                    prepaymentRecordTotalVO.setAuditAmount(amount);
                    break;
                default:
            }
        });
        return prepaymentRecordTotalVO;
    }
}
