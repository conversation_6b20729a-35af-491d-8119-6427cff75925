package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowSchemeDetailVO {


    /**
     * 方案编码
     */
    private Long schemeId;

    /**
     * 方案名称
     */
    private String schemeName;

    /**
     * 方案类型
     */
    private Integer schemeType;

    /**
     * 是否默认流程方案0:例外方案;1:默认方案
     */
    private Integer defaultFlag;

    /**
     * 例外方案门店id
     */
    private List<Long> storeIdList;

    /**
     * 例外方案门店信息
     */
    private List<FlowSchemeStoreDTO> storeDTOList;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 流程审核配置
     */
    private List<FlowRuleAuditDTO> flowRuleAuditDTOList;

    @Data
    public static class FlowSchemeStoreDTO {
        /**
         * 门店id
         */
        private Long storeId;
        /**
         * 门店编号
         */
        private String storeNo;
        /**
         * 门店名称
         */
        private String storeName;

        /**
         * 门店类型：0、直营店 1、加盟店 2、托管店
         */
        private Integer type;

    }

}
