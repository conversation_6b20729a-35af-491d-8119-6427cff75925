package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cosfo.manage.common.config.SetLongHandler;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <p>
 * 方案流程审核项配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@TableName(value = "tenant_flow_rule_audit", autoResultMap = true)
public class TenantFlowRuleAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 流程方案id，tenant_flow_scheme表id
     */
    @TableField("flow_scheme_id")
    private Long flowSchemeId;

    /**
     * 流程审核业务类型 1-订单审核 2-无仓自营配送前售后审核 3-代仓品配送前售后审核 4-代仓品配送后售后审核
     */
    @TableField("biz_type")
    private Integer bizType;

    /**
     * 审核开启标识 0 关闭 1 开启
     */
    @TableField("switch_flag")
    private Integer switchFlag;

    /**
     * 审核人员账号ID列表
     */
    @TableField(value = "audit_account_ids", typeHandler = SetLongHandler.class)
    private Set<Long> auditAccountIds;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
