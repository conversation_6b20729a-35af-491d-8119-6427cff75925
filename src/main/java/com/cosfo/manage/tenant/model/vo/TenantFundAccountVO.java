package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/4/21 11:31
 * @PackageName:com.cosfo.manage.tenant.model.vo
 * @ClassName: TenantFundAccountVO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class TenantFundAccountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 配置ID（tenant_fund_account_config.id）
     */
    private Long configId;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
