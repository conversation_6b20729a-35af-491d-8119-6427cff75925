package com.cosfo.manage.tenant.model.input;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/26  15:05
 */
@Data
public class TenantBillInput {
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 页面大小
     */
    private Integer pageSize;
    /**
     * 收支类型
     */
    private Integer type;
    /**
     * 起始时间
     */
    private String startTime;
    /**
     * 截止时间
     */
    private String endTime;
    /**
     * 支付类型1在线支付2账期
     */
    private Integer paymentType;
    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 交易对象名称，帆台内门店名称
     */
    private String tradingObject;
    /**
     * 门店id列表
     */
    private List<Long> storeIds;
    /**
     * 订单编号
     */
    private String recordNo;

    /**
     * 订单编号数组
     */
    private List<String> recordNos;
    /**
     * 帆台交易流水号
     */
    private String billNo;
    /**
     * 银行流水号
     */
    private String bankOrderId;
    /**
     * 交易类型
     * 0订单(消费)1售后(退款)
     *
     */
    private Integer exchangeType;

    /**
     * 交易类型查询描述
     */
    private String exchangeTypeDesc;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 支付方式接收参数 1、在线支付2、账期 3、余额 4、支付宝 5、0元购 6、线下支付 7、非现金支付
     */
    private Integer actualType;
}
