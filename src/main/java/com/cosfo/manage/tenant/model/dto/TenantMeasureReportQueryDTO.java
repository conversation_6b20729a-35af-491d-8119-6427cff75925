package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-09
 **/
@Data
public class TenantMeasureReportQueryDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 度量项类型
     * @see com.cosfo.manage.common.context.MeasureItemTypeEnum
     */
    private List<Integer> itemTypes;

    /**
     * 度量项类型
     */
    private Integer itemType;

    /**
     * 度量项结果状态
     * @see com.cosfo.manage.common.context.TenantConfigStatusEnum
     */
    private String itemResultState;

    /**
     * 记录id
     */
    private Long reportId;
}
