package com.cosfo.manage.tenant.model.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
* @description: 租户资金账户
* @author: <PERSON>
* @date: 2025-04-21
**/
@Data
public class TenantFundAccount {
    /**
    * primary key
    */
    private Long id;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 账户名称
    */
    private String accountName;

    /**
    * 配置ID（tenant_fund_account_config.id）
    */
    private Long configId;

    /**
    * 更新人ID
    */
    private Long updateUserId;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;
}