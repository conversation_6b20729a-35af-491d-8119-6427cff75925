package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: xiaowk
 * @time: 2023/12/25 下午2:04
 */
@Data
public class FlowRuleAuditDTO {

    private Long id;
    /**
     * 流程审核业务类型 1-订单审核 2-无仓自营配送前售后审核 3-代仓品配送前售后审核 4-代仓品配送后售后审核
     */
    private Integer bizType;

    /**
     * 审核开启标识 0 关闭 1 开启
     * false-关闭 true-开启
     */
    private Boolean switchFlag;

    /**
     * 审核人员账号ID列表
     */
    private List<Long> auditAccountIds;

    /**
     * 审核人员账号信息列表
     */
    private List<TenantAccountVO> auditAccountList;
}
