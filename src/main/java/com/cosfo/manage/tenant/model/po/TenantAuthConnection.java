package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * tenant_auth_connection
 * <AUTHOR>
@Data
public class TenantAuthConnection implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 授权人APPID
     */
    private String appId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 1-正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 汇付商户号
     */
    private String huifuId;

    /**
     * 汇付秘钥
     */
    private String secretKey;

    /**
     * 商户公钥
     */
    private String publicKey;

    /**
     * 汇付公钥
     */
    private String huifuPublicKey;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 上次同步结算信息时间
     */
    private Date syncTime;

    private static final long serialVersionUID = 1L;
}