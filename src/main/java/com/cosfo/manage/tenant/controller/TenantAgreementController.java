package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.tenant.service.TenantAgreementService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/28 15:21
 */
@RestController
@RequestMapping("/tenantAgreement")
public class TenantAgreementController {

    @Resource
    private TenantAgreementService tenantAgreementService;

    /**
     * 租户协议
     * @param type
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public ResultDTO listAll(Integer type) {
        return tenantAgreementService.listAll(type);
    }
}
