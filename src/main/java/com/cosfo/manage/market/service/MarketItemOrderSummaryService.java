package com.cosfo.manage.market.service;

import com.cosfo.manage.bill.model.po.Payment;

import java.time.LocalDateTime;

public interface MarketItemOrderSummaryService {

    /**
     * 生成新的记录
     *
     * @param payment 支付单
     */
    void generateMarketItemOrderSummaryByPayment(Payment payment);

    /**
     * 初始化数据
     */
    void initMarketItemOrderSummary(LocalDateTime startDateTime, LocalDateTime endDateTime);
}
