package com.cosfo.manage.market.service.impl;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:16
 */
//@Service
//public class MarketAreaItemServiceImpl implements MarketAreaItemService {
//
//    @Resource
//    private MarketAreaItemMapper marketAreaItemMapper;
//    @Resource
//    private MarketAreaItemRepository marketAreaItemRepository;
//    @Resource
//    private MarketAreaItemMappingService marketAreaItemMappingService;
//
//    @Override
//    public void insert(MarketAreaItem areaItem) {
//        marketAreaItemMapper.insert(areaItem);
//    }
//
//    @Override
//    public MarketAreaItem selectByTenantAndSkuId(Long tenantId, Long skuId) {
//        return marketAreaItemMapper.selectByTenantAndSkuId(tenantId, skuId);
//    }
//
//    @Override
//    public void updateByPrimaryKeySelective(MarketAreaItem update) {
//        marketAreaItemMapper.updateByPrimaryKeySelective(update);
//    }
//
//    @Override
//    public MarketAreaItemDTO queryByTenantIdAndSkuId(Long tenantId, Long skuId) {
//        return marketAreaItemMapper.queryByTenantAndSkuId(tenantId,skuId);
//    }
//
//}
