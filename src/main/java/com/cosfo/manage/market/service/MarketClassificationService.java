package com.cosfo.manage.market.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationQueryDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationTreeDTO;
import com.cosfo.manage.market.model.po.MarketClassification;
import com.cosfo.manage.market.model.vo.MarketClassificationVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 9:52
 */
public interface MarketClassificationService {

    /**
     * 新增分类
     * @param classificationDTO
     * @param contextInfoDTO
     * @return
     */
    ResultDTO saveClassification(MarketClassificationDTO classificationDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 创建租户的默认一级、二级分类
     * @param tenantId
     * @return 返回二级分类id
     */
    Long createDefaultClassification(Long tenantId);

    /**
     * 分类树
     * @param tenantId
     * @return
     */
    ResultDTO selectClassificationTree(Long tenantId);

    /**
     * 删除分类
     * @param id
     * @return
     */
    ResultDTO deleteClassification(Long id, LoginContextInfoDTO contextInfoDTO);

    /**
     * 更新分类
     * @param marketClassificationDTO
     * @return
     */
    ResultDTO updateClassification(MarketClassificationDTO marketClassificationDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 排序
     * @param marketClassificationTreeDTOList
     * @return
     */
    ResultDTO sort(List<MarketClassificationTreeDTO> marketClassificationTreeDTOList, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 根据分类id查询
     * @param id
     * @return
     */
    ResultDTO listAllById(Long id);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    MarketClassification selectByPrimaryKey(Long id);

    /**
     * 根据父级分类id和名称查询
     * @param tenantId
     * @param parentId
     * @param name
     * @return
     */
    MarketClassification selectByParentIdAndName(Long tenantId, Long parentId, String name);

    /**
     * 根据itemId查询分类
     * @param tenantId
     * @param marketId
     * @return
     */
    MarketClassification selectByItemId(Long tenantId, Long marketId);

    /**
     * 查询整个分类层级
     * @param tenantId
     * @param marketId
     * @return
     */
    MarketClassificationDTO selectWholeClassification(Long tenantId, Long marketId);

    /**
     * 根据分组id查询子类分组
     * @param id
     * @return
     */
    List<MarketClassification> queryChildList(Long id);

    /**
     * 通过classificationIds查询商品分组树
     *
     * @param marketClassificationQueryDTO
     * @return
     */
    List<MarketClassificationVO> queryByClassificationIds(MarketClassificationQueryDTO marketClassificationQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 前台分类map《2级分类name/1级分类name，2级别分类id》
     * @param tenantId
     * @return
     */
    Map<String, Long> getClassificationMap(Long tenantId);
}
