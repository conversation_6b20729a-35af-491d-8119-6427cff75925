package com.cosfo.manage.market.converter;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.PriceStrategyTypeEnum;
import com.cosfo.manage.market.model.dto.MarketItemUnfairPriceStrategyDTO;
import com.cosfo.manage.market.model.dto.MarketAreaItemMappingInput;

import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.MarketItemUnitTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.page.PageResp;
import com.cosfo.manage.common.constant.PriceStrategyConstants;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.*;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 18:24
 * @Description:
 */
@Mapper
public interface MarketFacadeConvert {
    MarketFacadeConvert INSTANCE = Mappers.getMapper(MarketFacadeConvert.class);

    @Mapping(source = "id", target = "itemId")
    @Mapping(source = "title", target = "itemTitle")
    MarketItemCommonQueryReq convert2Query(MarketItemQueryDTO dto);

    PageInfo<MarketItemInfoDTO> convert2ItemDtoPage(PageResp<MarketItemInfoResp> pageInfo);

    @Mapping(source = "resp", target = "marketAreaItemMappingVos", qualifiedByName = "convert2AreaMappingList")
    MarketItemInfoDTO convert2ItemDto(MarketItemInfoResp resp);

    CombineMarketQueryInputReq convert2CombineQueryReq(CombineMarketQueryDTO dto);

    PageInfo<CombineMarketListDTO> convert2CombinePage(PageResp<CombineMarketListResp> pageResp);

    CombineMarketDetailDTO convert2CombineDetail(CombineMarketDetailResp resp);

    CombineAddReq convert2CombineReq(CombineInputDTO dto);

    MarketSpuVO convert2MarketSpuVo(MarketDetailResp resp);

    @Mapping(source = "id", target = "marketId")
    @Mapping(source = "marketItemInput", target = "marketItemInputReq")
    MarketInputReq convert2InputReq(MarketSpuInput input);

    MarketAddResDTO convert2Res(AddMarketResp resp);

    @Mapping(source = "pageIndex", target = "pageNum")
    MarketQueryReq convert2QueryReq(MarketQueryInput queryInput);

    PageInfo<MarketSpuVO> convert2MarketPage(PageResp<MarketResp> pageResp);

    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingUnit" ,qualifiedByName = "getStoreOrderingUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryUnit" ,qualifiedByName = "getStoreInventoryUnit")
    @Mapping(source = "marketItemUnitList", target = "storeCostUnit" ,qualifiedByName = "getStoreCostUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryCostUnitMultiple" ,qualifiedByName = "getStoreInventoryCostUnitMultiple")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingInventoryUnitMultiple" ,qualifiedByName = "getStoreOrderingInventoryUnitMultiple")
    MarketItemVO convert2MarketItemVO(MarketItemInfoResp resp);

    @Named("getStoreOrderingUnit")
    static String getStoreOrderingUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }
    @Named("getStoreInventoryUnit")
    static String getStoreInventoryUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }
    @Named("getStoreCostUnit")
    static String getStoreCostUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_COST_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }

    @Named("getUnitByEnum")
    static MarketItemUnitResp getUnitByEnum(List<MarketItemUnitResp> marketItemUnitList,MarketItemUnitTypeEnum storeOrderingUnit) {
        if(CollectionUtil.isEmpty (marketItemUnitList)){
            return null;
        }
        Optional<MarketItemUnitResp> first = marketItemUnitList.stream ().filter (e -> storeOrderingUnit.getCode ().equals (e.getUnitType ())).findFirst ();
        if(first.isPresent ()){
          return first.get ();
        }
        return null;
    }

    @Named("getStoreInventoryCostUnitMultiple")
    static BigDecimal getStoreInventoryCostUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp costUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_COST_UNIT);
        if(inventoryUnit!=null && costUnit!=null){
            return costUnit.getStoreOrderingUnitMultiple ().divide (inventoryUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
    @Named("getStoreOrderingInventoryUnitMultiple")
    static BigDecimal getStoreOrderingInventoryUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp orderingUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT);
        if(inventoryUnit!=null && orderingUnit !=null){
            return inventoryUnit.getStoreOrderingUnitMultiple ().divide (orderingUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }

    @Mapping(source = "resp", target = "marketAreaItemMappingVos", qualifiedByName = "convert2AreaMappingList")
    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "stockAmount", target = "amount")
    @Mapping(source = "unfairPriceStrategyResp", target = "marketItemUnfairPriceStrategyDTO" ,qualifiedByName = "convertToMarketItemUnfairPriceStrategyDTO")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingUnit" ,qualifiedByName = "getStoreOrderingUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryUnit" ,qualifiedByName = "getStoreInventoryUnit")
    @Mapping(source = "marketItemUnitList", target = "storeCostUnit" ,qualifiedByName = "getStoreCostUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryCostUnitMultiple" ,qualifiedByName = "getStoreInventoryCostUnitMultiple")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingInventoryUnitMultiple" ,qualifiedByName = "getStoreOrderingInventoryUnitMultiple")
    MarketItemDTO convert2MarketItemDto(MarketItemInfoResp resp);

    @Mapping(source = "itemInput", target = "priceInputs", qualifiedByName = "convert2PriceStrategyList")
    @Mapping(source = "id", target = "marketItemId")
    @Mapping(source = "marketItemUnfairPriceStrategyDTO", target = "marketItemUnfairPriceStrategyReq")
    @Mapping(source = "itemInput", target = "marketItemUnitList" ,qualifiedByName = "getMarketItemUnitList")
    MarketItemInputReq convert2Req(MarketItemInput itemInput);

    @Named("getMarketItemUnitList")
    static List<MarketItemUnitReq> getMarketItemUnitList(MarketItemInput itemInput){
        List<MarketItemUnitReq> marketItemUnitList = new ArrayList<> ();

        Long tenantId = itemInput.getTenantId ();
        Long itemId = itemInput.getId ();

        String orderingUnit = itemInput.getStoreOrderingUnit();

        BigDecimal inventoryCostUnitMultiple = itemInput.getStoreInventoryCostUnitMultiple();
        String inventoryUnit = itemInput.getStoreInventoryUnit();
        BigDecimal orderingInventoryUnitMultiple = itemInput.getStoreOrderingInventoryUnitMultiple();
        String costUnit = itemInput.getStoreCostUnit();

        if(StringUtils.isNotBlank (orderingUnit)){
            MarketItemUnitReq marketItemUnitReq = buildMarketItemUnitReq (tenantId, itemId);
            marketItemUnitReq.setUnitDesc(orderingUnit);
            marketItemUnitReq.setUnitType(MarketItemUnitTypeEnum.STORE_ORDERING_UNIT.getCode ());
            marketItemUnitReq.setStoreOrderingUnitMultiple(new BigDecimal (1));
            marketItemUnitList.add (marketItemUnitReq);
        }
        if(StringUtils.isNotBlank (inventoryUnit)){
            MarketItemUnitReq marketItemUnitReq = buildMarketItemUnitReq (tenantId, itemId);
            marketItemUnitReq.setUnitDesc(inventoryUnit);
            marketItemUnitReq.setUnitType(MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT.getCode ());
            marketItemUnitReq.setStoreOrderingUnitMultiple(orderingInventoryUnitMultiple);
            marketItemUnitList.add (marketItemUnitReq);
        }
        if(StringUtils.isNotBlank (costUnit) && ObjectUtil.isNotNull (orderingInventoryUnitMultiple) && ObjectUtil.isNotNull (inventoryCostUnitMultiple)){
            MarketItemUnitReq marketItemUnitReq = buildMarketItemUnitReq (tenantId, itemId);
            marketItemUnitReq.setUnitDesc(costUnit);
            marketItemUnitReq.setUnitType(MarketItemUnitTypeEnum.STORE_COST_UNIT.getCode ());
            marketItemUnitReq.setStoreOrderingUnitMultiple(orderingInventoryUnitMultiple.multiply (inventoryCostUnitMultiple));
            marketItemUnitList.add (marketItemUnitReq);
        }
        return marketItemUnitList;
    }

    static MarketItemUnitReq buildMarketItemUnitReq(Long tenantId,Long itemId) {
        MarketItemUnitReq marketItemUnitReq = new MarketItemUnitReq ();
        marketItemUnitReq.setTenantId(tenantId);
        marketItemUnitReq.setItemId(itemId);
        return marketItemUnitReq;
    }

    MarketItemOnSaleInputReq convert2Req(MarketItemOnSaleInput input);
    MarketItemOnSaleInputReq convert2Req(ItemNoGoodsSupplyPriceExcelDataInput input);
    List<ItemNoGoodsSupplyPriceReq> convert2ReqList(List<ItemNoGoodsSupplyPriceExcelDataInput> input);

    BatchOnSaleResultVO convert2ResultVO(BatchOnSaleResultResp resp);
    BatchChangNoGoodsSupplyPriceResultVO convert2ResultVO(BatchChangNoGoodsSupplyPriceResp resp);

    @Mapping(source = "input", target = "priceInputs", qualifiedByName = "convert2PriceStrategyList4Price")
    @Mapping(source = "marketItemUnfairPriceStrategyDTO", target = "marketItemUnfairPriceStrategyReq")
    MarketItemPriceStrategyReq convert2Req(MarketItemPriceStrategyInput input);


    @Named("convert2PriceStrategyList")
    static List<MarketItemPriceInput> convert2PriceStrategyList(MarketItemInput input) {
        List<MarketAreaItemMappingInput> areaList = new ArrayList<>();
        Optional.ofNullable(input.getDefaultPrice()).ifPresent(areaList::add);
        if (CollectionUtils.isNotEmpty(input.getStoreGroupPrice())){
            areaList.addAll(input.getStoreGroupPrice());
        }
        if (CollectionUtils.isNotEmpty(input.getStorePrice())){
            areaList.addAll(input.getStorePrice());
        }
        return getPriceInputs(input.getTenantId(), input.getId(), areaList);
    }

    @Named("convert2PriceStrategyList4Price")
    static List<MarketItemPriceInput> convert2PriceStrategyList4Price(MarketItemPriceStrategyInput input) {
        List<MarketAreaItemMappingInput> areaList = new ArrayList<>();
        Optional.ofNullable(input.getDefaultPrice()).ifPresent(areaList::add);
        if (CollectionUtils.isNotEmpty(input.getStoreGroupPrice())){
            areaList.addAll(input.getStoreGroupPrice());
        }
        if (CollectionUtils.isNotEmpty(input.getStorePrice())){
            areaList.addAll(input.getStorePrice());
        }
        return getPriceInputs(input.getTenantId(), input.getItemId(), areaList);
    }

    static List<MarketItemPriceInput> getPriceInputs(Long tenantId, Long itemId, List<MarketAreaItemMappingInput> priceList) {
        return priceList.stream()
            .map(a -> {
                MarketItemPriceInput priceStrategy = MarketItemPriceInput.builder()
                    .itemId(itemId)
                    .strategyType(a.getType())
                    .strategyValue(a.getMappingNumber())
                    .targetType(PriceStrategyConstants.switchTargetType(a.getPriceType()))
                    .remark(a.getRemark())
                    .build();
                if(CollectionUtils.isNotEmpty (a.getLadderPrices ())){
                    priceStrategy.setPriceStrategyValue (JSON.toJSONString (a.getLadderPrices ()));
                }else{
                    if (PriceStrategyTypeEnum.ASSIGN.getCode ().equals(priceStrategy.getStrategyType ())) {
                        LadderPriceDTO ladderPriceDTO = new LadderPriceDTO ();
                        ladderPriceDTO.setPrice (a.getMappingNumber ());
                        ladderPriceDTO.setUnit (1);
                        priceStrategy.setPriceStrategyValue(JSON.toJSONString (Collections.singletonList (ladderPriceDTO)));
                    }else {
                        priceStrategy.setPriceStrategyValue (a.getMappingNumber ().toString ());
                    }
                }
                if (PriceTargetTypeEnum.TENANT.getCode().equals(priceStrategy.getTargetType())) {
                    priceStrategy.setTargetIds(Collections.singletonList(tenantId));
                } else if (PriceTargetTypeEnum.STORE.getCode().equals(priceStrategy.getTargetType())) {
                    priceStrategy.setTargetIds(a.getStoreIds());
                } else if (PriceTargetTypeEnum.STORE_GROUP.getCode().equals(priceStrategy.getTargetType())) {
                    priceStrategy.setTargetIds(a.getStoreGroupIds());
                }
                return priceStrategy;
            })
            .collect(Collectors.toList());
    }


    /**
     * 策略转area
     *
     * @param resp item对象
     * @return
     */
    @Named("convert2AreaMappingList")
    static List<MarketAreaItemMappingVO> convert2AreaMappingList(MarketItemInfoResp resp) {
        List<MarketItemPriceInput> priceStrategyList = resp.getPriceStrategyList();
        if (CollectionUtils.isEmpty(priceStrategyList)) {
            return Collections.emptyList();
        }
        return convert2AreaItemMappingVos(priceStrategyList, resp.getTenantId());
    }

    @Named("convertToMarketItemUnfairPriceStrategyDTO")
    static MarketItemUnfairPriceStrategyDTO convertToMarketItemUnfairPriceStrategyDTO(UnfairPriceStrategyResp unfairPriceStrategyResp){
        MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO = new MarketItemUnfairPriceStrategyDTO();
        if(Objects.isNull(unfairPriceStrategyResp)){
            return marketItemUnfairPriceStrategyDTO;
        }

        marketItemUnfairPriceStrategyDTO.setDefaultFlag(unfairPriceStrategyResp.getDefaultFlag().getCode());
        marketItemUnfairPriceStrategyDTO.setStrategyType(unfairPriceStrategyResp.getStrategyValue().getCode());
        return marketItemUnfairPriceStrategyDTO;
    }

    /**
     * 策略转area
     *
     * @param priceStrategyList
     * @param tenantId
     * @return
     */
    static List<MarketAreaItemMappingVO> convert2AreaItemMappingVos(List<MarketItemPriceInput> priceStrategyList, Long tenantId) {
        return priceStrategyList.stream()
            .map(p -> {
                MarketAreaItemMappingVO mappingVO = MarketAreaItemMappingVO.builder()
                    .tenantId(tenantId)
                    .priceType(PriceStrategyConstants.switchPriceType(p.getTargetType()))
                    .type(p.getStrategyType())
                    .mappingNumber(p.getStrategyValue())
//                    .differenceValue(p.getDifferenceValue())//4.25 因为阶梯价暂时去掉此参数的透出
                    .remark(p.getRemark())
                    .build();
                if (PriceStrategyTypeEnum.ASSIGN.getCode ().equals(mappingVO.getType ())) {
                    if (StringUtils.isNotBlank (p.getPriceStrategyValue ())) {
                        mappingVO.setLadderPrices (JSON.parseArray (p.getPriceStrategyValue (),LadderPriceDTO.class));
                    }
                }
                if (PriceTargetTypeEnum.STORE.getCode().equals(p.getTargetType())) {
                    mappingVO.setStoreIds(p.getTargetIds());
                } else if (PriceTargetTypeEnum.STORE_GROUP.getCode().equals(p.getTargetType())) {
                    mappingVO.setStoreGroupIds(p.getTargetIds());
                }
                return mappingVO;
            }).collect(Collectors.toList());
    }
}
