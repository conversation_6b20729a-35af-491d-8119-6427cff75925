package com.cosfo.manage.market.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 销售商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-21
 */
@Getter
@Setter
@TableName("market")
public class Market implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 主标题
     */
    @TableField("title")
    private String title;

    /**
     * 副标题
     */
    @TableField("sub_title")
    private String subTitle;

    /**
     * 类目Id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 主图
     */
    @TableField("main_picture")
    private String mainPicture;

    /**
     * 详情图
     */
    @TableField("detail_picture")
    private String detailPicture;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


    /**
     * @see com.cosfo.manage.common.context.MarketDeleteFlagEnum
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

}
