package com.cosfo.manage.market.model.vo;

import com.cosfo.manage.market.model.dto.LadderPriceDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/27 14:12
 * @Description:
 */
@Data
public class MarketItemInfoPageVO implements Serializable {
    private static final long serialVersionUID = 4864178595956957310L;

    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * 商品item主键
     * 下面两个值都是market——item.id
     */
    @Deprecated
    private Long itemId;
    private Long id;

    /**
     * 商品头图
     */
    private String mainPicture;

    /**
     * 商品名称
     * 下面三个值都是market.title
     */
    @Deprecated
    private String spuTitle;
    private String title;
    @Deprecated
    private String itemTitle;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategory;
    /**
     * 类目全名
     */
    private String categoryStr;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;
    /**
     * 二级分类Id
     * 下面两个值相同
     */
    private Long secondClassificationId;
    @Deprecated
    private Long classificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 分类全名
     */
    private String marketItemClassificationStr;

    /**
     * 售价
     * 下面两个值相同
     */
    private String priceRange;
    @Deprecated
    private String priceStr;
    /**
     * 最大售价
     */
    private BigDecimal maxPrice;
    /**
     * 最小售价
     */
    private BigDecimal minPrice;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 货品类型 0无货商品 1报价货品 2自营货品
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 标准单价(库存单价)
     */
    private BigDecimal standardUnitPrice;
    /**
     * 成本单价
     */
    private BigDecimal costUnitPrice;
    /**
     * 管控门店库存false=不管;true=管控
     */
    private Boolean storeInventoryControlFlag;
    /**
     * 门店订货单位
     */
    private String storeOrderingUnit;
    /**
     * 成本 库存 单位倍数
     */
    private BigDecimal storeInventoryCostUnitMultiple;
    /**
     * 门店库存单位
     */
    private String storeInventoryUnit;
    /**
     * 订货 库存 单位倍数
     */
    private BigDecimal storeOrderingInventoryUnitMultiple;
    /**
     * 门店成本单位
     */
    private String storeCostUnit;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;

    private Long marketId;

    /**
     * 阶梯价
     */
    private List<LadderPriceDTO> ladderPrices;

}
