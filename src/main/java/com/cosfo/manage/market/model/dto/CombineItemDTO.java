package com.cosfo.manage.market.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 16:07
 * @Description:
 */
@Data
public class CombineItemDTO {
    /**
     * 组合品映射表的主键
     */
    private Long mappingId;

    /**
     * market_item_id
     */
    private Long marketItemId;

    /**
     * 商品名
     */
    private String itemTitle;
    /**
     * 头图
     */
    private String mainPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private String priceStr;
    /**
     * 最大售价
     */
    private BigDecimal maxPrice;
    /**
     * 最小售价
     */
    private BigDecimal minPrice;
}
