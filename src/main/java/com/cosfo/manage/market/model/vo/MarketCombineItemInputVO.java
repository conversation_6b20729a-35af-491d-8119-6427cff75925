package com.cosfo.manage.market.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/26 10:35
 * @Description:
 */
@Data
public class MarketCombineItemInputVO implements Serializable {
    private static final long serialVersionUID = 1765856600311766590L;
    /**
     * 组合包ID
     */
    private Long combineMarketId;
    /**
     * 组合品item_id
     */
    private Integer combineItemId;
    /**
     * 组合包标题
     */
    private String combineMarketTitle;
    /**
     * 副标题
     */
    private String combineMarketSubTitle;

    /**
     * 分类Id
     */
    private Long classificationId;

    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价 3组合品按总价下调固定额度,4组合品按总价下调百分比 5组合品总价
     */
    private Integer priceType;
    /**
     * 价格调整金额/比例
     */
    private BigDecimal strategyValue;
    /**
     * 组合商品
     */
    private List<MakertCombineItemMappingVO> marketCombineItemMappingVOList;
}
