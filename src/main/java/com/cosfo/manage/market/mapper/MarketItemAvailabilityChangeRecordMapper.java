package com.cosfo.manage.market.mapper;

import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;

/**
 * <p>
 * 商品可用性记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Mapper
public interface MarketItemAvailabilityChangeRecordMapper extends BaseMapper<MarketItemAvailabilityChangeRecord> {

    void saveBatch(Collection<MarketItemAvailabilityChangeRecord> entityList);

}
