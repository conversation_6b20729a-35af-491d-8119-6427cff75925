package com.cosfo.manage.market.repository;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
//public interface MarketItemClassificationRepository extends IService<MarketItemClassification> {
//    /**
//     * 根据marketId更新
//     *
//     * @param tenantId
//     * @param marketId
//     * @param classificationId
//     */
//    void updateByMarketId(Long tenantId, Long marketId, Long classificationId);
//
//    /**
//     * 根据marketId查询绑定分类
//     *
//     * @param tenantId
//     * @param marketId
//     * @return
//     */
//    MarketItemClassification selectByMarketId(Long tenantId, Long marketId);
//}
