package com.cosfo.manage.market.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.market.mapper.MarketClassificationMapper;
import com.cosfo.manage.market.model.po.MarketClassification;
import com.cosfo.manage.market.repository.MarketClassificationRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
@Service
public class MarketClassificationRepositoryImpl extends ServiceImpl<MarketClassificationMapper, MarketClassification> implements MarketClassificationRepository {
//    @Resource
//    private MarketClassificationMapper marketClassificationMapper;
//
//
//    @Override
//    public MarketClassification queryByMarketId(Long marketId, Long tenantId) {
//        return marketClassificationMapper.selectByMarketId(tenantId, marketId);
//    }
}
