package com.cosfo.manage.market.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.converter.MarketConvert;
import com.cosfo.manage.market.model.dto.MarketAddResDTO;
import com.cosfo.manage.market.model.dto.MarketDeleteDTO;
import com.cosfo.manage.market.model.dto.MarketQueryInput;
import com.cosfo.manage.market.model.dto.MarketSpuInput;
import com.cosfo.manage.market.model.vo.MarketAddResVO;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 销售商品管理V2
 *
 * <AUTHOR>
 * @since 2022-12-21
 */
@RestController
@RequestMapping("/market")
public class MarketController extends BaseController {
    @Resource
    private MarketService marketService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @PostMapping("/query/detail")
    public CommonResult<MarketSpuVO> detail(Long id) {
        MarketSpuVO marketSpuVO = marketService.detailTenant(id,getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(marketSpuVO);
    }

    /**
     * 新增
     *
     * @param marketSpuInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-market:add", expireError = true)
    @PostMapping("/upsert/add")
    public CommonResult<MarketAddResVO> add(@RequestBody MarketSpuInput marketSpuInput) {
        MarketAddResDTO resDto = marketService.add(marketSpuInput, getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(MarketConvert.INSTANCE.convert2ResVO(resDto));
    }

    /**
     * 修改
     *
     * @param marketSpuInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-market:update", expireError = true)
    @PostMapping("/upsert/update")
    public CommonResult update(@RequestBody MarketSpuInput marketSpuInput) {
        marketService.update(marketSpuInput, getMerchantInfoDTO().getTenantId ());
        return CommonResult.ok(Boolean.TRUE);
    }
    /**
     * 销售商品列表
     *
     * @param marketQueryDTO
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:goods-market:query"}, logical = Logical.OR)
    @PostMapping("/query/list")
    public CommonResult<PageInfo<MarketSpuVO>> listAll(@RequestBody MarketQueryInput marketQueryDTO) {
        PageInfo<MarketSpuVO> pageInfo = marketService.list(marketQueryDTO, getMerchantInfoDTO());
        return CommonResult.ok(pageInfo);
    }

    /**
     * 商品导入
     * 已弃用，页面没有导入入口了
     *
     * @param file
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
//    @PostMapping(value = "/import")
//    @Deprecated
//    public CommonResult<ExcelImportResDTO> importProduct(@RequestBody MultipartFile file) throws IOException {
//        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
//        ExcelImportResDTO excelImportResDTO = marketService.importProduct(file, loginContextInfoDTO);
//        return CommonResult.ok(excelImportResDTO);
//    }

    /**
     * 导入三方商品映射
     *
     * @param file
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/importThirdMap")
    public CommonResult<ExcelImportResDTO> importThirdMap(@RequestBody MultipartFile file, HttpServletResponse response) throws IOException {
        return CommonResult.ok(marketService.importThirdMap(file, getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 导出三方商品映射模板
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/query/exportThirdMapTemplate")
    public CommonResult<String> exportThirdMapTemplate() {
        return CommonResult.ok(marketService.exportThirdMapTemplate());
    }



        /**
         * 商品导出
         *
         * @param marketQueryDTO
         * @return
         */
        @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/export")
    public CommonResult export(@RequestBody MarketQueryInput marketQueryDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        marketService.export(marketQueryDTO, loginContextInfoDTO);
        return CommonResult.ok();
    }

    /**
     * 商品删除
     *
     * @param marketDeleteDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/delete")
    public CommonResult delete(@RequestBody MarketDeleteDTO marketDeleteDTO) {
        marketService.delete(marketDeleteDTO);
        return CommonResult.ok();
    }
}
