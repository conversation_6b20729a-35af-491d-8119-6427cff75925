package com.cosfo.manage.thirdsys.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlElement;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxNotifyMessageVO {
    /**
     * <xml>
     *   <ToUserName><![CDATA[toUser]]></ToUserName>
     *   <FromUserName><![CDATA[FromUser]]></FromUserName>
     *   <CreateTime>123456789</CreateTime>
     *   <MsgType><![CDATA[event]]></MsgType>
     *   <Event><![CDATA[subscribe]]></Event>
     *   <EventKey><![CDATA[qrscene_123123]]></EventKey>
     * </xml>
     */
    private static final long serialVersionUID = 1L;
    @XmlElement(name = "ToUserName")
    private String ToUserName;
    @XmlElement(name = "FromUserName")
    private String FromUserName;
    @XmlElement(name = "CreateTime")
    private Long CreateTime;
    @XmlElement(name = "MsgType")
    private String MsgType;
    @XmlElement(name = "Event")
    private String Event;
    @XmlElement(name = "EventKey")
    private String EventKey;
    @Override
    public String toString() {
        return "<xml>" +
                "<ToUserName>" + ToUserName + "</ToUserName:" +
                "<FromUserName>" + FromUserName + "</FromUserName>" +
                "<CreateTime>" + CreateTime + "</CreateTime:" +
                "<MsgType>" + MsgType + "</MsgType>" +
                "<Event>" + Event + "</Event>" +
                "<EventKey>" + EventKey + "EventKey" +
                "</xml>";
    }
}