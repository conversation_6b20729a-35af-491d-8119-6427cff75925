package com.cosfo.manage.thirdsys.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Objects;

public enum WxEeventType {
    /**
     * 扫码
     */
    SCAN("SCAN"),
    /**
     * 关注公众号
     */
    SUBSCRIBE("subscribe"),
    /**
     * 取消关注公众号
     */
    UNSUBSCRIBE("unsubscribe");

    public final String code;

    WxEeventType(String code) {
        this.code = code;
    }

    public static WxEeventType wrap(String code) {
        return StrUtil.isEmpty(code) ? null :
                Arrays.stream(WxEeventType.values())
                        .filter(type -> Objects.equals(type.code, code))
                        .findFirst()
                        .orElse(null);
    }
}