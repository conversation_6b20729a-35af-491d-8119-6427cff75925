package com.cosfo.manage.bill.model.vo;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.bind.DefaultValue;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentRecordTotalVO implements Serializable {

    /**
     * 充值成功
     */
    private BigDecimal successAmount;


    /**
     * 充值失败
     */
    private BigDecimal failAmount;

    /**
     * 待审核
     */
    private BigDecimal auditAmount;

    public PrepaymentRecordTotalVO() {
        this.successAmount = BigDecimal.ZERO;
        this.failAmount = BigDecimal.ZERO;
        this.auditAmount = BigDecimal.ZERO;
    }
}
