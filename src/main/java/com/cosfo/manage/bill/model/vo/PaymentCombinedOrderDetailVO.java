package com.cosfo.manage.bill.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @description: 组合支付订单明细表
* @author: George
* @date: 2025-04-29
**/
@Data
public class PaymentCombinedOrderDetailVO {
    /**
    * primary key
    */
    private Long id;

    /**
    * 组合明细ID
    */
    private Long combinedDetailId;

    /**
    * 订单ID
    */
    private Long orderId;

    /**
    * 金额
    */
    private BigDecimal totalPrice;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 订单编号
    */
    private String orderNo;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
     * 交易类型，枚举值：JSAPI：微信原生支付 汇付字段:T_JSAPI: 微信公众号支付、T_MINIAPP: 微信小程序支付、A_NATIVE: 支付宝正扫、ZERO_PRICE: 无需支付、OFFLINE:线下支付、NON_CASH：非现金支付
     */
    private String tradeType;

    /**
     * 交易类型，枚举值：JSAPI：微信原生支付 汇付字段:T_JSAPI: 微信公众号支付、T_MINIAPP: 微信小程序支付、A_NATIVE: 支付宝正扫、ZERO_PRICE: 无需支付、OFFLINE:线下支付、NON_CASH：非现金支付
     */
    private String tradeTypeDesc;

    /**
     * 支付类型
     */
    private Integer payType;
}