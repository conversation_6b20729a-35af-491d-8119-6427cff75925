package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Data
public class TenantBillQueryDTO {
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * type 1、直配到店 2、采购入库 3、代仓
     */
    private Integer type;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 品牌方租户Id
     */
    private Long tenantId;

    /**
     * 导出内容 1、订单汇总 2、商品汇总
     */
    private List<Integer> exportContentType;

    /**
     * 0、无货 1、报价货品 2、自营货品
     */
    private Integer goodsType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 请求来源，区分 cosfo-manage请求，还是cosfo-oms请求，空默认是cosfo-manage
     * - cosfo-manage
     * - cosfo-oms
     */
    private String reqSource;
}
