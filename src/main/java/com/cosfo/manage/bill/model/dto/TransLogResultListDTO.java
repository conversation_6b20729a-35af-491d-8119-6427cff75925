package com.cosfo.manage.bill.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 结算信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Data
@Getter
@EqualsAndHashCode(callSuper = false)
public class TransLogResultListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 结算流水编号
     */
    private String transId;

    /**
     * 结算日期；yyyyMMdd；示例值：20221023
     */
    private String transDate;

    /**
     * 账务状态；P：处理中、S：成功、F：失败；示例值：S
     * 银行结算状态
     */
    private String acctStat;

    /**
     * 结算金额
     */
    private String transMoney;
    /**
     * 结算手续费
     */
    private String transFee;
    /**
     * 银行失败原因
     */
    private String reason;

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页码大小
     */
    private Integer pageSize;

}
