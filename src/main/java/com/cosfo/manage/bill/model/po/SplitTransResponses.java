package com.cosfo.manage.bill.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 汇付分账明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SplitTransResponses implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 请求流水号
     */
    private String reqSeqId;

    /**
     * 请求日期
     */
    private String reqDate;

    /**
     * 出账方商户号
     */
    private String huifuId;

    /**
     * 入账方商户号
     */
    private String inHuifuId;

    /**
     * 产品号
     */
    private String productId;

    /**
     * 订单流水号
     */
    private String orgOrdId;

    /**
     * 汇付交易订单号
     */
    private String transOrdId;

    /**
     * 分账交易汇付全局流水号
     */
    private String hfSeqId;

    /**
     * 交易类型;consume：正向交易、refund：反向交易；示例值：refund
     */
    private String ordType;

    /**
     * 分账金额
     */
    private String splitAmt;

    /**
     * 分账手续费
     */
    private String splitFeeAmt;

    /**
     * 分账手续费承担方商户号
     */
    private String splitFeeHuifuId;

    /**
     * 分账流水号
     */
    private String splitSeqId;

    /**
     * 分账类型;realTime：实时、delay：延时；示例值：delay
     */
    private String splitType;

    /**
     * 分账完成时间;yyyyMMddHHmmss；示例值：20221023112345
     */
    private String transFinishTime;

    /**
     * 账务状态;P：处理中、S：成功、F：失败；示例值：S
     */
    private String acctStat;


}
