package com.cosfo.manage.bill.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 分账退款明细快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
@Getter
@Setter
@TableName("bill_profit_sharing_refund_snapshot")
@NoArgsConstructor
@AllArgsConstructor
public class BillProfitSharingRefundSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 订单明细id
     */
    @TableField("order_item_id")
    private Long orderItemId;

    /**
     * 售后id
     */
    @TableField("order_after_sale_id")
    private Long orderAfterSaleId;

    /**
     * 账号id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 1、自营商品金额 2、供应商商品金额 3、运费 4、订单手续费
     */
    @TableField("profit_sharing_type")
    private Integer profitSharingType;

    /**
     * 原始金额
     */
    @TableField("origin_price")
    private BigDecimal originPrice;

    /**
     * 退款金额
     */
    @TableField("refund_price")
    private BigDecimal refundPrice;

    /**
     * 应退金额
     */
    @TableField("should_refund_price")
    private BigDecimal shouldRefundPrice;

    /**
     * 实退金额
     */
    @TableField("actual_refund_price")
    private BigDecimal actualRefundPrice;

    /**
     * 最终金额
     */
    @TableField("final_refund_price")
    private BigDecimal finalRefundPrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
