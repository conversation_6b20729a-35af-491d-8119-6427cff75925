package com.cosfo.manage.bill.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/11
 */
@Data
public class FinancialBillTotalDataVO {
    /**
     * 账单数
     */
    private Integer billNum;
    /**
     * 账单应收金额合计
     */
    private BigDecimal receivablePrice;
    /**
     * 订单应收金额总计
     */
    private BigDecimal orderReceivablePrice;
    /**
     * 售后单应收金额总计
     */
    private BigDecimal orderAfterSaleReceivablePrice;
}
