package com.cosfo.manage.bill.model.bo;

import com.cosfo.manage.bill.model.po.BillSupplierDirectAssignSummary;
import com.cosfo.manage.report.model.dto.MarketItemSalesSummaryExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @description:
 * @author: George
 * @date: 2024-02-29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillSummaryBO {

    /**
     * 账单概要汇总
     */
    private BillSupplierDirectAssignSummary billSupplierDirectAssignSummary;

    /**
     * 商品汇总
     */
    private Map<Integer, Map<Long, MarketItemSalesSummaryExcelDTO>> itemSummaryMap;
}
