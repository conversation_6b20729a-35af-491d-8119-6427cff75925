package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date : 2022/12/20 2:40
 */
@Data
public class BillProfitSharingDTO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 接收方租户id
     */
    private Long receiverTenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * appId
     */
    private String appId;

    /**
     * 账号类型
     */
    private String type;

    /**
     * 付款方账号
     */
    private String account;

    /**
     * 原交易银行流水号
     */
    private String transactionId;

    /**
     * 外部交易订单号
     */
    private String outTradeNo;

    /**
     * 交易金额，分账金额
     */
    private BigDecimal price;

    /**
     * 状态0待分账1成功2失败3取消
     */
    private Integer status;

    /**
     * 分账方接收方主体名称
     */
    private String tenantName;
    /**
     * 分账手续费
     */
    private BigDecimal splitFeeAmt;

    /**
     * 成功时间
     */
    private Date successTime;

    /**
     * 分账描述
     */
    private String description;

    /**
     * 微信分账单号,原交易银行流水号
     */
    private String bankOrderId;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 分账明细单号
     */
    private String detailId;

    /**
     * start time
     */
    private LocalDateTime startTime;
    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * end time
     */
    private LocalDateTime endTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 1,正向分账 2逆向分账退款
     */
    private Integer businessType;
    /**
     * 售后单Id
     */
    private Long afterSaleId;

    private Integer pageIndex;

    private Integer pageSize;
    /**
     * 分账交易订单号
     */
    private String recordNo;

    /**
     * @see com.cosfo.manage.common.context.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 支付单号
     */
    private String paymentNo;

    private static final long serialVersionUID = 1L;
}
