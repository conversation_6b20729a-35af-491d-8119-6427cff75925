package com.cosfo.manage.bill.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PrepaymentTransactionQueryDTO extends BasePageInput {

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 收支类型 0、供应商直供 1、代仓费用 2、供应商直供和代仓费用
     */
    private Integer prepaymentType;

    /**
     * 交易类型  0、预付 1、预付退款 2、直供货品消费 3、直供货品退款 4、运费 5、运费退款 6、代仓费用 7、代仓费用退款
     */
    private Integer transactionType;


    /**
     * 收支类型 0、收入 1、支出
     */
    private Integer type;

    /**
     * 交易对象
     */
    private String transactionName;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 订单编号
     */
    private String orderNo;

    private Long tenantId;
}
