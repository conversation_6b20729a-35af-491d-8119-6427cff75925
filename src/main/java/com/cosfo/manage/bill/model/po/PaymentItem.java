package com.cosfo.manage.bill.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * payment_item
 * <AUTHOR>
@Data
public class PaymentItem implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 支付凭证
     */
    private String paymentReceipt;
    /**
     * 财务凭证
     */
    private String financialReceipt;
    /**
     * 收款日期
     */
    private LocalDate receiptDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 线下支付方式0=银行转账;1=微信;2=支付宝
     */
    private Integer offlinePayType;

    private static final long serialVersionUID = 1L;
}