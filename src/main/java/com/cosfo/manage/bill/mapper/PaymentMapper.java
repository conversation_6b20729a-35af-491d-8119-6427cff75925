package com.cosfo.manage.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.query.PaymentConditionQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PaymentMapper extends BaseMapper<Payment> {
    int deleteByPrimaryKey(Long id);

    int insert(Payment record);

    int insertSelective(Payment record);

    Payment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Payment record);

    int updateByPrimaryKey(Payment record);

    /**
     * 查询支付单
     * @param paymentNo 支付单号
     * @return 支付单
     */
    Payment selectByPaymentNo(@Param("paymentNo") String paymentNo);

    Payment selectByTransactionId(@Param("transactionId") String transactionId);

    /**
     * 根据订单号查询支付单
     * @param orderId
     * @return
     */
    Payment selectByOrderId(Long orderId);

    /**
     * 根据订单号批量查询支付成功交易记录
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<PaymentItemDTO> querySuccessPaymentByOrderIds(@Param("orderIds") List<Long> orderIds, @Param("tenantId") Long tenantId);

    /**
     * 查询需要上传发货信息的支付单
     * @param query
     * @return
     */
    List<Payment> queryNeedShippingPaymentByCondition(PaymentConditionQuery query);

    /**
     * 根据时间查询成功的支付单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Payment> selectSuccessByTime(@Param("tenantId") Long tenantId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询成功总金额
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal selectSuccessPrice(@Param("tenantId") Long tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据订单号查询成功支付单
     * @param tenantId
     * @param orderId
     * @return
     */
    Payment querySuccessByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);
}