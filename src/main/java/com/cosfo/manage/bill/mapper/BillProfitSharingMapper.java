package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.manage.bill.model.dto.BillProfitSharingQueryDTO;
import com.cosfo.manage.bill.model.po.BillProfitSharing;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BillProfitSharingMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(BillProfitSharing record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(BillProfitSharing record);

    /**
     * 查询
     * @param id
     * @return
     */
    BillProfitSharing selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillProfitSharing record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(BillProfitSharing record);

    /**
     * 查询分账明细
     * @param billProfitSharingDTO
     * @return
     */
    List<BillProfitSharingDTO> listAll(BillProfitSharingQueryDTO billProfitSharingDTO);

    /**
     * 导出查询分账明细
     * @param billProfitSharingDTO
     * @param resultHandler
     */
    void exportListAll(BillProfitSharingQueryDTO billProfitSharingDTO, ResultHandler<?> resultHandler);

    /**
     * 通过订单id查询分账明细
     * @param orderId
     * @return
     */
    List<BillProfitSharingDTO> selectByOrderId(Long orderId);

}
