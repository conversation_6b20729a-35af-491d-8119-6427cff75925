package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail;
import com.cosfo.manage.bill.model.vo.PaymentCombinedOrderDetailVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
* @description: ${description}
* @author: George
* @date: 2025-04-29
**/
public interface PaymentCombinedOrderDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentCombinedOrderDetail record);

    int insertSelective(PaymentCombinedOrderDetail record);

    PaymentCombinedOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentCombinedOrderDetail record);

    int updateByPrimaryKey(PaymentCombinedOrderDetail record);

    List<PaymentCombinedOrderDetailVO> getListByOrderIds(@Param("orderIds") Collection<Long> orderIds);

    /**
     * 根据组合明细ID和订单ID查询金额
     *
     * @param combinedDetailId 组合明细ID
     * @param orderId 订单ID
     * @return 金额
     */
    BigDecimal getTotalPriceByCombinedDetailIdAndOrderId(@Param("combinedDetailId") Long combinedDetailId, @Param("orderId") Long orderId);
}