package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.dto.BillProfitSharingOrderQuery;
import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.model.po.BillProfitSharingOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BillProfitSharingOrderMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(BillProfitSharingOrder record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(BillProfitSharingOrder record);

    /**
     * 查询
     * @param id
     * @return
     */
    BillProfitSharingOrder selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillProfitSharingOrder record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(BillProfitSharingOrder record);

    /**
     * 查询分账账单
     *
     * @param tenantBillQueryDTO
     * @return
     */
    List<BillProfitSharingOrder> queryProfitSharingOrder(@Param("tenantBillQueryDTO") TenantBillQueryDTO tenantBillQueryDTO);

    /**
     * 按状态批量查询分账订单
     * @param orderQuery
     * @return
     */
    List<BillProfitSharingOrder> batchQueryByStatus(@Param("orderQuery")BillProfitSharingOrderQuery orderQuery);


    /**
     * 根据订单Id查询
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<BillProfitSharingOrder> queryByOrderIdAndTenantId(@Param("tenantId") Long tenantId,@Param("orderId") Long orderId);

    int updateStatusById(@Param("id") Long id, @Param("orgStatus") Integer orgStatus, @Param("finalStatus") Integer finalStatus);

    /**
     * 根据订单Id和供应商id查询
     * @param tenantId
     * @param orderId
     * @param supplierId
     * @return
     */
    BillProfitSharingOrder queryByTenantAndOrderAndSupplierId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId, @Param("supplierId") Long supplierId);
}
