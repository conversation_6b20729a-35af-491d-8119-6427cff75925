package com.cosfo.manage.bill.controller;

import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTotalVO;
import com.cosfo.manage.bill.service.PrepaymentRecordService;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 预付概况
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prepayment/record")
public class PrepaymentRecordController extends BaseController {

    @Resource
    private PrepaymentRecordService prepaymentRecordService;

    /**
     * 预付明细
     * @param queryDTO
     * @return
     */
    @PostMapping("/query/record")
    public CommonResult<PageInfo<PrepaymentRecordVO>> queryPrepaymentRecord(@RequestBody PrepaymentRecordQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentRecordService.queryPrepaymentRecordPage(queryDTO));
    }

    /**
     * 导出预付明细
     * @param queryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/download")
    public CommonResult<Boolean> downloadPrepaymentRecord(@RequestBody PrepaymentRecordQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentRecordService.downPrepaymentRecord(queryDTO));
    }

    /**
     * 数据合计
     * @param queryDTO
     * @return
     */
    @PostMapping("/query/total")
    public CommonResult<PrepaymentRecordTotalVO> queryPrepaymentTotal(@RequestBody PrepaymentRecordQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
       return CommonResult.ok(prepaymentRecordService.queryPrepaymentTotal(queryDTO));
    }

    /**
     * 预付充值
     * @param dto
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:prepay-record:add", expireError = true)
    @PostMapping("/upsert/add")
    public CommonResult<String> addPrepayment(@RequestBody @Valid PrepaymentRecordDTO dto) {
        dto.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(String.valueOf(prepaymentRecordService.addPrepaymentRecord(dto)));
    }

    /**
     * 预付记录详情
     * @param dto
     * @return
     */
    @PostMapping("/query/detail")
    public CommonResult<PrepaymentRecordVO> getPrepaymentRecord(@RequestBody PrepaymentRecordDTO dto) {
        dto.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentRecordService.getPrepaymentRecord(dto.getId()));
    }
}
