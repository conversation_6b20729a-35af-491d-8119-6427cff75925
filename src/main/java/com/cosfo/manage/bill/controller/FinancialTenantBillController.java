package com.cosfo.manage.bill.controller;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.service.FinancialTenantBillService;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.result.ResultDTO;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述: 应付账单
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@RestController
@RequestMapping("/tenantBill")
public class FinancialTenantBillController extends BaseController {
    @Resource
    private FinancialTenantBillService financialTenantBillService;

    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export")
    public ResultDTO export(@RequestBody TenantBillQueryDTO tenantBillQueryDTO){
        return financialTenantBillService.export(tenantBillQueryDTO, getMerchantInfoDTO());
    }
}
