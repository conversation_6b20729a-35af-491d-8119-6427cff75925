package com.cosfo.manage.bill.controller;

import com.cosfo.manage.bill.model.dto.*;
import com.cosfo.manage.bill.service.BillProfitSharingOrderService;
import com.cosfo.manage.bill.service.FinancialBillService;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 描述:分账明细和结算明细
 * <AUTHOR>
 * @date : 2022/12/23 14:44
 */
@Slf4j
@RestController
@RequestMapping("/financial")
public class FinancialBillAndSettleController extends BaseController {

    @Resource
    private FinancialBillService financialBillService;

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    /**
     * 查询分账明细接口
     * @param billProfitSharingDTO
     * @return
     */
    @PostMapping("/query/profit-list")
    public CommonResult<PageInfo<BillProfitSharingDTO>> getProfitList(@RequestBody BillProfitSharingDTO billProfitSharingDTO){
        BillProfitSharingQueryDTO billProfitSharingQueryDTO = new BillProfitSharingQueryDTO();
        BeanUtils.copyProperties(billProfitSharingDTO,billProfitSharingQueryDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return billProfitSharingOrderService.listAllProfits(billProfitSharingQueryDTO, contextInfoDTO);
    }

    /**
     * 查询分账明细金额合计
     * @param billProfitSharingDTO
     * @return
     */
    @PostMapping("/query/profit-count")
    public CommonResult<ReceiveAndOutMoneyDTO> getProfitListCount(@RequestBody BillProfitSharingDTO billProfitSharingDTO){
        BillProfitSharingQueryDTO billProfitSharingQueryDTO = new BillProfitSharingQueryDTO();
        BeanUtils.copyProperties(billProfitSharingDTO,billProfitSharingQueryDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return billProfitSharingOrderService.listAllProfitsCountMoney(billProfitSharingQueryDTO, contextInfoDTO);
    }


    /**
     * 分账明细导出
     *
     * @param billProfitSharingDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export/export-profit", method = RequestMethod.POST)
    public CommonResult exportProfitList(@RequestBody BillProfitSharingQueryDTO billProfitSharingDTO) {
        return financialBillService.exportProfitList(getMerchantInfoDTO(), billProfitSharingDTO);
    }

    /**
     * 查询结算明细接口
     * @param transLogResultListQueryDTO
     * @return
     */
    @PostMapping("/query/settlement-list")
    public CommonResult<PageInfo<TransLogResultListDTO>> getSettlementList(@RequestBody TransLogResultListQueryDTO transLogResultListQueryDTO){
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return billProfitSharingOrderService.listAllTrans(transLogResultListQueryDTO, contextInfoDTO);
    }

    /**
     * 查询结算明细金额合计
     * @param transLogResultListQueryDTO
     * @return
     */
    @PostMapping("/query/settlement-count")
    public CommonResult<ReceiveAndOutMoneyDTO> getSettlementListCount(@RequestBody TransLogResultListQueryDTO transLogResultListQueryDTO){
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return billProfitSharingOrderService.listAllTransCountMoney(transLogResultListQueryDTO, contextInfoDTO);
    }

    /**
     * 结算明细导出
     *
     * @param transLogResultListQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export/export-settlement", method = RequestMethod.POST)
    public CommonResult exportSettlementList(@RequestBody TransLogResultListQueryDTO transLogResultListQueryDTO) {
        return financialBillService.exportSettlementList(getMerchantInfoDTO(), transLogResultListQueryDTO);
    }
    /**
     * 省市区行政编码表导入
     *
     * @param file
     * @return
     */
    @RequestMapping(value = "/import/import-administrative-division", method = RequestMethod.POST)
    public CommonResult importAdministrativeDivision(@RequestBody MultipartFile file) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ExcelImportResDTO excelImportResDTO = null;
        try {
            excelImportResDTO = financialBillService.importAdministrativeDivision(file, loginContextInfoDTO);
        } catch (IOException e) {
            log.error("导入省市区行政编码表报错!{}",e);
        }
        return CommonResult.ok(excelImportResDTO);
    }
}
