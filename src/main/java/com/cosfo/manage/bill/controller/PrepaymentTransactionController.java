package com.cosfo.manage.bill.controller;

import com.cosfo.manage.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionVO;
import com.cosfo.manage.bill.service.PrepaymentTransactionService;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 预付金流水
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prepayment/transaction")
public class PrepaymentTransactionController extends BaseController {

    @Resource
    private PrepaymentTransactionService prepaymentTransactionService;
    /**
     * 收支明细
     * @param queryDTO
     * @return
     */
    @PostMapping("/query/page")
    public CommonResult<PageInfo<PrepaymentTransactionVO>> queryPrepaymentTransactionPage(@RequestBody PrepaymentTransactionQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentTransactionService.queryPrepaymentTransactionPage(queryDTO));
    }

    /**
     * 导出收支明细
     * @param queryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/download")
    public CommonResult<Boolean> downloadPrepaymentTransaction(@RequestBody PrepaymentTransactionQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentTransactionService.downloadPrepaymentTransaction(queryDTO));
    }

    /**
     * 收支合计
     * @param queryDTO
     * @return
     */
    @PostMapping("/query/total")
    public CommonResult<PrepaymentTransactionTotalVO> queryPrepaymentTransactionTotal(@RequestBody PrepaymentTransactionQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentTransactionService.queryPrepaymentTransactionTotal(queryDTO));
    }
}
