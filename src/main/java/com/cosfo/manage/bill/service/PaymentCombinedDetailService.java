package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-05-08
 **/
public interface PaymentCombinedDetailService {

    /**
     * 根据订单查询组合支付明细
     *
     * @param orderId
     * @return
     */
    List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId);

    /**
     * 根据租户ID和订单ID查询组合支付明细，返回tradeType和totalPrice的映射
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return Map<String, BigDecimal> key是tradeType，value是totalPrice
     */
    Map<String, BigDecimal> querySuccessCombinedTradeTypeAmountByOrderId(Long tenantId, Long orderId);
}
