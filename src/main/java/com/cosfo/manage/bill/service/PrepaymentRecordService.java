package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 */
public interface PrepaymentRecordService {

    /**
     * 预付一笔
     * @param dto
     * @return
     */
    Long addPrepaymentRecord(PrepaymentRecordDTO dto);

    /**
     * 获取预付记录详情
     * @param id
     * @return
     */
    PrepaymentRecordVO getPrepaymentRecord(Long id);

    /**
     * 获取预付记录分页
     * @param queryDTO
     * @return
     */
    PageInfo<PrepaymentRecordVO> queryPrepaymentRecordPage(PrepaymentRecordQueryDTO queryDTO);


    /**
     * 下载预付记录
     * @param queryDTO
     * @return
     */
    Boolean downPrepaymentRecord(PrepaymentRecordQueryDTO queryDTO);

    /**
     * 获取预付合计
     * @param queryDTO
     * @return
     */
    PrepaymentRecordTotalVO queryPrepaymentTotal(PrepaymentRecordQueryDTO queryDTO);
}
