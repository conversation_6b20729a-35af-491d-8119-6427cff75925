package com.cosfo.manage.bill.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.bill.converter.BillConverter;
import com.cosfo.manage.bill.model.bo.BillSummaryBO;
import com.cosfo.manage.bill.model.dto.BillSummaryExcelDTO;
import com.cosfo.manage.bill.model.dto.OrderProfitSharingDifferenceExcelDTO;
import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.model.po.BillAgentWarehouseSummary;
import com.cosfo.manage.bill.model.po.BillSupplierDirectAssignSummary;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import com.cosfo.manage.bill.repository.BillAgentWarehouseSummaryRepository;
import com.cosfo.manage.bill.repository.BillSupplierDirectAssignSummaryRepository;
import com.cosfo.manage.bill.service.FinancialTenantBillService;
import com.cosfo.manage.bill.service.OrderProfitSharingDifferenceService;
import com.cosfo.manage.common.SettlementTypeEnum;
import com.cosfo.manage.common.config.SpecialReconciliationTenantsConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.SheetCounter;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemSnapshotQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.merchant.service.MerchantService;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.manage.report.converter.ReportConverter;
import com.cosfo.manage.report.model.dto.*;
import com.cosfo.manage.report.model.po.MarketItemSalesSummary;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import com.cosfo.manage.report.model.po.OrderAfterSaleInvertedSummary;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import com.cosfo.manage.report.service.*;
import com.cosfo.manage.supplier.service.SupplierDeliveryInfoService;
import com.cosfo.manage.supplier.service.SupplierService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述: 应付账单
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Slf4j
@Service
public class FinancialTenantBillServiceImpl implements FinancialTenantBillService {
    @Resource
    private CommonService commonService;
    @Resource
    private MerchantService merchantService;

    @Resource
    private OrderItemDetailSummaryService orderItemDetailSummaryService;
    @Resource
    private OrderAfterSaleDetailSummaryService orderAfterSaleDetailSummaryService;
    @Resource
    private MarketItemSalesSummaryService marketItemSalesSummaryService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private BillSupplierDirectAssignSummaryRepository billSupplierDirectAssignSummaryRepository;
    @Resource
    private BillAgentWarehouseSummaryRepository billAgentWarehouseSummaryRepository;
    @Resource
    private OrderAfterSaleInvertedSummaryService orderAfterSaleInvertedSummaryService;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;
    @Resource
    private OrderProfitSharingDifferenceService orderProfitSharingDifferenceService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private SpecialReconciliationTenantsConfig specialReconciliationTenantsConfig;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public ResultDTO export(TenantBillQueryDTO tenantBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 处理请求参数
        processRequestParams(tenantBillQueryDTO, loginContextInfoDTO);

        // 处理文件参数
        Map<String, String> queryParamsMap = processFileParameters(tenantBillQueryDTO);

        Integer fileDownloadType = FileDownloadTypeEnum.AGENT_WAREHOUSE_BILL.getType();
        if(Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode())){
            fileDownloadType = FileDownloadTypeEnum.PRE_PAY.getType();
        }else{
            fileDownloadType = FileDownloadTypeEnum.AGENT_WAREHOUSE_BILL.getType();
        }

        String fileName = getStatementFileName(tenantBillQueryDTO) + ".xlsx";

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource("cosfo-oms".equals(tenantBillQueryDTO.getReqSource()) ? DownloadCenterEnum.RequestSource.SAAS_BOSS : DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(fileDownloadType);
        recordDTO.setTenantId("cosfo-oms".equals(tenantBillQueryDTO.getReqSource()) ? 0L : loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(fileName);
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);

        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(tenantBillQueryDTO, ee -> {
            // 1、表格处理
            String filePath = generateFile(tenantBillQueryDTO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        //存储文件下载记录
//        Long fileDownloadRecordId = processFileDownloadRecord(tenantBillQueryDTO, queryParamsMap, loginContextInfoDTO);
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateStatementExportFile(tenantBillQueryDTO, fileDownloadRecordId);
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(fileDownloadRecordId);
//                log.error("应付账单导出失败", e);
//            }
//        });
        return ResultDTO.success();
    }

    private String generateFile(TenantBillQueryDTO tenantBillQueryDTO) {
        Long tenantId = tenantBillQueryDTO.getTenantId();
        boolean directAssignBill = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode());
        boolean xmSupplier = Objects.equals(tenantBillQueryDTO.getSupplierId(), SupplierTenantEnum.XM.getId());
        // 益禾堂
        if (specialReconciliationTenantsConfig.getSpecialReconciliationTenants().contains(tenantId) && directAssignBill && xmSupplier) {
            return generateSpecialStatementExportFile(tenantBillQueryDTO);
        }

        // 常规
        return generateStatementExportFile(tenantBillQueryDTO);
    }

    private String generateSpecialStatementExportFile(TenantBillQueryDTO tenantBillQueryDTO) {
        String tempExcelFilePath = ExcelUtils.tempExcelFilePath();
        // 模板是固定的
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.SPECIAL_RULE_BILL.getName());
        ExcelWriter excelWriter = EasyExcel.write(tempExcelFilePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();

        List<Integer> exportContentTypes = tenantBillQueryDTO.getExportContentType();
        boolean orderSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ORDER_SUMMARY.getType());
        boolean itemSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ITEM_SUMMARY.getType());

        // 生成订单明细
        BillSummaryBO billSummaryBO = generateSpecialOrderItemSheet(tenantBillQueryDTO, excelWriter, tempExcelFilePath, !orderSummaryFlag);

        // 生成概要汇总
        generateSpecialBillSummary(billSummaryBO.getBillSupplierDirectAssignSummary(), tenantBillQueryDTO, excelWriter, !orderSummaryFlag);

        // 现结分账差额
        SheetCounter sheetCounter = new SheetCounter(2);
        generateInvertedSummarySheet(tenantBillQueryDTO, excelWriter, false, tempExcelFilePath, sheetCounter);

        // 退款分账差额
        generateInvertedRefundSummarySheet(tenantBillQueryDTO, excelWriter, false, tempExcelFilePath, sheetCounter);

        // 生成商品汇总
        generateSpecialItemSummarySheets(billSummaryBO.getItemSummaryMap(), excelWriter, !itemSummaryFlag);

        // 关闭excelWriter
        excelWriter.finish();
        return tempExcelFilePath;
    }

    private void generateSpecialItemSummarySheets(Map<Integer, Map<Long, MarketItemSalesSummaryExcelDTO>> itemPaySummaryMap, ExcelWriter excelWriter, Boolean needHiddenSheet) {
        if (needHiddenSheet) { // NOSONAR
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.setSheetHidden(4, true);
            workbook.setSheetHidden(5, true);
            workbook.setSheetHidden(6, true);
            return;
        }

        WriteSheet allSheets = EasyExcel.writerSheet(4).build();
        WriteSheet currentSettleSheets = EasyExcel.writerSheet(5).build();
        WriteSheet billSettleSheets = EasyExcel.writerSheet(6).build();

        Map<Long, MarketItemSalesSummaryExcelDTO> currentSettleItemMap = itemPaySummaryMap.getOrDefault(SettlementTypeEnum.CURRENT_SETTLE.getType(), Collections.emptyMap());
        Map<Long, MarketItemSalesSummaryExcelDTO> billSettleItemMap = itemPaySummaryMap.getOrDefault(SettlementTypeEnum.BILL_SETTLE.getType(), Collections.emptyMap());

        // 现结结算商品汇总
        Collection<MarketItemSalesSummaryExcelDTO> currentSettleMarketItemSummaries = currentSettleItemMap.values();
        excelWriter.fill(currentSettleMarketItemSummaries, currentSettleSheets);

        // 账期结算商品汇总
        Collection<MarketItemSalesSummaryExcelDTO> billSettleMarketItemSummaries = billSettleItemMap.values();
        excelWriter.fill(billSettleMarketItemSummaries, billSettleSheets);

        // 所有商品汇总
        // 将现结和账期的商品汇总合并
        Map<Long, MarketItemSalesSummaryExcelDTO> itemSummaryMap = mergeItemMap(currentSettleItemMap, billSettleItemMap);
        Collection<MarketItemSalesSummaryExcelDTO> allMarketItemSummaries = itemSummaryMap.values();
        excelWriter.fill(allMarketItemSummaries, allSheets);
    }

    private Map<Long, MarketItemSalesSummaryExcelDTO> mergeItemMap(Map<Long, MarketItemSalesSummaryExcelDTO> currentSettleItemMap, Map<Long, MarketItemSalesSummaryExcelDTO> billSettleItemMap) {
        Map<Long, MarketItemSalesSummaryExcelDTO> itemSummaryMap = new HashMap<>(currentSettleItemMap);
        billSettleItemMap.forEach((k, v) -> {
            itemSummaryMap.computeIfAbsent(k, key -> new MarketItemSalesSummaryExcelDTO());
            MarketItemSalesSummaryExcelDTO marketSummaryDTO = itemSummaryMap.get(k);
            Integer totalAmount = Objects.isNull(marketSummaryDTO.getTotalAmount()) ? v.getTotalAmount() : marketSummaryDTO.getTotalAmount() + v.getTotalAmount();
            marketSummaryDTO.setTotalAmount(totalAmount);
            marketSummaryDTO.setTotalPrice(NumberUtil.add(marketSummaryDTO.getTotalPrice(), v.getTotalPrice()));
            BigDecimal averagePayablePrice = totalAmount == 0 ? BigDecimal.ZERO : NumberUtil.div(marketSummaryDTO.getTotalPrice(), totalAmount, 2, RoundingMode.valueOf(ROUND_HALF_UP));
            marketSummaryDTO.setAveragePayablePrice(averagePayablePrice);
            marketSummaryDTO.setTotalRefundPrice(NumberUtil.add(marketSummaryDTO.getTotalRefundPrice(), v.getTotalRefundPrice()));
            marketSummaryDTO.setTotalPriceDeductedRefund(NumberUtil.add(marketSummaryDTO.getTotalPriceDeductedRefund(), v.getTotalPriceDeductedRefund()));
            marketSummaryDTO.setGoodsTotalSupplyPrice(NumberUtil.add(marketSummaryDTO.getGoodsTotalSupplyPrice(), v.getGoodsTotalSupplyPrice()));
            BigDecimal goodAverageSupplyPrice = totalAmount == 0 ? BigDecimal.ZERO : NumberUtil.div(marketSummaryDTO.getGoodsTotalSupplyPrice(), totalAmount, 2, RoundingMode.valueOf(ROUND_HALF_UP));
            marketSummaryDTO.setGoodAverageSupplyPrice(goodAverageSupplyPrice);
            marketSummaryDTO.setGoodsRefundPrice(NumberUtil.add(marketSummaryDTO.getGoodsRefundPrice(), v.getGoodsRefundPrice()));
            marketSummaryDTO.setGoodsPriceDeductedRefund(NumberUtil.add(marketSummaryDTO.getGoodsPriceDeductedRefund(), v.getGoodsPriceDeductedRefund()));
            marketSummaryDTO.setSalesAndSupplyDifferenceDeductedPrice(NumberUtil.add(marketSummaryDTO.getSalesAndSupplyDifferenceDeductedPrice(), v.getSalesAndSupplyDifferenceDeductedPrice()));
        });
        return itemSummaryMap;
    }

    private void accumulateSummary(MarketItemSalesSummaryExcelDTO marketSummaryDTO, OrderItemDetailSummaryExcelDTO dto) {
        marketSummaryDTO.setItemId(dto.getItemId());
        marketSummaryDTO.setItemCode(dto.getItemCode());
        marketSummaryDTO.setItemTitle(dto.getItemTitle());
        marketSummaryDTO.setItemSpecification(dto.getItemSpecification());
        Integer totalAmount = Objects.isNull(marketSummaryDTO.getTotalAmount()) ? dto.getAmount() : marketSummaryDTO.getTotalAmount() + dto.getAmount();
        marketSummaryDTO.setTotalAmount(totalAmount);
        marketSummaryDTO.setTotalPrice(NumberUtil.add(marketSummaryDTO.getTotalPrice(), dto.getTotalPrice()));
        BigDecimal averagePayablePrice = totalAmount == 0 ? BigDecimal.ZERO : NumberUtil.div(marketSummaryDTO.getTotalPrice(), totalAmount, 2, RoundingMode.valueOf(ROUND_HALF_UP));
        marketSummaryDTO.setAveragePayablePrice(averagePayablePrice);
        marketSummaryDTO.setTotalRefundPrice(NumberUtil.add(marketSummaryDTO.getTotalRefundPrice(), dto.getItemRefundPrice()));
        BigDecimal totalPriceDeductedRefund = NumberUtil.add(dto.getTotalPriceDeductedRefund(), dto.getDeliveryFee().negate(), dto.getDeliveryRefundFee());
        marketSummaryDTO.setTotalPriceDeductedRefund(NumberUtil.add(marketSummaryDTO.getTotalPriceDeductedRefund(), totalPriceDeductedRefund));
        marketSummaryDTO.setGoodsTotalSupplyPrice(NumberUtil.add(marketSummaryDTO.getGoodsTotalSupplyPrice(), dto.getGoodsSupplyTotalPrice()));
        BigDecimal goodAverageSupplyPrice = totalAmount == 0 ? BigDecimal.ZERO : NumberUtil.div(marketSummaryDTO.getGoodsTotalSupplyPrice(), totalAmount, 2, RoundingMode.valueOf(ROUND_HALF_UP));
        marketSummaryDTO.setGoodAverageSupplyPrice(goodAverageSupplyPrice);
        marketSummaryDTO.setGoodsRefundPrice(NumberUtil.add(marketSummaryDTO.getGoodsRefundPrice(), dto.getGoodsRefundPrice()));
        marketSummaryDTO.setGoodsPriceDeductedRefund(NumberUtil.add(marketSummaryDTO.getGoodsPriceDeductedRefund(), dto.getGoodsTotalPriceDeductedDeliveryFee()));
        marketSummaryDTO.setSalesAndSupplyDifferenceDeductedPrice(NumberUtil.add(marketSummaryDTO.getSalesAndSupplyDifferenceDeductedPrice(), dto.getSalesAndSupplyDifferenceDeductedRefund()));
    }

    private void generateSpecialBillSummary(BillSupplierDirectAssignSummary billSupplierDirectAssignSummary, TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, Boolean needHiddenSheet) {
        if (needHiddenSheet) {  // NOSONAR
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.setSheetHidden(0, true);
            return;
        }
        WriteSheet summarySheet = EasyExcel.writerSheet(0).build();
        BillSummaryExcelDTO billSummaryExcelDTO = BillConverter.convertToExcelDTO(billSupplierDirectAssignSummary);
        String supplierName = getSupplierName(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId());
        billSummaryExcelDTO.setBillStartTime(tenantBillQueryDTO.getStartTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        billSummaryExcelDTO.setBillEndTime(tenantBillQueryDTO.getEndTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        billSummaryExcelDTO.setSupplierName(supplierName);

        // 从订单明细汇总查询各类支付门店数
        querySummaryFromOrderItem(billSummaryExcelDTO, tenantBillQueryDTO);

        excelWriter.fill(billSummaryExcelDTO, summarySheet);
    }

    private BillSummaryBO generateSpecialOrderItemSheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, String tempExcelFilePath, Boolean needHiddenSheet) {
        WriteSheet orderItemDetailSummarySheet = EasyExcel.writerSheet(1).build();
        Set<String> thisPeriodOrderNos = Sets.newHashSetWithExpectedSize(300);
        BillSupplierDirectAssignSummary billSupplierDirectAssignSummary = new BillSupplierDirectAssignSummary(Boolean.TRUE);
        Map<Integer, Map<Long, MarketItemSalesSummaryExcelDTO>> itemSummaryMap = Maps.newHashMapWithExpectedSize(50);
        ExcelLargeDataSetExporter<OrderItemDetailSummary, OrderItemDetailSummaryExcelDTO> orderItemDetailSummaryHandler = new ExcelLargeDataSetExporter<OrderItemDetailSummary, OrderItemDetailSummaryExcelDTO>(excelWriter, orderItemDetailSummarySheet, tempExcelFilePath, needHiddenSheet) {
            @Override
            protected List<OrderItemDetailSummaryExcelDTO> convert(OrderItemDetailSummary data) {
                // 1、当周期的售后明细
                OrderItemDetailSummaryExcelDTO orderItemDetailSummaryExcelDTO = convertToOrderItemDetailSummaryExcelDTO(tenantBillQueryDTO, data, Boolean.TRUE, Boolean.FALSE);
                thisPeriodOrderNos.add(orderItemDetailSummaryExcelDTO.getOrderNo());
                // 2、统计汇总
                summarization(billSupplierDirectAssignSummary, itemSummaryMap, orderItemDetailSummaryExcelDTO);
                return Arrays.asList(orderItemDetailSummaryExcelDTO);
            }
        };
        orderItemDetailSummaryService.queryByConditionWithHandler(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime(), tenantBillQueryDTO.getGoodsType(), orderItemDetailSummaryHandler);
        orderItemDetailSummaryHandler.clearData();

        // 2.1、售后跨期处理 把订单号过滤出来
        List<OrderAfterSaleDetailSummary> orderAfterSaleDetailSummaries = getInterTemporalOrderNos(tenantBillQueryDTO, thisPeriodOrderNos);
        if (!CollectionUtils.isEmpty(orderAfterSaleDetailSummaries)) {
            List<String> orderNos = orderAfterSaleDetailSummaries.stream().map(OrderAfterSaleDetailSummary::getOrderNo).collect(Collectors.toList());
            List<OrderItemDetailSummary> orderItemDetailSummaries = orderItemDetailSummaryService.queryByCondition(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getGoodsType(), orderNos);
            // 2.2、转换成excelDTO
            List<OrderItemDetailSummaryExcelDTO> orderItemDetailSummaryExcelDTOS = orderItemDetailSummaries.stream()
                    .map(data -> convertToOrderItemDetailSummaryExcelDTO(tenantBillQueryDTO, data, Boolean.TRUE, Boolean.TRUE))
                    .filter(el -> el.getItemRefundPrice().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            // 2.3 统计汇总
            orderItemDetailSummaryExcelDTOS.forEach(dto -> summarization(billSupplierDirectAssignSummary, itemSummaryMap, dto));
            // 2.4、填充
            excelWriter.fill(orderItemDetailSummaryExcelDTOS, orderItemDetailSummarySheet);
        }

        // 3、最后finish掉
        orderItemDetailSummaryHandler.finish(false);

        return BillSummaryBO.builder().billSupplierDirectAssignSummary(billSupplierDirectAssignSummary).itemSummaryMap(itemSummaryMap).build();
    }

    private void summarization(BillSupplierDirectAssignSummary billSupplierDirectAssignSummary, Map<Integer, Map<Long, MarketItemSalesSummaryExcelDTO>> itemPaySummaryMap, OrderItemDetailSummaryExcelDTO dto) {
        billSupplierDirectAssignSummary.setTotalPurchaseAmount(NumberUtil.add(billSupplierDirectAssignSummary.getTotalPurchaseAmount(), dto.getGoodsSupplyTotalPrice()));
        boolean isCurrentSettlement = com.cosfo.manage.common.context.PayTypeEnum.isCurrentSettlement(dto.getPayType());
        if (isCurrentSettlement) {
            billSupplierDirectAssignSummary.setTotalPurchaseAmountWechatPay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalPurchaseAmountWechatPay(), dto.getGoodsSupplyTotalPrice()));
            billSupplierDirectAssignSummary.setTotalAmountOfPurchaseAndAfterSalesWechatPay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalAmountOfPurchaseAndAfterSalesWechatPay(), dto.getGoodsRefundPrice()));
            BigDecimal totalAmountOfPurchaseAndAfterSalesWechatPay = NumberUtil.sub(dto.getGoodsSupplyTotalPrice(), dto.getGoodsRefundPrice());
            billSupplierDirectAssignSummary.setTotalPurchaseAmountRemoveRefundWechatPay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalPurchaseAmountRemoveRefundWechatPay(), totalAmountOfPurchaseAndAfterSalesWechatPay));
        } else {
            billSupplierDirectAssignSummary.setTotalPurchaseAmountBillBalancePay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalPurchaseAmountBillBalancePay(), dto.getGoodsSupplyTotalPrice()));
            billSupplierDirectAssignSummary.setTotalAmountOfPurchaseAndAfterSalesBillBalancePay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalAmountOfPurchaseAndAfterSalesBillBalancePay(), dto.getGoodsRefundPrice()));
            BigDecimal totalAmountOfPurchaseAndAfterSalesBalancePay = NumberUtil.sub(dto.getGoodsSupplyTotalPrice(), dto.getGoodsRefundPrice());
            billSupplierDirectAssignSummary.setTotalPurchaseAmountRemoveRefundBillBalancePay(NumberUtil.add(billSupplierDirectAssignSummary.getTotalPurchaseAmountRemoveRefundBillBalancePay(), totalAmountOfPurchaseAndAfterSalesBalancePay));
        }
        // 货品配送费-售后
        BigDecimal goodsDeliveryFeeRemoveRefund = NumberUtil.sub(dto.getGoodsDeliveryFee(), dto.getGoodsDeliveryFeeRefund());
        billSupplierDirectAssignSummary.setGoodsDeliveryFeeRemoveRefund(NumberUtil.add(billSupplierDirectAssignSummary.getGoodsDeliveryFeeRemoveRefund(), goodsDeliveryFeeRemoveRefund));

        // 商城商品销售
        billSupplierDirectAssignSummary.setTotalSalesAmount(NumberUtil.add(billSupplierDirectAssignSummary.getTotalSalesAmount(), dto.getTotalPrice()));
        billSupplierDirectAssignSummary.setTotalSalesAmountWechatPay(isCurrentSettlement ? NumberUtil.add(billSupplierDirectAssignSummary.getTotalSalesAmountWechatPay(), dto.getTotalPrice()) : billSupplierDirectAssignSummary.getTotalSalesAmountWechatPay());
        billSupplierDirectAssignSummary.setTotalSalesAmountBillBalancePay(isCurrentSettlement ? billSupplierDirectAssignSummary.getTotalSalesAmountBillBalancePay() : NumberUtil.add(billSupplierDirectAssignSummary.getTotalSalesAmountBillBalancePay(), dto.getTotalPrice()));
        // 商城商品售后
        billSupplierDirectAssignSummary.setAfterSaleAmountWechatPay(isCurrentSettlement ? NumberUtil.add(billSupplierDirectAssignSummary.getAfterSaleAmountWechatPay(), dto.getItemRefundPrice()) : billSupplierDirectAssignSummary.getAfterSaleAmountWechatPay());
        billSupplierDirectAssignSummary.setAfterSaleAmountBillBalancePay(isCurrentSettlement ? billSupplierDirectAssignSummary.getAfterSaleAmountBillBalancePay() : NumberUtil.add(billSupplierDirectAssignSummary.getAfterSaleAmountBillBalancePay(), dto.getItemRefundPrice()));
        // 商城商品销售金额-售后
        BigDecimal deductAfterSalesAmount = NumberUtil.sub(dto.getTotalPrice(), dto.getItemRefundPrice());
        billSupplierDirectAssignSummary.setDeductAfterSalesAmountWechatPay(isCurrentSettlement ? NumberUtil.add(billSupplierDirectAssignSummary.getDeductAfterSalesAmountWechatPay(), deductAfterSalesAmount) : billSupplierDirectAssignSummary.getDeductAfterSalesAmountWechatPay());
        billSupplierDirectAssignSummary.setDeductAfterSalesAmountBillBalancePay(isCurrentSettlement ? billSupplierDirectAssignSummary.getDeductAfterSalesAmountBillBalancePay() : NumberUtil.add(billSupplierDirectAssignSummary.getDeductAfterSalesAmountBillBalancePay(), deductAfterSalesAmount));
        // 配送费金额-售后
        BigDecimal deliveryFeeDeductAfterSalesAmount = NumberUtil.sub(dto.getDeliveryFee(), dto.getDeliveryRefundFee());
        billSupplierDirectAssignSummary.setDeliveryFeeDeductAfterSalesAmount(NumberUtil.add(billSupplierDirectAssignSummary.getDeliveryFeeDeductAfterSalesAmount(), deliveryFeeDeductAfterSalesAmount));
        // 销售与采购差额
        BigDecimal salesAndPurchaseDifference = NumberUtil.sub(dto.getTotalPrice(), dto.getItemRefundPrice(), NumberUtil.sub(dto.getGoodsSupplyTotalPrice(), dto.getGoodsRefundPrice()));
        billSupplierDirectAssignSummary.setSalesAndPurchaseDifferenceWechatPay(isCurrentSettlement ? NumberUtil.add(billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceWechatPay(), salesAndPurchaseDifference) : billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceWechatPay());
        billSupplierDirectAssignSummary.setSalesAndPurchaseDifferenceBillBalancePay(isCurrentSettlement ? billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceBillBalancePay() : NumberUtil.add(billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceBillBalancePay(), salesAndPurchaseDifference));

        // 商品汇总
        Integer currentSettleFlag = com.cosfo.manage.common.context.PayTypeEnum.getCurrentSettleFlag(dto.getPayType());
        itemPaySummaryMap.computeIfAbsent(currentSettleFlag, k -> new HashMap<>());
        Map<Long, MarketItemSalesSummaryExcelDTO> itemSummaryMap = itemPaySummaryMap.get(currentSettleFlag);
        itemSummaryMap.computeIfAbsent(dto.getItemId(), k -> new MarketItemSalesSummaryExcelDTO());
        MarketItemSalesSummaryExcelDTO marketSummaryDTO = itemSummaryMap.get(dto.getItemId());
        accumulateSummary(marketSummaryDTO, dto);
    }



    private List<OrderAfterSaleDetailSummary> getInterTemporalOrderNos(TenantBillQueryDTO tenantBillQueryDTO, Set<String> thisPeriodOrderNos) {
        OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO = new OrderAfterSaleDetailSummaryQueryDTO();
        orderAfterSaleDetailSummaryQueryDTO.setTenantId(tenantBillQueryDTO.getTenantId());
        orderAfterSaleDetailSummaryQueryDTO.setStartTime(tenantBillQueryDTO.getStartTime());
        orderAfterSaleDetailSummaryQueryDTO.setEndTime(tenantBillQueryDTO.getEndTime());
        orderAfterSaleDetailSummaryQueryDTO.setSupplierId(tenantBillQueryDTO.getSupplierId());
        orderAfterSaleDetailSummaryQueryDTO.setExcludeOrderNoList(thisPeriodOrderNos);
        orderAfterSaleDetailSummaryQueryDTO.setGoodsType(tenantBillQueryDTO.getGoodsType());
        orderAfterSaleDetailSummaryQueryDTO.setSpecialFlag(true);
        return orderAfterSaleDetailSummaryService.queryByCondition(orderAfterSaleDetailSummaryQueryDTO);
    }


    private Map<String, String> processFileParameters(TenantBillQueryDTO tenantBillQueryDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.FOUR);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 起始时间
        if (Objects.nonNull(tenantBillQueryDTO.getStartTime())) {
            String dateStr = formatter.format(tenantBillQueryDTO.getStartTime());
            queryParamsMap.put(Constants.ORDER_START_TIME, dateStr);
        }
        // 截止时间
        if (Objects.nonNull(tenantBillQueryDTO.getEndTime())) {
            String dateStr = formatter.format(tenantBillQueryDTO.getEndTime());
            queryParamsMap.put(Constants.ORDER_END_TIME, dateStr);
        }
        // 供应商租户
        if (Objects.nonNull(tenantBillQueryDTO.getSupplierId())) {
            String supplierName = getSupplierName(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId());
            queryParamsMap.put(Constants.SUPPLIER_NAME, supplierName);
            tenantBillQueryDTO.setSupplierName(supplierName);
        }

        // boss后台-供应商应收-导出
        if("cosfo-oms".equals(tenantBillQueryDTO.getReqSource())){
            TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantBillQueryDTO.getTenantId());
            queryParamsMap.put("品牌方", tenant != null ? tenant.getTenantName() : "");
        }
        return queryParamsMap;
    }

    private void processRequestParams(TenantBillQueryDTO tenantBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(tenantBillQueryDTO.getStartTime()) || Objects.isNull(tenantBillQueryDTO.getEndTime())) {
            throw new ParamsException("请填写起止日期再进行导出");
        }
        if (Objects.isNull(tenantBillQueryDTO.getType())) {
            throw new ParamsException("请选择导出的账单类型");
        }
        if (Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode()) && CollectionUtils.isEmpty(tenantBillQueryDTO.getExportContentType())) {
            throw new ParamsException("请选择您要导出的报表内容");
        }
        if (Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode()) && Objects.isNull(tenantBillQueryDTO.getSupplierId())) {
            throw new ParamsException("请选择您要导出的供应商");
        }

        tenantBillQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        Integer goodsType = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.AGENT_WAREHOUSE_BILL.getCode()) ? GoodsTypeEnum.SELF_GOOD_TYPE.getCode() : Objects.equals(tenantBillQueryDTO.getSupplierId(), SupplierTenantEnum.XM.getId()) ? GoodsTypeEnum.QUOTATION_TYPE.getCode() : GoodsTypeEnum.NO_GOOD_TYPE.getCode();
        tenantBillQueryDTO.setGoodsType(goodsType);
    }

    private String getSupplierName(Long tenantId, Long supplierId) {
        if (supplierId != null && supplierId > SupplierTenantEnum.XM.getId()) {
            Map<Long, SupplierInfoDTO> supplierInfoDTOMap = supplierService.batchQuerySupplierMap(tenantId, Arrays.asList(supplierId));
            SupplierInfoDTO supplierInfoDTO = supplierInfoDTOMap.get(supplierId);
            return Objects.isNull(supplierInfoDTO) ? "" : supplierInfoDTO.getSupplierName();
        }
        return SupplierTenantEnum.XM.getName();
    }

    private String generateStatementExportFile(TenantBillQueryDTO tenantBillQueryDTO) {
        // 文件名
//        String fileName = getStatementFileName(tenantBillQueryDTO);
        //String fileName = "测试" + System.currentTimeMillis();
        String tempExcelFilePath = ExcelUtils.tempExcelFilePath();

        // 选择文件模板 直配订单汇总、直配订单商品汇总、代仓模板
        ExcelTypeEnum excelTypeEnum = chooseFileTemplate(tenantBillQueryDTO);
        log.info("采购应付导出,excel模版:{},excel枚举:{}", excelTypeEnum.getName(), excelTypeEnum);
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), excelTypeEnum.getName());
        ExcelWriter excelWriter = EasyExcel.write(tempExcelFilePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();

        if (Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.AGENT_WAREHOUSE_BILL.getCode())) {
            // 生成代仓账单
            generateAgentWarehouseBill(tenantBillQueryDTO, excelWriter, tempExcelFilePath);
        } else {
            // 生成供应商直配账单
            generateSupplierDirectAssignBill(tenantBillQueryDTO, excelWriter, tempExcelFilePath);
        }

        return tempExcelFilePath;
        // 上传文件
//        commonService.uploadExcelAndUpdateDownloadStatus(tempExcelFilePath, fileName + System.currentTimeMillis(), ExcelTypeEnum.STATEMENT, fileDownloadRecordId);
    }

    private ExcelTypeEnum chooseFileTemplate(TenantBillQueryDTO tenantBillQueryDTO) {
        Integer billType = tenantBillQueryDTO.getType();
        List<Integer> exportContentTypes = tenantBillQueryDTO.getExportContentType();
        if (CollectionUtils.isEmpty(exportContentTypes)) {
            return ExcelTypeEnum.AGENT_WAREHOUSE_BILL;
        }
        final boolean orderSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ORDER_SUMMARY.getType());
        final boolean itemSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ITEM_SUMMARY.getType());
        final boolean supplierDirectAssignBillFlag = Objects.equals(billType, BillTypeEnum.DIRECT_ASSIGN_BILL.getCode());
        final boolean xmSupplierFlag = Objects.nonNull(tenantBillQueryDTO.getSupplierId()) && tenantBillQueryDTO.getSupplierId().equals(SupplierTenantEnum.XM.getId());
        return ExcelTypeEnum.chooseStatementExcel(orderSummaryFlag, itemSummaryFlag, supplierDirectAssignBillFlag, xmSupplierFlag);
    }

    /**
     * 生成供应商直配账单
     *
     * @param tenantBillQueryDTO  租户账单查询dto
     */
    private void generateSupplierDirectAssignBill(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, String tempExcelFilePath) {
        List<Integer> exportContentTypes = tenantBillQueryDTO.getExportContentType();
        final boolean orderSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ORDER_SUMMARY.getType());
        final boolean itemSummaryFlag = exportContentTypes.contains(StatementContentTypeEnum.ITEM_SUMMARY.getType());
        boolean needFinishWriter = !itemSummaryFlag;
        // 页计数器
        SheetCounter sheetCounter = new SheetCounter(0);

        if (orderSummaryFlag) {
            // 生成订单汇总页
            generateOrderSummarySheets(tenantBillQueryDTO, excelWriter, needFinishWriter, tempExcelFilePath, sheetCounter);
        }

        if (itemSummaryFlag) {
            // 生成商品汇总页
            generateItemSummarySheets(tenantBillQueryDTO, excelWriter, sheetCounter.getSheetNo(), tempExcelFilePath);
        }
    }

    private void generateAgentWarehouseBill(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, String tempExcelFilePath) {
        // 页计数器
        SheetCounter sheetCounter = new SheetCounter(0);

        // 生成代仓概要
        generateAgentWarehouseSummarySheet(tenantBillQueryDTO, excelWriter, sheetCounter);

        // 生成订单明细页
        generateOrderItemDetailSummarySheet(tenantBillQueryDTO, excelWriter, tempExcelFilePath, sheetCounter);

        // 生成售后明细页
        generateOrderAfterSaleDetailSummarySheet(tenantBillQueryDTO, excelWriter, true, tempExcelFilePath, sheetCounter);
    }

    private void generateAgentWarehouseSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, SheetCounter sheetCounter) {
        // 代仓汇总页
        BillAgentWarehouseSummary billAgentWarehouseSummary = billAgentWarehouseSummaryRepository.querySummary(tenantBillQueryDTO);
        BillSummaryExcelDTO billSummaryExcelDTO = BillConverter.convertToExcelDTO(billAgentWarehouseSummary);

        billSummaryExcelDTO.setBillStartTime(tenantBillQueryDTO.getStartTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        billSummaryExcelDTO.setBillEndTime(tenantBillQueryDTO.getEndTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        String supplierName = getSupplierName(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId());
        billSummaryExcelDTO.setSupplierName(supplierName);

        // 从订单明细汇总查询各类支付门店数
        querySummaryFromOrderItem(billSummaryExcelDTO, tenantBillQueryDTO);

        WriteSheet summarySheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        excelWriter.fill(billSummaryExcelDTO, summarySheet);
        sheetCounter.addCount();
    }

    private void generateItemSummarySheets(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, Integer sheetNo, String tempExcelFilePath) {
        MarketItemSummaryQueryDTO marketItemSummaryQueryDTO = new MarketItemSummaryQueryDTO();
        marketItemSummaryQueryDTO.setTenantId(tenantBillQueryDTO.getTenantId());
        marketItemSummaryQueryDTO.setSupplierId(tenantBillQueryDTO.getSupplierId());
        marketItemSummaryQueryDTO.setStartTime(tenantBillQueryDTO.getStartTime());
        marketItemSummaryQueryDTO.setEndTime(tenantBillQueryDTO.getEndTime());

        // 生成商品汇总页
        generateItemSummarySheet(marketItemSummaryQueryDTO, excelWriter, sheetNo++, tempExcelFilePath, false);

        // 微信支付商品汇总页
        marketItemSummaryQueryDTO.setPayTypes(Arrays.asList(PayTypeEnum.WECHAT_PAY.getCode()));
        generateItemSummarySheet(marketItemSummaryQueryDTO, excelWriter, sheetNo++, tempExcelFilePath, false);

        // 账期余额支付汇总页
        marketItemSummaryQueryDTO.setPayTypes(Arrays.asList(PayTypeEnum.BILL.getCode(), PayTypeEnum.BALANCE.getCode()));
        generateItemSummarySheet(marketItemSummaryQueryDTO, excelWriter, sheetNo++, tempExcelFilePath, true);
    }

    private void generateItemSummarySheet(MarketItemSummaryQueryDTO marketItemSummaryQueryDTO, ExcelWriter excelWriter, int sheetNo, String tempExcelFilePath, boolean needFinishedWriter) {
        // 对账单商品汇总
        WriteSheet marketItemSalesSummarySheet = EasyExcel.writerSheet(sheetNo++).build();
        ExcelLargeDataSetExporter<MarketItemSalesSummary, MarketItemSalesSummaryExcelDTO> marketItemSalesSummaryHandler = new ExcelLargeDataSetExporter<MarketItemSalesSummary, MarketItemSalesSummaryExcelDTO>(excelWriter, marketItemSalesSummarySheet, tempExcelFilePath) {
            @Override
            protected List<MarketItemSalesSummaryExcelDTO> convert(MarketItemSalesSummary data) {
                MarketItemSalesSummaryExcelDTO marketItemSalesSummaryExcelDTO = ReportConverter.MarketItemSalesSummary2ExcelDTO(data);
                return Arrays.asList(marketItemSalesSummaryExcelDTO);
            }
        };

        marketItemSalesSummaryService.queryByConditionWithHandler(marketItemSummaryQueryDTO, marketItemSalesSummaryHandler);
        marketItemSalesSummaryHandler.finish(needFinishedWriter);
    }

    private void generateOrderSummarySheets(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, boolean needFinishWriter, String tempExcelFilePath, SheetCounter sheetCounter) {
        // 生成概要
        generateSummarySheet(tenantBillQueryDTO, excelWriter, sheetCounter);

        // 生成订单明细页
        generateOrderItemDetailSummarySheet(tenantBillQueryDTO, excelWriter, tempExcelFilePath, sheetCounter);

        // 生成售后明细页
        generateOrderAfterSaleDetailSummarySheet(tenantBillQueryDTO, excelWriter, false, tempExcelFilePath, sheetCounter);

        // 生成分账差额页
        generateInvertedSummarySheet(tenantBillQueryDTO, excelWriter, false, tempExcelFilePath, sheetCounter);

        // 生成退款分账差额页
        generateInvertedRefundSummarySheet(tenantBillQueryDTO, excelWriter, needFinishWriter, tempExcelFilePath, sheetCounter);
    }

    private void generateInvertedSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, boolean needFinishWriter, String tempExcelFilePath, SheetCounter sheetCounter) {
        // 生成分账差额页
        WriteSheet orderProfitSharingDifferenceSheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        long count = orderProfitSharingDifferenceService.count(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime());
        ExcelLargeDataSetExporter<OrderProfitSharingDifference, OrderProfitSharingDifferenceExcelDTO> orderProfitSharingDifferenceHandler = new ExcelLargeDataSetExporter<OrderProfitSharingDifference, OrderProfitSharingDifferenceExcelDTO>(excelWriter, orderProfitSharingDifferenceSheet, tempExcelFilePath, count == 0) {
            @Override
            protected List<OrderProfitSharingDifferenceExcelDTO> convert(OrderProfitSharingDifference data) {
                OrderProfitSharingDifferenceExcelDTO excelDTO = BillConverter.convertToExcelDTO(data);
                return Collections.singletonList(excelDTO);
            }
        };

        orderProfitSharingDifferenceService.queryByConditionWithHandler(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime(), orderProfitSharingDifferenceHandler);
        orderProfitSharingDifferenceHandler.finish(needFinishWriter);
        sheetCounter.addCount();
    }

    /**
     * 生成退款分账差额页
     *
     * @param tenantBillQueryDTO
     * @param excelWriter
     * @param needFinishWriter
     * @param tempExcelFilePath
     */
    private void generateInvertedRefundSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, boolean needFinishWriter, String tempExcelFilePath, SheetCounter sheetCounter){
        // 售价低于供应价差额订单汇总
        WriteSheet OrderAfterSaleInvertedSummarySheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        long count = orderAfterSaleInvertedSummaryService.count(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime());
        ExcelLargeDataSetExporter<OrderAfterSaleInvertedSummary, OrderAfterSaleInvertedSummaryDTO> OrderAfterSaleInvertedSummaryHandler = new ExcelLargeDataSetExporter<OrderAfterSaleInvertedSummary, OrderAfterSaleInvertedSummaryDTO>(excelWriter, OrderAfterSaleInvertedSummarySheet, tempExcelFilePath, count == 0) {
            @Override
            protected List<OrderAfterSaleInvertedSummaryDTO> convert(OrderAfterSaleInvertedSummary data) {
                OrderAfterSaleInvertedSummaryDTO orderAfterSaleInvertedSummaryDTO = ReportConverter.toOrderAfterSaleInvertedSummaryDTO(data);
                return Arrays.asList(orderAfterSaleInvertedSummaryDTO);
            }
        };

        orderAfterSaleInvertedSummaryService.queryByConditionWithHandler(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime(), OrderAfterSaleInvertedSummaryHandler);
        OrderAfterSaleInvertedSummaryHandler.finish(needFinishWriter);
        sheetCounter.addCount();
    }

    private void generateOrderAfterSaleDetailSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, Boolean needFinishWriter, String tempExcelFilePath, SheetCounter sheetCounter) {
        // 售后明细汇总
        WriteSheet orderAfterSaleDetailSummarySheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        boolean agentWarehouseFlag = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.AGENT_WAREHOUSE_BILL.getCode());
        boolean otherSupplierFlag = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode()) && !tenantBillQueryDTO.getSupplierId().equals(SupplierTenantEnum.XM.getId());

        ExcelLargeDataSetExporter<OrderAfterSaleDetailSummary, OrderAfterSaleDetailSummaryExcelDTO> orderAfterSaleDetailSummaryHandler = new ExcelLargeDataSetExporter<OrderAfterSaleDetailSummary, OrderAfterSaleDetailSummaryExcelDTO>(excelWriter, orderAfterSaleDetailSummarySheet, tempExcelFilePath) {
            @Override
            protected List<OrderAfterSaleDetailSummaryExcelDTO> convert(OrderAfterSaleDetailSummary data) {
                return null;
            }

            @Override
            protected int getSize() {
                return 100;
            }

            @Override
            protected List<OrderAfterSaleDetailSummaryExcelDTO> convertBatch(Collection<OrderAfterSaleDetailSummary> dataList) {
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
                    return Collections.EMPTY_LIST;
                }
                List<String> afterSaleOrderNoList = dataList.stream().map(OrderAfterSaleDetailSummary::getAfterSaleOrderNo).distinct().collect(Collectors.toList());
                List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(afterSaleOrderNoList);
                Map<String, OrderAfterSaleResp> afterSaleMap = orderAfterSaleResps.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, Function.identity(),(v1,v2)->v1));
                Map<String, Long> orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, OrderAfterSaleResp::getOrderItemId));
                List<Long> orderItemIdList = orderAfterSaleResps.stream().map(OrderAfterSaleResp::getOrderItemId).collect(Collectors.toList());
                List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIdList);
                Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, Function.identity(), (v1, v2) -> v1));

                return dataList.stream().map(data -> {
                    OrderAfterSaleDetailSummaryExcelDTO orderAfterSaleDetailSummaryExcelDTO = ReportConverter.OrderAfterSaleDetailSummary2ExcelDTO(data);
                    if (Objects.equals(data.getResponsibilityType(), ResponsibilityTypeEnum.SUPPLIER.getType())) {
                        orderAfterSaleDetailSummaryExcelDTO.setResponsibilityTypeDesc(agentWarehouseFlag ? "代仓服务商" : "供应商");
                    }
                    orderAfterSaleDetailSummaryExcelDTO.setGoodsSupplierName(otherSupplierFlag ? tenantBillQueryDTO.getSupplierName() : orderAfterSaleDetailSummaryExcelDTO.getGoodsSupplierName());
                    String handleRemark = Optional.ofNullable(afterSaleMap.get(data.getAfterSaleOrderNo())).map(OrderAfterSaleResp::getHandleRemark).orElse("");
                    orderAfterSaleDetailSummaryExcelDTO.setHandleRemark(handleRemark);
                    builderAfterSaleUnit(orderAfterSaleDetailSummaryExcelDTO, orderAfterSaleMap, orderItemSnapshotDTOMap);
                    return orderAfterSaleDetailSummaryExcelDTO;
                }).collect(Collectors.toList());
            }

            /**
             * 组装售后单的售后单位
             * @param dto
             * @param orderAfterSaleMap
             * @param orderItemSnapshotDTOMap
             */
            private void builderAfterSaleUnit(OrderAfterSaleDetailSummaryExcelDTO dto, Map<String, Long> orderAfterSaleMap, Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap) {
                OrderItemSnapshotResp orderItemSnapshotDTO = Optional.ofNullable(orderAfterSaleMap.get(dto.getAfterSaleOrderNo())).map(orderItemId -> orderItemSnapshotDTOMap.get(orderItemId)).orElse(null);
                if (Objects.isNull(orderItemSnapshotDTO)) {
                    log.info("组装售后单位异常，未找到售后单订单信息 afterSaleOrderNo:{}", dto.getAfterSaleOrderNo());
                    return;
                }
                dto.setAfterSaleUnit(OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(dto.getServiceType(), dto.getAfterSaleType()) ? orderItemSnapshotDTO.getAfterSaleUnit() : orderItemSnapshotDTO.getSpecificationUnit());
            }
        };
        orderAfterSaleDetailSummaryService.queryByConditionWithHandler(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime(), tenantBillQueryDTO.getGoodsType(), orderAfterSaleDetailSummaryHandler);
        orderAfterSaleDetailSummaryHandler.finish(needFinishWriter);
        sheetCounter.addCount();
    }

    private void generateSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, SheetCounter sheetCounter) {
        // 供应商直配的汇总
        BillSupplierDirectAssignSummary billSupplierDirectAssignSummary = billSupplierDirectAssignSummaryRepository.querySummary(tenantBillQueryDTO);
        boolean otherSupplierFlag = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode()) && !tenantBillQueryDTO.getSupplierId().equals(SupplierTenantEnum.XM.getId());
        if (otherSupplierFlag) {
            billSupplierDirectAssignSummary.setGoodsDeliveryFeeRemoveRefund(null);
        }
        BillSummaryExcelDTO billSummaryExcelDTO = BillConverter.convertToExcelDTO(billSupplierDirectAssignSummary);
        String supplierName = getSupplierName(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId());
        billSummaryExcelDTO.setSupplierName(supplierName);
        billSummaryExcelDTO.setBillStartTime(tenantBillQueryDTO.getStartTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        billSummaryExcelDTO.setBillEndTime(tenantBillQueryDTO.getEndTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));

        // 从订单明细汇总查询各类支付门店数
        querySummaryFromOrderItem(billSummaryExcelDTO, tenantBillQueryDTO);

        WriteSheet summarySheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        excelWriter.fill(billSummaryExcelDTO, summarySheet);
        sheetCounter.addCount();
    }

    private void querySummaryFromOrderItem(BillSummaryExcelDTO billSummaryExcelDTO, TenantBillQueryDTO tenantBillQueryDTO) {
        List<OrderPaymentStatistics> orderPaymentStatistics = orderItemDetailSummaryService.queryStoreCounts(tenantBillQueryDTO);
        Map<Integer, Integer> orderPaymentMap = orderPaymentStatistics.stream().collect(Collectors.toMap(OrderPaymentStatistics::getPayType, OrderPaymentStatistics::getStoreCounts));
        Integer wechatPayStoreCounts = Optional.ofNullable(orderPaymentMap.get(PayTypeEnum.WECHAT_PAY.getCode())).orElse(0);
        Integer billPayStoreCounts = Optional.ofNullable(orderPaymentMap.get(PayTypeEnum.BILL.getCode())).orElse(0);
        Integer balancePayStoreCounts = Optional.ofNullable(orderPaymentMap.get(PayTypeEnum.BALANCE.getCode())).orElse(0);
        billSummaryExcelDTO.setBillPayStoreCount(billPayStoreCounts);
        billSummaryExcelDTO.setBalancePayStoreCount(balancePayStoreCounts);
        billSummaryExcelDTO.setWechatPayStoreCount(wechatPayStoreCounts);

        OrderSummaryDTO orderSummaryDTO = orderItemDetailSummaryService.querySummary(tenantBillQueryDTO);
        billSummaryExcelDTO.setOrderCount(orderSummaryDTO.getOrderCount());
        billSummaryExcelDTO.setOrderItemCount(orderSummaryDTO.getOrderItemCount());
        billSummaryExcelDTO.setSkuCount(orderSummaryDTO.getSkuCount());
    }

    private void generateOrderItemDetailSummarySheet(TenantBillQueryDTO tenantBillQueryDTO, ExcelWriter excelWriter, String tempExcelFilePath, SheetCounter sheetCounter) {
        // 订单明细汇总
        WriteSheet orderItemDetailSummarySheet = EasyExcel.writerSheet(sheetCounter.getSheetNo()).build();
        ExcelLargeDataSetExporter<OrderItemDetailSummary, OrderItemDetailSummaryExcelDTO> orderItemDetailSummaryHandler = new ExcelLargeDataSetExporter<OrderItemDetailSummary, OrderItemDetailSummaryExcelDTO>(excelWriter, orderItemDetailSummarySheet, tempExcelFilePath) {
            @Override
            protected List<OrderItemDetailSummaryExcelDTO> convert(OrderItemDetailSummary data) {
                OrderItemDetailSummaryExcelDTO orderItemDetailSummaryExcelDTO = convertToOrderItemDetailSummaryExcelDTO(tenantBillQueryDTO, data, Boolean.FALSE, Boolean.FALSE);
                return Arrays.asList(orderItemDetailSummaryExcelDTO);
            }
        };
        orderItemDetailSummaryService.queryByConditionWithHandler(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId(), tenantBillQueryDTO.getStartTime(), tenantBillQueryDTO.getEndTime(), tenantBillQueryDTO.getGoodsType(), orderItemDetailSummaryHandler);
        orderItemDetailSummaryHandler.finish(false);
        sheetCounter.addCount();
    }

    private OrderItemDetailSummaryExcelDTO convertToOrderItemDetailSummaryExcelDTO(TenantBillQueryDTO tenantBillQueryDTO, OrderItemDetailSummary data, Boolean specialFlag, Boolean needResetOrderPrice) {
        if (needResetOrderPrice) {
            data.setPayablePrice(BigDecimal.ZERO);
            data.setAmount(0);
//            data.setTotalPrice(BigDecimal.ZERO);
            data.setDeliveryFee(BigDecimal.ZERO);
            data.setGoodsSupplyPrice(BigDecimal.ZERO);
//            data.setGoodsSupplyTotalPrice(BigDecimal.ZERO);
            data.setGoodsDeliveryFee(BigDecimal.ZERO);
        }
        boolean otherSupplierFlag = Objects.equals(tenantBillQueryDTO.getType(), BillTypeEnum.DIRECT_ASSIGN_BILL.getCode()) && !tenantBillQueryDTO.getSupplierId().equals(SupplierTenantEnum.XM.getId());
        //查询在此期间的售后信息
        OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO = new OrderAfterSaleDetailSummaryQueryDTO();
        orderAfterSaleDetailSummaryQueryDTO.setTenantId(tenantBillQueryDTO.getTenantId());
        orderAfterSaleDetailSummaryQueryDTO.setStartTime(tenantBillQueryDTO.getStartTime());
        orderAfterSaleDetailSummaryQueryDTO.setEndTime(tenantBillQueryDTO.getEndTime());
        orderAfterSaleDetailSummaryQueryDTO.setOrderNo(data.getOrderNo());
        orderAfterSaleDetailSummaryQueryDTO.setItemId(data.getItemId());
        orderAfterSaleDetailSummaryQueryDTO.setSpecialFlag(specialFlag);
        OrderAfterSaleDetailSummary orderAfterSaleDetailSummary = orderAfterSaleDetailSummaryService.queryByConditionSum(orderAfterSaleDetailSummaryQueryDTO);
        // 代仓规则
        data.setGoodsAgentRule(handleAgentHitRule(data.getGoodsAgentRule()));
        // 商品退款金额
        data.setItemRefundPrice(orderAfterSaleDetailSummary.getItemRefundPrice());
        // 已到货或者没有退款标识则不退
        BigDecimal goodsDeliveryFeeRefund = null;
        if (Objects.equals(orderAfterSaleDetailSummary.getDeliveryRefundFeeFlag(), DeliveryRefundFeeFlagEnum.NEED_REFUND.getFlag())) {
            goodsDeliveryFeeRefund = supplierDeliveryInfoService.querySupplierDeliveryInfo(data.getOrderNo());
        } else {
            orderAfterSaleDetailSummary.setDeliveryRefundFee(null);
        }
        data.setGoodsDeliveryFeeRefund(goodsDeliveryFeeRefund);
        data.setDeliveryRefundFee(orderAfterSaleDetailSummary.getDeliveryRefundFee());
        // 货品退款金额
        BigDecimal totalPrice = data.getTotalPrice();
        BigDecimal goodsRefundPrice = totalPrice.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : NumberUtil.div(NumberUtil.mul(data.getGoodsSupplyTotalPrice(), data.getItemRefundPrice()), data.getTotalPrice(), 2);
        data.setGoodsRefundPrice(goodsRefundPrice);
        data.setGoodsSupplyTotalPrice(needResetOrderPrice ? BigDecimal.ZERO : data.getGoodsSupplyTotalPrice());
        data.setTotalPrice(needResetOrderPrice ? BigDecimal.ZERO : data.getTotalPrice());
        // 商品金额扣除售后
        BigDecimal itemTotalPriceDeductedRefund = NumberUtil.sub(data.getTotalPrice(), data.getItemRefundPrice());
        // 运费金额扣除售后
        BigDecimal deliveryFeeDeductedRefund = NumberUtil.sub(data.getDeliveryFee(), data.getDeliveryRefundFee());
        data.setTotalPriceDeductedRefund(NumberUtil.add(itemTotalPriceDeductedRefund, deliveryFeeDeductedRefund));
        // 商品售价扣除成本价
        BigDecimal salesAndSupplyDifference = NumberUtil.sub(data.getTotalPrice(), data.getGoodsSupplyTotalPrice());
        data.setSalesAndSupplyDifference(salesAndSupplyDifference);
        // 货品成本价扣除售后
        BigDecimal goodsSupplyPriceDeductedRefund = NumberUtil.sub(data.getGoodsSupplyTotalPrice(), data.getGoodsRefundPrice());
        // 商品售价扣除成本价格（剔除售后）
        BigDecimal salesAndSupplyDifferenceDeductedRefund = NumberUtil.sub(itemTotalPriceDeductedRefund, goodsSupplyPriceDeductedRefund);
        data.setSalesAndSupplyDifferenceDeductedRefund(salesAndSupplyDifferenceDeductedRefund);
        // 采购应付总计 = 成本价总价 + 供应商配送费 - 货品退款金额 - 货品配送费售后金额
        BigDecimal goodsTotalPrice = NumberUtil.add(data.getGoodsSupplyTotalPrice(), data.getGoodsDeliveryFee(), data.getGoodsRefundPrice().negate(), Objects.isNull(goodsDeliveryFeeRefund) ? BigDecimal.ZERO : goodsDeliveryFeeRefund.negate());
        data.setGoodsTotalPrice(goodsTotalPrice);
        OrderItemDetailSummaryExcelDTO orderItemDetailSummaryExcelDTO = ReportConverter.OrderStatementSummary2ExcelDTO(data);
        // 非鲜沐供应商则运费为空
        if (otherSupplierFlag) {
            orderItemDetailSummaryExcelDTO.setGoodsDeliveryFee(null);
            orderItemDetailSummaryExcelDTO.setGoodsDeliveryFeeRefund(null);
            orderItemDetailSummaryExcelDTO.setGoodsSupplierName(tenantBillQueryDTO.getSupplierName());
        }

        // 采购应付总计不含配送费
        orderItemDetailSummaryExcelDTO.setGoodsTotalPriceDeductedDeliveryFee(NumberUtil.add(data.getGoodsSupplyTotalPrice(), data.getGoodsRefundPrice().negate()));
        return orderItemDetailSummaryExcelDTO;
    }

    private BigDecimal getGoodsDeliveryFeeRefund(BigDecimal deliveryFee, BigDecimal deliveryRefundFee, BigDecimal goodsDeliveryFee) {
        if (Objects.isNull(deliveryFee) || Objects.isNull(goodsDeliveryFee)) {
            return null;
        }
        if (Objects.isNull(deliveryRefundFee)) {
            return BigDecimal.ZERO;
        }
        if (deliveryFee.compareTo(BigDecimal.ZERO) == 0 && deliveryRefundFee.compareTo(BigDecimal.ZERO) == 0) {
            return goodsDeliveryFee;
        }
        BigDecimal rate = NumberUtil.div(deliveryRefundFee, deliveryFee);
        return NumberUtil.mul(goodsDeliveryFee, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
    }

    private String getStatementFileName(TenantBillQueryDTO tenantBillQueryDTO) {
        MerchantResultResp merchant = merchantService.selectByTenantId(tenantBillQueryDTO.getTenantId());
        String merchantName = merchant.getMerchantName();
        String startTime = tenantBillQueryDTO.getStartTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        String endTime = tenantBillQueryDTO.getEndTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        String fileName = merchantName + "&" + getSupplierName(tenantBillQueryDTO.getTenantId(), tenantBillQueryDTO.getSupplierId()) + startTime + "-" + endTime + "对账单";
        return fileName;
    }

    /**
     * 处理代仓规则展示
     * @param hitRule
     * @return
     */
    private String handleAgentHitRule(String hitRule) {
        if (StringUtils.isEmpty(hitRule)) {
            return "-";
        }
        ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = JSON.parseObject(hitRule, ProductAgentSkuFeeRuleDetailDTO.class);
        if (productAgentSkuFeeRuleDetailDTO.getPercentage() != null) {
            return String.format("交易额 x %s%%", productAgentSkuFeeRuleDetailDTO.getPercentage());
        }

        if (productAgentSkuFeeRuleDetailDTO.getAmount() != null) {
            return String.format("%s元/件", productAgentSkuFeeRuleDetailDTO.getAmount());
        }
        return "-";
    }
}
