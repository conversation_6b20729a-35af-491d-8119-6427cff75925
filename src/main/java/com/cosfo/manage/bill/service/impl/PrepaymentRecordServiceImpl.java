package com.cosfo.manage.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.converter.PrepaymentRecordMapper;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordVO;
import com.cosfo.manage.bill.service.PrepaymentRecordService;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.QiNiuConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.prepay.TenantPrepayPayableTargetEnum;
import com.cosfo.manage.common.context.prepay.TenantPrepayRecordStatusEnum;
import com.cosfo.manage.common.context.prepay.TenantPrepayRecordTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.tenant.dao.TenantPrepaymentRecordDao;
import com.cosfo.manage.tenant.mapper.TenantCompanyMapper;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantCompany;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentRecord;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.model.vo.TenantCompanyDetailVO;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantCompanyAccountService;
import com.cosfo.manage.tenant.service.TenantCompanyService;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrepaymentRecordServiceImpl implements PrepaymentRecordService {
    @Resource
    private TenantPrepaymentRecordDao tenantPrepaymentRecordDao;
    @Resource
    private TenantCompanyMapper tenantCompanyMapper;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantCompanyAccountService tenantCompanyAccountService;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private TenantCompanyService tenantCompanyService;

    @Override
    public Long addPrepaymentRecord(PrepaymentRecordDTO dto) {
        return tenantPrepaymentRecordDao.addPrepaymentRecord(dto);
    }

    @Override
    public PrepaymentRecordVO getPrepaymentRecord(Long id) {
        TenantPrepaymentRecord prepaymentRecord = tenantPrepaymentRecordDao.getPrepaymentRecord(id);
        //获取供应商信息
        Long supplierTenantId = prepaymentRecord.getSupplierTenantId();
        TenantCompanyAccount tenantCompanyAccount = tenantCompanyAccountService.getAccount(supplierTenantId, prepaymentRecord.getTenantId());
        TenantAccountVO tenantAccountVO = tenantAccountService.getSaasTenantAccountVO(prepaymentRecord.getCreatorId());
        PrepaymentRecordVO prepaymentRecordVO = PrepaymentRecordMapper.INSTANCE.recordToVO(prepaymentRecord, tenantCompanyAccount);
        if (tenantAccountVO != null) {
            prepaymentRecordVO.setCreator(tenantAccountVO.getNickname());
            prepaymentRecordVO.setCreatorPhone(tenantAccountVO.getPhone());
        }
//        TenantCompany tenantCompany = tenantCompanyMapper.selectByTenantId(supplierTenantId);
        TenantCompany tenantCompany = tenantCompanyService.selectByTenantId(supplierTenantId);
        if (tenantCompany != null) {
            //manage审核人展示供应商主体
            prepaymentRecordVO.setAuditor(tenantCompany.getCompanyName());
            prepaymentRecordVO.setSupplierTenant(tenantCompany.getAccountName());
            // 预收和退款创建者展示供应商主体
            if (!Objects.equals(prepaymentRecord.getTransactionType(), TenantPrepayRecordTypeEnum.PREPAY.getRecordType())) {
                prepaymentRecordVO.setCreator(tenantCompany.getCompanyName());
                prepaymentRecordVO.setCreatorPhone("");
            }
        }
        TenantCompanyDetailVO tenantCompanyDetail = tenantService.queryTenantCompanyDetail(prepaymentRecord.getTenantId());
        // 补充付款人信息
        if (tenantCompanyDetail != null) {
            prepaymentRecordVO.setTenantName(tenantCompanyDetail.getTenantName());
            prepaymentRecordVO.setTenantCompanyName(tenantCompanyDetail.getCompanyName());
        }
        return prepaymentRecordVO;
    }

    @Override
    public Boolean downPrepaymentRecord(PrepaymentRecordQueryDTO queryDTO) {
        List<TenantPrepaymentRecord> prepaymentRecordList = tenantPrepaymentRecordDao.getPrepaymentRecordList(queryDTO);
        Set<Long> supplierIds = prepaymentRecordList.stream().map(TenantPrepaymentRecord::getSupplierTenantId).collect(Collectors.toSet());
        Map<Long, TenantCompanyAccount> supplierMap = tenantCompanyAccountService.queryAccountMap(supplierIds, queryDTO.getTenantId());
        Set<Long> creatorIds = prepaymentRecordList.stream().map(TenantPrepaymentRecord::getCreatorId).collect(Collectors.toSet());
        Map<Long, TenantAccount> accountMap = tenantAccountService.queryAccountMap(creatorIds);
        Map<Long, SupplierTenantVO> companyMap = tenantService.querySupplierMap(supplierIds);
        List<PrepaymentRecordVO> prepaymentRecordVOS = PrepaymentRecordMapper.INSTANCE.recordToVOList(prepaymentRecordList, supplierMap, accountMap, companyMap);
        exportPrepaymentRecord(queryDTO, prepaymentRecordVOS);
        return Boolean.TRUE;
    }

    private void exportPrepaymentRecord(PrepaymentRecordQueryDTO queryDTO, List<PrepaymentRecordVO> data) {
        Map<String, String> paramsMap = new HashMap<>();
        if (queryDTO.getDateStart() != null && queryDTO.getDateEnd() != null) {
            String startTime = queryDTO.getDateStart().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String endTime = queryDTO.getDateEnd().format(DateTimeFormatter.ISO_LOCAL_DATE);
            paramsMap.put(Constants.QUERY_TIME, startTime + StringConstants.SEPARATING_IN_LINE + endTime);
        }
        if (queryDTO.getSupplierTenantId() != null) {
//            TenantCompany tenantDTO = tenantCompanyMapper.selectByTenantId(queryDTO.getSupplierTenantId());
            TenantCompany tenantDTO = tenantCompanyService.selectByTenantId(queryDTO.getSupplierTenantId());
            paramsMap.put("收款人", tenantDTO.getCompanyName());
        }
        if (queryDTO.getCreatorId() != null) {
            TenantAccountVO tenantAccountVO = tenantAccountService.getSaasTenantAccountVO(queryDTO.getCreatorId());
            paramsMap.put("提交人", tenantAccountVO.getNickname());
        }
        if (queryDTO.getStatus() != null) {
            paramsMap.put("状态", TenantPrepayRecordStatusEnum.fromStatus(queryDTO.getStatus()).getDesc());
        }
        if (queryDTO.getType() != null) {
            paramsMap.put("可用范围", TenantPrepayPayableTargetEnum.valueOf(queryDTO.getType()).getDesc());
        }
        List<Map<String, String>> collect;
        if (!CollectionUtils.isEmpty(data)) {
            collect = data.stream().map(d -> {
                Map<String, String> map = new HashMap<>();
                try {
                    map = BeanUtils.describe(d);
                    // 转map导致easyexcel convert失效
                    map.put("payTime", d.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    List<String> proofList = d.getProofList();
                    if (CollectionUtils.isEmpty(proofList)) {
                        return map;
                    }
                    for (int i = 0; i < proofList.size(); i++) {
                        map.put("proof" + (i + 1), QiNiuConstants.OSS_URL + proofList.get(i));
                    }
                } catch (Exception e) {
                    throw new BizException("导出预付记录异常", e);
                }
                return map;
            }).collect(Collectors.toList());
        } else {
            collect = new ArrayList<>();
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PREPAYMENT_RECORD_EXPORT.getType());
        recordDTO.setTenantId(queryDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.PREPAYMENT_RECORD_EXPORT.getFileName());
        recordDTO.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(collect, ee -> {
            // 1、表格处理
            String filePath = commonService.exportExcel(ee, ExcelTypeEnum.PREPAYMENT_RECORD_EXPORT.getName());

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
//        fileDownloadRecord.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        fileDownloadRecord.setType(FileDownloadTypeEnum.PREPAYMENT_RECORD_EXPORT.getType());
//        fileDownloadRecord.setTenantId(queryDTO.getTenantId());
//        fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
//        // 异步导出
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            Boolean isSuccess = commonService.generateAndUploadExcel(collect, ExcelTypeEnum.PREPAYMENT_RECORD_EXPORT, fileDownloadRecord.getId());
//        });
    }

    @Override
    public PageInfo<PrepaymentRecordVO> queryPrepaymentRecordPage(PrepaymentRecordQueryDTO queryDTO) {
        Page<TenantPrepaymentRecord> page = tenantPrepaymentRecordDao.getPrepaymentRecordPage(queryDTO);
        Set<Long> supplierIds = page.getRecords().stream().map(TenantPrepaymentRecord::getSupplierTenantId).collect(Collectors.toSet());
        Map<Long, TenantCompanyAccount> supplierMap = tenantCompanyAccountService.queryAccountMap(supplierIds, queryDTO.getTenantId());
        Set<Long> creatorIds = page.getRecords().stream().map(TenantPrepaymentRecord::getCreatorId).collect(Collectors.toSet());
        Map<Long, TenantAccount> accountMap = tenantAccountService.queryAccountMap(creatorIds);
        Map<Long, SupplierTenantVO> companyMap = tenantService.querySupplierMap(supplierIds);
        return PrepaymentRecordMapper.INSTANCE.pageToPageInfo(page, supplierMap, accountMap, companyMap);
    }

    @Override
    public PrepaymentRecordTotalVO queryPrepaymentTotal(PrepaymentRecordQueryDTO queryDTO) {
        return tenantPrepaymentRecordDao.queryPrepaymentTotal(queryDTO);
    }
}

