package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.OrderProfitSharingDifferenceExcelDTO;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-08-16
 **/
public interface OrderProfitSharingDifferenceService {
    /**
     * 查询
     *
     * @param tenantId                            租户id
     * @param startTime                           开始时间
     * @param endTime                             结束时间
     * @param orderProfitSharingDifferenceHandler
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ExcelLargeDataSetExporter<OrderProfitSharingDifference, OrderProfitSharingDifferenceExcelDTO> orderProfitSharingDifferenceHandler);

    /**
     * 查询个数
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    long count(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime);
}
