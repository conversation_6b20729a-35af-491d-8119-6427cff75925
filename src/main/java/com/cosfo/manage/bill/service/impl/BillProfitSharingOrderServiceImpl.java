package com.cosfo.manage.bill.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.bill.mapper.*;
import com.cosfo.manage.bill.model.bo.OrderProfitSharingAccountDetailBO;
import com.cosfo.manage.bill.model.dto.*;
import com.cosfo.manage.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.service.BillProfitSharingOrderService;
import com.cosfo.manage.bill.service.BillProfitSharingRefundSnapshotService;
import com.cosfo.manage.bill.service.PaymentService;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.facade.ordercenter.OrderItemSnapshotQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.huifu.dto.HuiFuSupplierAccountDTO;
import com.cosfo.manage.huifu.service.HuiFuAccountService;
import com.cosfo.manage.order.service.OrderAgentSkuFeeRuleSnapshotService;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/27
 */
@Service
@Slf4j
public class BillProfitSharingOrderServiceImpl implements BillProfitSharingOrderService {
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private OrderAgentSkuFeeRuleSnapshotService orderAgentSkuFeeRuleSnapshotService;
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Resource
    private TransLogResultListMapper transLogResultListMapper;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private BillProfitSharingRefundSnapshotService billProfitSharingRefundSnapshotService;
    @Resource
    private HuiFuAccountService huiFuAccountService;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentService paymentService;

    @Override
    public CommonResult<PageInfo<BillProfitSharingDTO>> listAllProfits(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        List<BillProfitSharingDTO> billProfitSharingDTOList = getBillProfitSharingDTOList(contextInfoDTO, billProfitSharingQueryDTO);
        if (ObjectUtil.isNotNull(billProfitSharingQueryDTO.getPageIndex()) && ObjectUtil.isNotNull(billProfitSharingQueryDTO.getPageSize())){
            return CommonResult.ok(PageInfoHelper.createPageInfo(billProfitSharingDTOList,  billProfitSharingQueryDTO.getPageSize()));
        }
        return CommonResult.ok(PageInfoHelper.createPageInfo(billProfitSharingDTOList, NumberConstants.ONE));
    }

    private Map<Long, OrderResp> getOrderDTOByIds(Set<Long> allOrderIds){
        if(CollectionUtils.isEmpty(allOrderIds)){
            return Collections.EMPTY_MAP;
        }
        List<OrderResp> orderDTOList = new LinkedList<>();
        Iterators.partition(allOrderIds.iterator(), 100).forEachRemaining(sutSet -> {
            List<OrderResp> orderResps = orderQueryFacade.queryByIds(sutSet);
            orderDTOList.addAll(orderResps);
        });
        return orderDTOList.stream().collect(Collectors.toMap(OrderResp::getId, order -> order));
    }

    /**
     * Pair<Long, Long>: orderId, receiverTenantId(bill_profit_sharing_snapshot.account_id)
     * @param orderIds
     * @param receiverTenantId
     * @return
     */
    private Map<Pair<Long, Long>, List<BillProfitSharingSnapshot>> getBillProfitSharingSnapshotByOrdersAndItsReceiver(Set<Long> orderIds, Long receiverTenantId) {
        List<BillProfitSharingSnapshot> sharingSnapshots = new LinkedList<>();
        Iterators.partition(orderIds.iterator(), 500).forEachRemaining(subSet -> {
            sharingSnapshots.addAll(billProfitSharingSnapshotMapper.selectByOrderIdListAndReceiverTenantId(subSet, receiverTenantId));
        });
        if(CollectionUtils.isEmpty(sharingSnapshots)){
            return Collections.EMPTY_MAP;
        }
        return sharingSnapshots.stream().collect(Collectors.groupingBy(snapshot -> Pair.of(snapshot.getOrderId(), snapshot.getAccountId())));
    }

    private Map<Pair<String, Long>, BigDecimal> getServiceCharge(List<BillProfitSharingDTO> billProfitSharingDTOList) {
        if (CollectionUtils.isEmpty(billProfitSharingDTOList)) {
            return Collections.emptyMap();
        }
        Long tenantId = billProfitSharingDTOList.get(0).getTenantId();
        Set<Long> orderIds = billProfitSharingDTOList.stream().map(BillProfitSharingDTO::getOrderId).collect(Collectors.toSet());
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryServiceChargeByTenantIdAndOrderIds(tenantId, orderIds);
        if (CollectionUtils.isEmpty(billProfitSharingSnapshots)) {
            return Collections.emptyMap();
        }
        return billProfitSharingSnapshots.stream()
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .collect(Collectors.toMap(
                        snapshot -> Pair.of(Optional.ofNullable(snapshot.getProfitSharingNo()).orElse(String.valueOf(snapshot.getOrderId())), snapshot.getAccountId()),
                        BillProfitSharingSnapshot::getProfitSharingPrice
                ));
    }

    /**
     * 导出时复用逻辑；
     */
    @Override
    public void fulfillBillProfitSharingInfo(Collection<BillProfitSharingDTO> billProfitSharingDTOList) {
        if (CollectionUtils.isEmpty(billProfitSharingDTOList)) {
            return;
        }
        BillProfitSharingDTO firstBillProfitSharing = billProfitSharingDTOList.stream().findFirst().get();
        Long tenantId = firstBillProfitSharing.getTenantId();

        List<BillProfitSharingDTO> orderBillProfitSharingDTOS = billProfitSharingDTOList.stream().filter(e -> ProfitSharingBusinessTypeEnums.Order.getType().equals(e.getBusinessType())).collect(Collectors.toList());
        Map<Pair<String, Long>, BigDecimal> orderProfitSharingMap = getServiceCharge(orderBillProfitSharingDTOS);

        Set<Long> allOrderIds = billProfitSharingDTOList.stream().map(BillProfitSharingDTO::getOrderId).collect(Collectors.toSet());
        Map<Long, OrderResp> orderIdToDTOMap = getOrderDTOByIds(allOrderIds);

        // 查询支付单信息获取支付单号
        List<Long> orderIds = Lists.newArrayList(allOrderIds);
        List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds(orderIds, tenantId);
        Map<Long, String> orderPaymentNoMap = paymentItemDTOS.stream().collect(Collectors.toMap(PaymentItemDTO::getOrderId, PaymentItemDTO::getPaymentNo, (v1, v2) -> v1));

        // 逆向分账流水手续费
        List<BillProfitSharingDTO> orderAfterSaleBillProfitSharingDTOS = billProfitSharingDTOList.stream().filter(e -> ProfitSharingBusinessTypeEnums.ORDER_AFTER_SALE.getType().equals(e.getBusinessType())).collect(Collectors.toList());
        Map<Long, List<BillProfitSharingDTO>> receiverTenantIdAndItsOrderAfterSales = orderAfterSaleBillProfitSharingDTOS.stream().collect(Collectors.groupingBy(BillProfitSharingDTO::getReceiverTenantId));
        Map<Pair<Long, Long>, BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshotMap = Maps.newHashMap();
        receiverTenantIdAndItsOrderAfterSales.entrySet().forEach(longListEntry -> {
            billProfitSharingRefundSnapshotMap.putAll(billProfitSharingRefundSnapshotService.selectServiceFeeByOrderIdAndReceiverTenantId(
                    new HashSet<>(longListEntry.getValue().stream().map(BillProfitSharingDTO::getAfterSaleId).collect(Collectors.toSet())), longListEntry.getKey()));
        });

        List<Pair<Integer, Long>> receiverTenantIdList = billProfitSharingDTOList.stream().map(el -> Pair.of(Objects.isNull(el.getAccountType()) ? AccountTypeEnum.getByTenantId(el.getReceiverTenantId()) : el.getAccountType(), el.getReceiverTenantId())).distinct().collect(Collectors.toList());
        Map<Pair<Integer, Long>, String> tenantNameMap = getTenantNameMap(tenantId, receiverTenantIdList);
        for (BillProfitSharingDTO billProfitSharingDTO : billProfitSharingDTOList) {
            // 分账手续费
            if (ProfitSharingBusinessTypeEnums.Order.getType().equals(billProfitSharingDTO.getBusinessType())) {
                BigDecimal serviceCharge = orderProfitSharingMap.get(
                        Pair.of(Optional.ofNullable(billProfitSharingDTO.getOutTradeNo()).orElse(String.valueOf(billProfitSharingDTO.getOrderId())), billProfitSharingDTO.getReceiverTenantId()));
                billProfitSharingDTO.setSplitFeeAmt(serviceCharge);
            }

            if(ProfitSharingBusinessTypeEnums.ORDER_AFTER_SALE.getType().equals(billProfitSharingDTO.getBusinessType())){
                BillProfitSharingRefundSnapshot billProfitSharingRefundSnapshot = billProfitSharingRefundSnapshotMap.get(
                        Pair.of(billProfitSharingDTO.getAfterSaleId(), billProfitSharingDTO.getReceiverTenantId()));
                if (ObjectUtil.isNotNull(billProfitSharingRefundSnapshot)) {
                    // 前端手续费显示为正数
                    billProfitSharingDTO.setSplitFeeAmt(billProfitSharingRefundSnapshot.getActualRefundPrice().abs());
                }
            }

            if(Objects.isNull(billProfitSharingDTO.getSplitFeeAmt())){
                billProfitSharingDTO.setSplitFeeAmt(BigDecimal.ZERO);
            }

            Long orderId = billProfitSharingDTO.getOrderId();
            // 如果分帐单对应订单不为空
            OrderResp orderDTO = orderIdToDTOMap.get(orderId);
            if (ObjectUtil.isNotNull(orderDTO)) {
                billProfitSharingDTO.setRecordNo(orderDTO.getOrderNo());
                billProfitSharingDTO.setPaymentNo(orderPaymentNoMap.get(orderId));
            }

            Long tenantReceiverId = billProfitSharingDTO.getReceiverTenantId();
            Pair<Integer, Long> key = Pair.of(Objects.isNull(billProfitSharingDTO.getAccountType()) ? AccountTypeEnum.getByTenantId(tenantReceiverId) : billProfitSharingDTO.getAccountType(), tenantReceiverId);
            String tenantName = tenantNameMap.getOrDefault(key, "");
            billProfitSharingDTO.setTenantName(tenantName);
        }

    }


    private Map<Pair<Integer, Long>, String> getTenantNameMap(Long tenantId, List<Pair<Integer, Long>> receiverTenantIdList) {
        if (CollectionUtils.isEmpty(receiverTenantIdList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Pair<Integer, Long>, String> tenantNameMap = Maps.newHashMap();

        // 供应商类型且非鲜沐
        List<Long> otherSupplierIds = receiverTenantIdList.stream()
                .filter(pair -> Objects.equals(pair.getLeft(), AccountTypeEnum.SUPPLIER.getType()) && !Objects.equals(pair.getRight(), BillProfitSharingAccountIdEnum.SUPPLIER.getId()))
                .map(Pair::getRight)
                .collect(Collectors.toList());
        List<HuiFuSupplierAccountDTO> huiFuSupplierAccountDTOS = huiFuAccountService.queryBySupplierIds(tenantId, otherSupplierIds);
        tenantNameMap.putAll(huiFuSupplierAccountDTOS.stream().collect(Collectors.toMap(huiFuSupplierAccountDTO -> Pair.of(AccountTypeEnum.SUPPLIER.getType(), huiFuSupplierAccountDTO.getSupplierId()), HuiFuSupplierAccountDTO::getRegName)));

        // 非供应商类型或者鲜沐供应商
        List<Long> remainingTenantIds = receiverTenantIdList.stream()
                .filter(pair -> !Objects.equals(pair.getLeft(), AccountTypeEnum.SUPPLIER.getType()) || Objects.equals(pair.getRight(), BillProfitSharingAccountIdEnum.SUPPLIER.getId()))
                .map(Pair::getRight)
                .collect(Collectors.toList());
        List<TenantResultResp> tenantsByIds = userCenterTenantFacade.getTenantsByIds(remainingTenantIds);
        tenantNameMap.putAll(tenantsByIds.stream().collect(Collectors.toMap(tenantResultResp -> Pair.of(AccountTypeEnum.getByTenantId(tenantResultResp.getId()), tenantResultResp.getId()), TenantResultResp::getTenantName)));
        return tenantNameMap;
    }


    private List<BillProfitSharingDTO> getBillProfitSharingDTOList(LoginContextInfoDTO contextInfoDTO, BillProfitSharingQueryDTO billProfitSharingQueryDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        billProfitSharingQueryDTO.setTenantId(tenantId);
        if (StringUtils.isNotBlank(billProfitSharingQueryDTO.getRecordNo())) {
            // 根据orderId过滤；
            OrderResp orderResp = orderQueryFacade.queryByNo(billProfitSharingQueryDTO.getRecordNo());
            if (null == orderResp) {
                log.error("未找到订单：{}", billProfitSharingQueryDTO);
                return Lists.newLinkedList();
            }
            billProfitSharingQueryDTO.setOrderId(orderResp.getId());
        }

        if (StringUtils.isNotBlank(billProfitSharingQueryDTO.getPaymentNo())) {
            List<Long> orderIds = paymentService.getOrderIdsByPaymentNo(billProfitSharingQueryDTO.getPaymentNo());
            if (CollectionUtils.isEmpty(orderIds)) {
                return Collections.emptyList();
            } else {
                billProfitSharingQueryDTO.setOrderIds(orderIds);
            }
        }

        if(StringUtils.isNotBlank(billProfitSharingQueryDTO.getTenantName())){
            List<Long> receiverIds = getReceiverIds(contextInfoDTO.getTenantId(), billProfitSharingQueryDTO.getTenantName());
            billProfitSharingQueryDTO.setReceiverTenantIds(receiverIds);
        }

        if (billProfitSharingQueryDTO.getPageSize() != null && billProfitSharingQueryDTO.getPageSize() > 0) {
            PageHelper.startPage(billProfitSharingQueryDTO.getPageIndex(), billProfitSharingQueryDTO.getPageSize());
        }
        billProfitSharingQueryDTO.setStatus(ProfitSharingResultEnum.FINISHED.getStatus());
        List<BillProfitSharingDTO> billProfitSharingDTOList = billProfitSharingMapper.listAll(billProfitSharingQueryDTO);
        if (CollectionUtils.isEmpty(billProfitSharingDTOList)) {
            return billProfitSharingDTOList;
        }

        fulfillBillProfitSharingInfo(billProfitSharingDTOList);
        return billProfitSharingDTOList;
    }

    private List<Long> getReceiverIds(Long tenantId, String tenantName) {
        TenantQueryReq req = new TenantQueryReq();
        req.setTenantName(tenantName);
        List<TenantResultResp> listOfTenant = userCenterTenantFacade.getTenantsByQuery(req);
        List<Long> tenantAccountIds = listOfTenant.stream().map(TenantResultResp::getId).collect(Collectors.toList());
        List<Long> huiFuAccountIds = huiFuAccountService.queryByRegName(tenantId, tenantName);
        tenantAccountIds.addAll(huiFuAccountIds);
        return tenantAccountIds;
    }

    @Override
    public CommonResult<ReceiveAndOutMoneyDTO> listAllProfitsCountMoney(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        List<BillProfitSharingDTO> billProfitSharingDTOList = getBillProfitSharingDTOList(contextInfoDTO, billProfitSharingQueryDTO);
        BigDecimal countMoney = Constants.ZERO;
        BigDecimal countFeeMoney = Constants.ZERO;
        // 逆向分账流水手续费
        List<BillProfitSharingDTO> orderAfterSaleBillProfitSharingDTOS = billProfitSharingDTOList.stream().filter(e -> ProfitSharingBusinessTypeEnums.ORDER_AFTER_SALE.getType().equals(e.getBusinessType())).collect(Collectors.toList());
        Map<Long, List<BillProfitSharingDTO>> receiverTenantIdAndItsOrderAfterSales = orderAfterSaleBillProfitSharingDTOS.stream().collect(Collectors.groupingBy(BillProfitSharingDTO::getReceiverTenantId));
        Map<Pair<Long, Long>, BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshotMap = Maps.newHashMap();
        receiverTenantIdAndItsOrderAfterSales.entrySet().forEach(longListEntry -> {
            billProfitSharingRefundSnapshotMap.putAll(billProfitSharingRefundSnapshotService.selectServiceFeeByOrderIdAndReceiverTenantId(
                    new HashSet<>(longListEntry.getValue().stream().map(BillProfitSharingDTO::getAfterSaleId).collect(Collectors.toSet())), longListEntry.getKey()));
        });

        for (BillProfitSharingDTO sharingDTO : billProfitSharingDTOList) {
            if(ProfitSharingResultEnum.FINISHED.getStatus().equals(sharingDTO.getStatus())) {
                countMoney = countMoney.add(sharingDTO.getPrice());
                if(ProfitSharingBusinessTypeEnums.Order.getType().equals(sharingDTO.getBusinessType())) {
                    BigDecimal serviceCharge = sharingDTO.getSplitFeeAmt();
                    countFeeMoney = countFeeMoney.add(serviceCharge.abs());
                }

                if(ProfitSharingBusinessTypeEnums.ORDER_AFTER_SALE.getType().equals(sharingDTO.getBusinessType())){
                    BillProfitSharingRefundSnapshot billProfitSharingRefundSnapshot = billProfitSharingRefundSnapshotMap.get(
                            Pair.of(sharingDTO.getAfterSaleId(), sharingDTO.getReceiverTenantId()));
                    if (ObjectUtil.isNotNull(billProfitSharingRefundSnapshot)) {
                        // 逆向手续费减去
                        countFeeMoney = countFeeMoney.add(billProfitSharingRefundSnapshot.getActualRefundPrice().negate());
                    }
                }
            }

        }
        ReceiveAndOutMoneyDTO receiveAndOutMoneyDTO = new ReceiveAndOutMoneyDTO();
        receiveAndOutMoneyDTO.setAllMoney(countMoney);
        receiveAndOutMoneyDTO.setCountFeeMoney(countFeeMoney);
        return CommonResult.ok(receiveAndOutMoneyDTO);
    }

    @Override
    public CommonResult<PageInfo<TransLogResultListDTO>> listAllTrans(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        transLogResultListQueryDTO.setTenantId(tenantId);
        PageHelper.startPage(transLogResultListQueryDTO.getPageIndex(), transLogResultListQueryDTO.getPageSize());
        if (ObjectUtil.isNotNull(transLogResultListQueryDTO.getStartTime())&&ObjectUtil.isNotNull(transLogResultListQueryDTO.getEndTime())){
            transLogResultListQueryDTO.setStartTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getStartTime()),TimeUtils.FORMAT_STRING));
            transLogResultListQueryDTO.setEndTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getEndTime()),TimeUtils.FORMAT_STRING));
        }
        List<TransLogResultListDTO> transLogResultListDTOList = transLogResultListMapper.listAll(transLogResultListQueryDTO);
        return CommonResult.ok(PageInfoHelper.createPageInfo(transLogResultListDTOList,  transLogResultListQueryDTO.getPageSize()));
    }

    @Override
    public CommonResult<ReceiveAndOutMoneyDTO> listAllTransCountMoney(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        transLogResultListQueryDTO.setTenantId(tenantId);
        if (ObjectUtil.isNotNull(transLogResultListQueryDTO.getPageIndex())&&ObjectUtil.isNotNull(transLogResultListQueryDTO.getPageSize())){
            PageHelper.startPage(transLogResultListQueryDTO.getPageIndex(), transLogResultListQueryDTO.getPageSize());
        }
        if (ObjectUtil.isNotNull(transLogResultListQueryDTO.getStartTime())&&ObjectUtil.isNotNull(transLogResultListQueryDTO.getEndTime())){
            transLogResultListQueryDTO.setStartTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getStartTime()),TimeUtils.FORMAT_STRING));
            transLogResultListQueryDTO.setEndTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getEndTime()),TimeUtils.FORMAT_STRING));
        }
        List<TransLogResultListDTO> transLogResultListDTOList = transLogResultListMapper.listAll(transLogResultListQueryDTO);
        /**
         * 结算金额合计
         */
        BigDecimal countMoney = Constants.ZERO;
        /**
         * 结算手续费合计
         */
        BigDecimal countFeeAmt = Constants.ZERO;
        for (TransLogResultListDTO transLogResultListDTO : transLogResultListDTOList) {
            countMoney = countMoney.add(new BigDecimal(transLogResultListDTO.getTransMoney()));
            if (ObjectUtil.isNull(transLogResultListDTO.getTransFee())){
                continue;
            }
            countFeeAmt = countFeeAmt.add(new BigDecimal(transLogResultListDTO.getTransFee()));
        }
        ReceiveAndOutMoneyDTO receiveAndOutMoneyDTO = new ReceiveAndOutMoneyDTO();
        receiveAndOutMoneyDTO.setAllMoney(countMoney);
        if (ObjectUtil.isNotNull(countFeeAmt)){
            receiveAndOutMoneyDTO.setCountFeeMoney(countFeeAmt);
        }else {
            receiveAndOutMoneyDTO.setCountFeeMoney(Constants.ZERO);
        }
        return CommonResult.ok(receiveAndOutMoneyDTO);
    }


    @Override
    public List<OrderProfitSharingAccountDetailBO> queryOrderProfitSharingAccountDetail(Long tenantId, Long orderId) {
        
        List<OrderProfitSharingAccountDetailBO> resultList = new ArrayList<>();

        // 1. 查询订单的成功支付单
        Payment payment = paymentMapper.querySuccessByOrderId(tenantId, orderId);
        if (payment == null) {
            log.warn("订单{}未找到成功的支付单", orderId);
            return resultList;
        }

        // 2. 判断支付渠道
        Integer payChannel = payment.getOnlinePayChannel();
        boolean isHuiFuChannel = Objects.equals(payChannel, OnlinePayChannelEnum.HUIFU_PAY.getChannel());

        if (isHuiFuChannel) {
            // 汇付渠道：检查分账状态
            List<BillProfitSharingDTO> profitSharings = billProfitSharingMapper.selectByOrderId(tenantId, orderId);

            // 检查是否有未完成的分账
            boolean hasUnfinishedProfitSharing = profitSharings.stream()
                    .anyMatch(ps -> !Objects.equals(ps.getStatus(), BillProfitEnum.SUCCESS.getCode()));

            if (hasUnfinishedProfitSharing) {
                throw new RuntimeException("订单" + orderId + "的分账尚未完成，无法查询分账明细");
            }

            // 分账已完成，查询分账快照统计
            List<BillProfitSharingSnapshot> snapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
            if (!CollectionUtils.isEmpty(snapshots)) {
                Map<String, OrderProfitSharingAccountDetailBO> snapshotResultMap = aggregateSnapshotsByAccount(snapshots);
                resultList.addAll(snapshotResultMap.values());
            }

        } else {
            // 非汇付渠道：返回交易类型和总金额
            OrderProfitSharingAccountDetailBO detail = new OrderProfitSharingAccountDetailBO();
            detail.setOrderId(orderId);
            detail.setTradeType(payment.getTradeType());
            detail.setTotalAmount(payment.getTotalPrice());
            detail.setProductAmount(BigDecimal.ZERO);
            detail.setDeliveryAmount(BigDecimal.ZERO);
            detail.setServiceFeeAmount(BigDecimal.ZERO);
            resultList.add(detail);
        }
        // 填充分账方名称（仅对汇付渠道的数据）
        List<OrderProfitSharingAccountDetailBO> huiFuDetails = resultList.stream()
                .filter(detail -> detail.getAccountId() != null)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(huiFuDetails)) {
            fillAccountNames(tenantId, huiFuDetails);
        }

        return resultList;
    }
    

    /**
     * 获取账户类型
     */
    private Integer getAccountType(Long accountId) {
        // 根据业务逻辑判断账户类型
        // 这里简化处理，实际应该根据具体的业务规则来判断
        return AccountTypeEnum.getByTenantId(accountId);
    }

    /**
     * 填充分账方名称
     */
    private void fillAccountNames(Long tenantId, List<OrderProfitSharingAccountDetailBO> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        // 按账户类型分组
        List<Pair<Integer, Long>> accountPairs = detailList.stream()
                .map(detail -> Pair.of(detail.getAccountType(), detail.getAccountId()))
                .distinct()
                .collect(Collectors.toList());

        // 获取账户名称映射
        Map<Pair<Integer, Long>, String> nameMap = getTenantNameMap(tenantId, accountPairs);

        // 设置账户名称
        for (OrderProfitSharingAccountDetailBO detail : detailList) {
            Pair<Integer, Long> key = Pair.of(detail.getAccountType(), detail.getAccountId());
            String accountName = nameMap.getOrDefault(key, "未知账户");
            detail.setAccountName(accountName);
        }
    }

    /**
     * 聚合分账快照数据按账户维度
     */
    private Map<String, OrderProfitSharingAccountDetailBO> aggregateSnapshotsByAccount(List<BillProfitSharingSnapshot> snapshots) {
        Map<String, OrderProfitSharingAccountDetailBO> resultMap = new LinkedHashMap<>();

        for (BillProfitSharingSnapshot snapshot : snapshots) {
            String key = snapshot.getOrderId() + "_" + getAccountType(snapshot.getAccountId()) + "_" + snapshot.getAccountId();

            OrderProfitSharingAccountDetailBO detail = resultMap.computeIfAbsent(key, k -> {
                OrderProfitSharingAccountDetailBO dto = new OrderProfitSharingAccountDetailBO();
                dto.setOrderId(snapshot.getOrderId());
                dto.setAccountId(snapshot.getAccountId());
                dto.setAccountType(getAccountType(snapshot.getAccountId()));
                dto.setProductAmount(BigDecimal.ZERO);
                dto.setDeliveryAmount(BigDecimal.ZERO);
                dto.setServiceFeeAmount(BigDecimal.ZERO);
                dto.setTotalAmount(BigDecimal.ZERO);
                return dto;
            });

            // 根据分账类型累加金额
            BigDecimal amount = snapshot.getProfitSharingPrice() != null ? snapshot.getProfitSharingPrice() : BigDecimal.ZERO;

            if (snapshot.getProfitSharingType() != null) {
                switch (snapshot.getProfitSharingType()) {
                    case 1:
                    case 2:
                        detail.setProductAmount(detail.getProductAmount().add(amount));
                        break;
                    case 3:
                        detail.setDeliveryAmount(detail.getDeliveryAmount().add(amount));
                        break;
                    case 4:
                        amount = amount.negate();
                        detail.setServiceFeeAmount(detail.getServiceFeeAmount().add(amount));
                        break;
                    default:
                        break;
                }
            }

            // 累加总金额
            detail.setTotalAmount(detail.getTotalAmount().add(amount));
        }

        return resultMap;
    }
}
