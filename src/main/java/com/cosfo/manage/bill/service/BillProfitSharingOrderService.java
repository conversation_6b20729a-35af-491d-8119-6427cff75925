package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.bo.OrderProfitSharingAccountDetailBO;
import com.cosfo.manage.bill.model.dto.*;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.Collection;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/26
 */
public interface BillProfitSharingOrderService {

    /**
     * 查询分账明细记录
     * @param billProfitSharingQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<PageInfo<BillProfitSharingDTO>> listAllProfits(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO);

    void fulfillBillProfitSharingInfo(Collection<BillProfitSharingDTO> billProfitSharingDTOList);

    /**
     * 查询分账明细记录金额合计
     * @param billProfitSharingQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<ReceiveAndOutMoneyDTO> listAllProfitsCountMoney(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询结账明细记录
     * @param transLogResultListQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<PageInfo<TransLogResultListDTO>> listAllTrans(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询结账明细金额合计
     * @param transLogResultListQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<ReceiveAndOutMoneyDTO> listAllTransCountMoney(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO);


    /**
     * 查询订单分账账户维度明细
     * 根据tenantId和orderIds查询分账明细快照表，按account_type与account_id聚合，统计商品、运费、手续费分账金额
     * @param tenantId 租户id
     * @param orderId 订单id
     * @return 分账账户维度明细列表
     */
    List<OrderProfitSharingAccountDetailBO> queryOrderProfitSharingAccountDetail(Long tenantId, Long orderId);
}
