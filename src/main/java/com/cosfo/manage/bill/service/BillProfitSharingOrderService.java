package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.*;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.Collection;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/26
 */
public interface BillProfitSharingOrderService {

    /**
     * 查询倒挂分账订单
     *
     * @param tenantBillQueryDTO
     * @return
     */
//    List<BillOrderVO> queryInvertedProfitSharingOrder(TenantBillQueryDTO tenantBillQueryDTO, List<BillOrderVO> billOrderVOS);

    /**
     * 查询分账明细记录
     * @param billProfitSharingQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<PageInfo<BillProfitSharingDTO>> listAllProfits(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO);

    void fulfillBillProfitSharingInfo(Collection<BillProfitSharingDTO> billProfitSharingDTOList);

    /**
     * 查询分账明细记录金额合计
     * @param billProfitSharingQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<ReceiveAndOutMoneyDTO> listAllProfitsCountMoney(BillProfitSharingQueryDTO billProfitSharingQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询结账明细记录
     * @param transLogResultListQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<PageInfo<TransLogResultListDTO>> listAllTrans(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询结账明细金额合计
     * @param transLogResultListQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<ReceiveAndOutMoneyDTO> listAllTransCountMoney(TransLogResultListQueryDTO transLogResultListQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 获取分账订单信息
     *
     * @param tenantId
     * @param orderId
     * @param orderItemId
     * @return
     */
//    BillProfitSharingOrder getBillProfitSharingOrder(Long tenantId, Long orderId, Long orderItemId);
}
