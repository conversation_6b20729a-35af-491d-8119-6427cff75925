package com.cosfo.manage.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.converter.PrepaymentTransactionMapper;
import com.cosfo.manage.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionVO;
import com.cosfo.manage.bill.service.PrepaymentTransactionService;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.prepay.TenantPrepayTransactionEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.tenant.dao.TenantPrepaymentTransactionDao;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrepaymentTransactionServiceImpl implements PrepaymentTransactionService {

    @Resource
    private TenantPrepaymentTransactionDao prepaymentTransactionDao;
    @Resource
    private TenantService tenantService;

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;

    @Override
    public PageInfo<PrepaymentTransactionVO> queryPrepaymentTransactionPage(PrepaymentTransactionQueryDTO queryDTO) {
        Page<TenantPrepaymentTransaction> page = prepaymentTransactionDao.queryPrepaymentTransactionPage(queryDTO);
        Map<Long, SupplierTenantVO> supplierMap = getSupplierMap(page.getRecords());
        return PrepaymentTransactionMapper.INSTANCE.transactionToVOPageInfo(page, supplierMap);
    }

    @Override
    public Boolean downloadPrepaymentTransaction(PrepaymentTransactionQueryDTO queryDTO) {
        List<TenantPrepaymentTransaction> tenantPrepaymentTransactions = prepaymentTransactionDao.queryPrepaymentTransactionList(queryDTO);
        Map<Long, SupplierTenantVO> supplierMap = getSupplierMap(tenantPrepaymentTransactions);
        exportPrepaymentTransaction(queryDTO, PrepaymentTransactionMapper.INSTANCE.transactionToVoList(tenantPrepaymentTransactions, supplierMap));
        return Boolean.TRUE;
    }

    private Map<Long, SupplierTenantVO> getSupplierMap(List<TenantPrepaymentTransaction> tenantPrepaymentTransactions) {
        Set<Long> supplierSet = tenantPrepaymentTransactions.stream().map(TenantPrepaymentTransaction::getSupplierTenantId).collect(Collectors.toSet());
        Map<Long, SupplierTenantVO> supplierMap = tenantService.querySupplierMap(supplierSet);
        return supplierMap;
    }

    @Override
    public PrepaymentTransactionTotalVO queryPrepaymentTransactionTotal(PrepaymentTransactionQueryDTO queryDTO) {
        return prepaymentTransactionDao.queryPrepaymentTransactionTotal(queryDTO);
    }

    private void exportPrepaymentTransaction(PrepaymentTransactionQueryDTO queryDTO, List<PrepaymentTransactionVO> data) {
        Map<String, String> paramsMap = new HashMap<>();
        if (queryDTO.getStartTime() != null && queryDTO.getEndTime() != null) {
            String startTime = queryDTO.getStartTime().toString();
            String endTime = queryDTO.getEndTime().toString();
            paramsMap.put(Constants.QUERY_TIME, startTime + StringConstants.SEPARATING_IN_LINE + endTime);
        }
        if (queryDTO.getType() != null) {
            paramsMap.put("收支类型", queryDTO.getType() == 1 ? "收入" : "支出");
        }
        if (queryDTO.getTransactionType() != null) {
            paramsMap.put("交易类型", TenantPrepayTransactionEnum.TransactionType.fromType(queryDTO.getTransactionType()).getDesc());
        }
        if (StringUtils.isNotBlank(queryDTO.getTransactionName())) {
            paramsMap.put("交易对象", queryDTO.getTransactionName());
        }
        if (queryDTO.getTransactionId() != null) {
            paramsMap.put("交易流水号", queryDTO.getTransactionId().toString());
        }
        if (StringUtils.isNotBlank(queryDTO.getOrderNo())) {
            paramsMap.put("订单编号", queryDTO.getOrderNo());
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PREPAYMENT_TRANSACTION_EXPORT.getType());
        recordDTO.setTenantId(queryDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.PREPAYMENT_TRANSACTION_EXPORT.getFileName());
        recordDTO.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(data, ee -> {
            // 1、表格处理
            String filePath = commonService.exportExcel(data, ExcelTypeEnum.PREPAYMENT_TRANSACTION_EXPORT.getName());

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });

//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
//        fileDownloadRecord.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        fileDownloadRecord.setType(FileDownloadTypeEnum.PREPAYMENT_TRANSACTION_EXPORT.getType());
//        fileDownloadRecord.setTenantId(queryDTO.getTenantId());
//        fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
//        // 异步导出
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            Boolean isSuccess = commonService.generateAndUploadExcel(data, ExcelTypeEnum.PREPAYMENT_TRANSACTION_EXPORT, fileDownloadRecord.getId());
//        });
    }

    public static void main(String[] args) {
        System.out.println(LocalDate.now().toString());
    }
}
