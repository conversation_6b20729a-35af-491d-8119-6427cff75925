package com.cosfo.manage.bill.repository;

import com.cosfo.manage.bill.model.dto.OrderProfitSharingDifferenceExcelDTO;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单分账差额 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface OrderProfitSharingDifferenceRepository extends IService<OrderProfitSharingDifference> {

    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ExcelLargeDataSetExporter<OrderProfitSharingDifference, OrderProfitSharingDifferenceExcelDTO> orderProfitSharingDifferenceHandler);
}
