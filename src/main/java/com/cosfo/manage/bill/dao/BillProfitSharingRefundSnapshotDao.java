package com.cosfo.manage.bill.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.bill.model.po.BillProfitSharingRefundSnapshot;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/9
 */
public interface BillProfitSharingRefundSnapshotDao extends IService<BillProfitSharingRefundSnapshot> {

    /**
     * 查询逆向交易退款手续费
     *
     * @param afterSaleIds
     * @param receiverTenantId
     * @return
     */
    List<BillProfitSharingRefundSnapshot> selectServiceFeeByOrderIdAndReceiverTenantId(Collection<Long> afterSaleIds, Long receiverTenantId);
}
