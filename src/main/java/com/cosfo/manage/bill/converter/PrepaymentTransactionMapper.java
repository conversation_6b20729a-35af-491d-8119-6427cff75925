package com.cosfo.manage.bill.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionVO;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.github.pagehelper.PageInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface PrepaymentTransactionMapper {
    PrepaymentTransactionMapper INSTANCE = Mappers.getMapper(PrepaymentTransactionMapper.class);

    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "list", source = "records")
    PageInfo<PrepaymentTransactionVO> transactionToVOPageInfo(Page<TenantPrepaymentTransaction> page, @Context Map<Long, SupplierTenantVO> supplierMap);

    List<PrepaymentTransactionVO> transactionToVoList(List<TenantPrepaymentTransaction> transactionList, @Context Map<Long, SupplierTenantVO> supplierMap);

    @Mapping(target = "transactionAmount", expression = "java(transaction.getType() == 1 ? transaction.getTransactionAmount().negate() : transaction.getTransactionAmount())")
    @Mapping(target = "transactionTypeDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayTransactionEnum.TransactionType.fromType(transaction.getTransactionType()).getDesc())")
    @Mapping(target = "typeDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayTransactionEnum.Type.fromType(transaction.getType()).getDesc())")
    PrepaymentTransactionVO transactionToVO(TenantPrepaymentTransaction transaction, @Context Map<Long, SupplierTenantVO> supplierMap);

    @AfterMapping
    default void voAfterMapping(@MappingTarget PrepaymentTransactionVO transaction, @Context Map<Long, SupplierTenantVO> supplierMap) {
        SupplierTenantVO supplierTenantVO = supplierMap.get(transaction.getSupplierTenantId());
        if (supplierTenantVO != null) {
            transaction.setSupplierTenant(supplierTenantVO.getCompanyName());
        }
    }
}
