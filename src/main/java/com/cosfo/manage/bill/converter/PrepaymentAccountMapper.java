package com.cosfo.manage.bill.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.model.vo.PrepaymentAccountVO;
import com.cosfo.manage.tenant.model.po.Tenant;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.github.pagehelper.PageInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface PrepaymentAccountMapper {

    PrepaymentAccountMapper INSTANCE = Mappers.getMapper(PrepaymentAccountMapper.class);

    @Mapping(target = "pageSize", source = "page.size")
    @Mapping(target = "pageNum", source = "page.current")
    @Mapping(target = "list", source = "page.records")
    PageInfo<PrepaymentAccountVO> accountPageToPageInfo(Page<TenantPrepaymentAccount> page, @Context Map<Long, SupplierTenantVO> supplierMap);

    ArrayList<PrepaymentAccountVO> accountListToVoList(List<TenantPrepaymentAccount> accountList, @Context Map<Long, SupplierTenantVO> supplierMap);

    @Mapping(target = "target", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayPayableTargetEnum.valueOf(account.getPayableTarget()).getDesc())")
    @Mapping(target = "type", source = "account.payableTarget")
    PrepaymentAccountVO accountToVO(TenantPrepaymentAccount account, @Context Map<Long, SupplierTenantVO> supplierMap);

    @AfterMapping
    default void afterMapping(@MappingTarget PrepaymentAccountVO target, @Context Map<Long, SupplierTenantVO> supplierMap) {
        SupplierTenantVO supplierTenantVO = supplierMap.get(target.getSupplierTenantId());
        if (supplierTenantVO != null) {
            target.setSupplierTenantName(supplierTenantVO.getCompanyName());
        }
    }
}
