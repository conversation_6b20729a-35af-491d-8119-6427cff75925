package com.cosfo.manage.bill.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordVO;
import com.cosfo.manage.common.context.prepay.TenantPrepayRecordTypeEnum;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentRecord;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.github.pagehelper.PageInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Mapper
public interface PrepaymentRecordMapper {

    PrepaymentRecordMapper INSTANCE = Mappers.getMapper(PrepaymentRecordMapper.class);

    @Mapping(target = "transactionType", expression = "java(0)")
    @Mapping(target = "payableTarget", source = "type")
    @Mapping(target = "transactionAmount", source = "amount")
    TenantPrepaymentRecord recordDTOToRecord(PrepaymentRecordDTO dto);

    @Mapping(target = "payTargetDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayPayableTargetEnum.valueOf(record.getPayableTarget()).getDesc())")
    @Mapping(target = "auditStatusDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayRecordStatusEnum.fromStatus(record.getAuditStatus()).getDesc())")
    @Mapping(target = "type", source = "payableTarget")
    @Mapping(target = "prepaymentAmount", source = "transactionAmount")
    PrepaymentRecordVO recordToVO(TenantPrepaymentRecord record, @Context TenantCompanyAccount tenantCompany);

    @Mapping(target = "transactionTypeDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayRecordTypeEnum.fromType(record.getTransactionType()).getDesc())")
    @Mapping(target = "proofList", expression = "java(org.apache.commons.lang3.StringUtils.isNotBlank(record.getProof())? com.google.common.collect.Lists.newArrayList(record.getProof().split(\",\")) : com.google.common.collect.Lists.newArrayList())")
    @Mapping(target = "payTargetDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayPayableTargetEnum.valueOf(record.getPayableTarget()).getDesc())")
    @Mapping(target = "auditStatusDesc", expression = "java(com.cosfo.manage.common.context.prepay.TenantPrepayRecordStatusEnum.fromStatus(record.getAuditStatus()).getDesc())")
    @Mapping(target = "prepaymentAmount", source = "transactionAmount")
    @Mapping(target = "type", source = "payableTarget")
    PrepaymentRecordVO recordToVO(TenantPrepaymentRecord record,
                                  @Context Map<Long, TenantCompanyAccount> supplierMap,
                                  @Context Map<Long, TenantAccount> accountMap,
                                  @Context Map<Long, SupplierTenantVO> companyMap);

    @AfterMapping
    default void recordToVOAfterMapping(@MappingTarget PrepaymentRecordVO recordVO, @Context TenantCompanyAccount tenantCompany) {
        if (tenantCompany != null) {
            recordVO.setAccountName(tenantCompany.getAccountName());
            recordVO.setAccountNumber(tenantCompany.getAccountNumber());
            recordVO.setOpeningBank(tenantCompany.getOpeningBank());
        }
    }

    @AfterMapping
    default void recordPageToVOAfterMapping(@MappingTarget PrepaymentRecordVO recordVO,
                                            @Context Map<Long, TenantCompanyAccount> supplierMap,
                                            @Context Map<Long, TenantAccount> accountMap,
                                            @Context Map<Long, SupplierTenantVO> companyMap) {
        TenantCompanyAccount tenantCompanyAccount = supplierMap.get(recordVO.getSupplierTenantId());
        if (tenantCompanyAccount != null) {
            recordVO.setOpeningBank(tenantCompanyAccount.getOpeningBank());
            recordVO.setAccountNumber(tenantCompanyAccount.getAccountNumber());
            recordVO.setAccountName(tenantCompanyAccount.getAccountName());
        }
        if (accountMap != null) {
            TenantAccount tenantAccount = accountMap.get(recordVO.getCreatorId());
            if (tenantAccount != null) {
                recordVO.setCreator(tenantAccount.getNickname());
                recordVO.setCreatorPhone(tenantAccount.getPhone());
            }
        }
        if (companyMap != null) {
            SupplierTenantVO supplierTenantVO = companyMap.get(recordVO.getSupplierTenantId());
            if (supplierTenantVO != null) {
                recordVO.setSupplierTenant(supplierTenantVO.getCompanyName());
                // 预收和退款创建者都应该展示供应商主体
                if (!Objects.equals(recordVO.getTransactionType(), TenantPrepayRecordTypeEnum.PREPAY.getRecordType())) {
                    recordVO.setCreator(supplierTenantVO.getCompanyName());
                    recordVO.setCreatorPhone("");
                }
            }


        }

    }

    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "list", source = "records")
    PageInfo<PrepaymentRecordVO> pageToPageInfo(Page<TenantPrepaymentRecord> page,
                                                @Context Map<Long, TenantCompanyAccount> supplierMap,
                                                @Context Map<Long, TenantAccount> accountMap,
                                                @Context Map<Long, SupplierTenantVO> companyMap);

    List<PrepaymentRecordVO> recordToVOList(List<TenantPrepaymentRecord> records,
                                            @Context Map<Long, TenantCompanyAccount> supplierMap,
                                            @Context Map<Long, TenantAccount> accountMap,
                                            @Context Map<Long, SupplierTenantVO> companyMap);
}
