package com.cosfo.manage.bill.converter;

import com.cosfo.manage.bill.model.vo.PrepaymentTrendVO;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentDailySnapshot;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PrepaymentDailySnapshotMapper {

    PrepaymentDailySnapshotMapper INSTANCE = Mappers.getMapper(PrepaymentDailySnapshotMapper.class);

    List<PrepaymentTrendVO> snapshotToVOList(List<TenantPrepaymentDailySnapshot> snapshots);

    @Mapping(target = "dayKey", source = "timeTag")
    @Mapping(target = "amount", source = "totalAmount")
    PrepaymentTrendVO snapshotToVO(TenantPrepaymentDailySnapshot snapshot);
}
