package com.cosfo.manage.pos.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

@Data
public class PosOrderQueryDTO extends BasePageInput implements Serializable {

    /**
     * 渠道 1=美团
     */
    private Integer channelType;
    /**
     * 外部系统门店名称
     */
    private String outStoreName;
    /**
     * 外部订单编码
     */
    private String orderNo;
    /**
     * 外部系统商品名称
     */
    private String outMenuName;
    /**
     * 外部系统商品编码
     */
    private String outMenuCode;
    /**
     * 开始时间 yyyy-mm-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String beginTime;
    /**
     * 结束时间 yyyy-mm-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
