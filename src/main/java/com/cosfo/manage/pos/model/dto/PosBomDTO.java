package com.cosfo.manage.pos.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PosBomDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 1=美团
     */
    private Integer channelType;

    /**
     * 租户id
     */
    private Long tenantId;

//    /**
//     * 外部系统门店code
//     */
//    private String outStoreCode;

    /**
     * 外部系统规格
     */
    private String outMenuSpecification;

    /**
     * 外部系统商品编码
     */
    private String outMenuCode;

    /**
     * 帆台门店id
     */
    private Long merchantStoreId;

    /**
     * 外部系统商品名称
     */
    private String outMenuName;

    /**
     * 售卖价
     */
    private BigDecimal price;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 毛利率
     */
    private BigDecimal grossProfitRate;

    /**
     * 生效日(废弃)
     */
    private LocalDate availableDate;

    /**
     * 帆台门店名称
     */
    private String merchantStoreName;

    /**
     * bom类型 1=品牌方
     */
    private Integer targetType;

    /**
     * 外部系统门店code/品牌方id
     */
    private String targetValue;

    /**
     * 菜单商品类型 1=成品,2=半成品,3=套餐
     */
    @NotNull(message = "菜单商品不能为空")
    private Integer bomType;
    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 成本单位
     */
    private String storeCostUnit;

    /**
     * 库存单位
     */
    private String storeInventoryUnit;

    /**
     * 单位换算比例
     */
    private BigDecimal unitMultiple;
    /**
     * 商品数量
     */
    private Integer posBomItemCount;

    /**
     * 标准单价（成本单价）
     */
    private BigDecimal costUnitPrice;
    private List<PosBomItemDTO> posBomItemList;
}
