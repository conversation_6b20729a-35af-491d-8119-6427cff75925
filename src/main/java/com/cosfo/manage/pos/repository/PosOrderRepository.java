package com.cosfo.manage.pos.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.pos.model.dto.PosOrderQueryDTO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * pos订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface PosOrderRepository extends IService<PosOrder> {
    PosOrder queryByOrderNo(String orderNo,Integer channelType , String outStoreCode, Long tenantId);

    List<PosOrder> queryByOrderNos(List<String> orderNos, Integer channelType, String outStoreCode, Long tenantId);
}
