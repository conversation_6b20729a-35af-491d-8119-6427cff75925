package com.cosfo.manage.pos.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.pos.model.po.PosBomItem;
import com.cosfo.manage.pos.mapper.PosBomItemMapper;
import com.cosfo.manage.pos.repository.PosBomItemRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * bom成本卡物料详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Service
public class PosBomItemRepositoryImpl extends ServiceImpl<PosBomItemMapper, PosBomItem> implements PosBomItemRepository {

    @Override
    public void batchInsert(List<PosBomItem> posBomItemList) {
        baseMapper.batchInsert(posBomItemList);
    }

    @Override
    public List<PosBomItem> listBomItem(Long tenantId, String menuCode, Integer targetType, String targetValue, Integer channelType, LocalDate availableDate) {
        LambdaQueryWrapper<PosBomItem>  lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PosBomItem::getOutMenuCode, menuCode);
        lambdaQueryWrapper.eq(PosBomItem::getTargetType, targetType);
        lambdaQueryWrapper.eq(PosBomItem::getTargetValue, targetValue);
        lambdaQueryWrapper.eq(PosBomItem::getTenantId, tenantId);
    //        lambdaQueryWrapper.eq(PosBomItem::getChannelType, channelType);
//        lambdaQueryWrapper.eq(PosBomItem::getAvailableDate, availableDate);
        return list(lambdaQueryWrapper);

    }

    @Override
    public List<PosBomItem> listBomItem(Long tenantId, List<String> menuCode) {
        LambdaQueryWrapper<PosBomItem>  lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PosBomItem::getOutMenuCode, menuCode);
        lambdaQueryWrapper.eq(PosBomItem::getTenantId, tenantId);
//        lambdaQueryWrapper.eq(PosBomItem::getAvailableDate, availableDate);
        return list(lambdaQueryWrapper);
    }

    @Override
    public void deleteByOutInfo(Long tenantId, String menuCode, Integer targetType, String targetValue, Integer channelType, LocalDate availableDate) {
        LambdaQueryWrapper<PosBomItem>  lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PosBomItem::getOutMenuCode, menuCode);
        lambdaQueryWrapper.eq(PosBomItem::getTargetType, targetType);
        lambdaQueryWrapper.eq(PosBomItem::getTargetValue, targetValue);
        lambdaQueryWrapper.eq(PosBomItem::getTenantId, tenantId);
//        lambdaQueryWrapper.eq(PosBomItem::getChannelType, channelType);
//        lambdaQueryWrapper.eq(PosBomItem::getAvailableDate, availableDate);
        remove(lambdaQueryWrapper);
    }

    @Override
    public void deleteByOutMenuCodes(Long tenantId, Set<String> menuCodes) {
        LambdaQueryWrapper<PosBomItem>  lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PosBomItem::getOutMenuCode, menuCodes);
        lambdaQueryWrapper.eq(PosBomItem::getTenantId, tenantId);
        remove(lambdaQueryWrapper);
    }

    @Override
    public List<PosBomItem> listBomItemByCompositionId(Long compositionId, Integer compositionType) {
        LambdaQueryWrapper<PosBomItem>  lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PosBomItem::getCompositionId, compositionId);
        lambdaQueryWrapper.eq(PosBomItem::getCompositionType, compositionType);
        return list(lambdaQueryWrapper);
    }
}
