package com.cosfo.manage.pos.repository.impl;

import com.cosfo.manage.pos.model.po.PosBomLog;
import com.cosfo.manage.pos.mapper.PosBomLogMapper;
import com.cosfo.manage.pos.repository.PosBomLogRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * bom成本卡log 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Service
public class PosBomLogRepositoryImpl extends ServiceImpl<PosBomLogMapper, PosBomLog> implements PosBomLogRepository {

    @Override
    public void batchInsert(List<PosBomLog> posBomLogList) {
        baseMapper.insertList(posBomLogList);
    }
}
