package com.cosfo.manage.pos.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.pos.model.dto.PosBomQueryDTO;
import com.cosfo.manage.pos.model.po.PosBom;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * bom成本卡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface PosBomRepository extends IService<PosBom> {

    /**
     * 分页查询
     * @param queryDTO
     * @return
     */
    Page<PosBom> listPage(PosBomQueryDTO queryDTO);
    List<PosBom> listByIds(Long tenantId, Set<Long> bomIds);


    /**
     * 删除posBom
     */
    Boolean delete(Long id);


    /**
     * 根据id查询
     * @param id
     * @return
     */
    PosBom getById(Long id);

    /**
     * 批量插入
     * @param posBomList
     */
    void batchInsert(List<PosBom> posBomList);

}
