package com.cosfo.manage.supplier.service.impl;

import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.facade.SupplierFacade;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.input.SupplierQueryInput;
import com.cosfo.manage.supplier.service.SupplierService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 供应商服务层
 * @author: George
 * @date: 2023-05-12
 **/
@Service
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private SupplierFacade supplierFacade;


    @Override
    public Integer countExpireSupplierCount(Long tenantId, Integer dayLimit) {
        return supplierFacade.countExpireSupplierCount(tenantId,dayLimit);
    }

    @Override
    public Map<Long, SupplierInfoDTO> batchQuerySupplierMap(Long tenantId, List<Long> supplierIds) {
        List<SupplierInfoDTO> supplierInfoDTOS = batchQuerySupplierList(tenantId, supplierIds);
        if (CollectionUtils.isEmpty(supplierInfoDTOS)) {
            return Collections.emptyMap();
        }
        return supplierInfoDTOS.stream().collect(Collectors.toMap(SupplierInfoDTO::getSupplierId, item -> item));
    }

    @Override
    public List<SupplierInfoDTO> batchQuerySupplierList(Long tenantId, List<Long> supplierIds) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return Collections.emptyList();
        }
        SupplierQueryInput supplierQueryInput = new SupplierQueryInput();
        supplierQueryInput.setTenantId(tenantId);
        supplierQueryInput.setSupplierIds(supplierIds);
        supplierQueryInput.setPageIndex(NumberConstants.ONE);
        supplierQueryInput.setPageSize(NumberConstants.ONE_THOUSAND);
        return supplierFacade.batchQuerySupplier(supplierQueryInput);
    }
}
