package com.cosfo.manage.supplier.service;

import com.cosfo.manage.facade.dto.SupplierInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * @description: 供应商服务层
 * @author: <PERSON>
 * @date: 2023-05-12
 **/
public interface SupplierService {

    /**
     * 查询供应商数量
     *
     * @param tenantId
     * @param dayLimit
     * @return
     */
    Integer countExpireSupplierCount(Long tenantId,Integer dayLimit);

    /**
     * 批量查询供应商 最多五百条
     * @param tenantId
     * @param supplierIds
     * @return
     */
    Map<Long, SupplierInfoDTO> batchQuerySupplierMap(Long tenantId, List<Long> supplierIds);

    /**
     * 批量查询供应商 最多五百条
     * @param tenantId
     * @param supplierIds
     * @return
     */
    List<SupplierInfoDTO> batchQuerySupplierList(Long tenantId, List<Long> supplierIds);
}
