package com.cosfo.manage.huifu.service;

import com.cosfo.manage.huifu.dto.HuiFuSupplierAccountDTO;
import com.cosfo.manage.huifu.dto.HuiFuBasicDataRespDTO;

import java.util.List;

/**
 * @description: 汇付账户服务层
 * @author: <PERSON>
 * @date: 2024-01-04
 **/
public interface HuiFuAccountService {

    /**
     * 根据供应商ids查询
     * @param supplierIds
     * @return
     */
    List<HuiFuSupplierAccountDTO> queryBySupplierIds(Long tenantId, List<Long> supplierIds);

    /**
     * 新建分账信息
     * @param huiFuSupplierAccountDTO
     */
    void saveOrUpdateSharingInfo(HuiFuSupplierAccountDTO huiFuSupplierAccountDTO);

    /**
     * 根据汇付id查询汇付账户信息
     * @param huifuId
     * @return
     */
    HuiFuBasicDataRespDTO queryHuiFuBasicData(String huifuId);

    /**
     * 根据名称查询汇付账户信息
     * @param regName
     * @return
     */
    List<Long> queryByRegName(Long tenantId, String regName);
}
