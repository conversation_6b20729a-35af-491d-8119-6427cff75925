package com.cosfo.manage.huifu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-05
 **/
@Data
public class HuiFuBaseApiReqDTO<T> {

    /**
     * 系统编号
     */
    @JSONField(name = "sys_id")
    private String sysId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 产品编号
     */
    @JSONField(name = "product_id")
    private String productId;

    /**
     * 数据
     */
    private T data;

    public HuiFuBaseApiReqDTO(String sysId, String sign, String productId, T data) {
        this.sysId = sysId;
        this.sign = sign;
        this.productId = productId;
        this.data = data;
    }


    // 默认方法用于创建 HuiFuBaseReqDTO 实例
    public static <T> HuiFuBaseApiReqDTO<T> create(String sysId, String sign, String productId, T data) {
        return new HuiFuBaseApiReqDTO<>(sysId, sign, productId, data);
    }
}
