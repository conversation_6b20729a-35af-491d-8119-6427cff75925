package com.cosfo.manage.report.model.vo;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.bind.DefaultValue;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PurchaseDetailAggVO implements Serializable {

    /**
     * 采购数量
     */
    private Integer purchaseQuantity;


    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 退货数量
     */
    private Integer backQuantity;

    /**
     * 退货金额
     */
    private BigDecimal backAmount;

    /**
     * 已入库退货数量
     */
    private Integer inboundQuantity;

    /**
     * 已入库退货金额
     */
    private BigDecimal inboundAmount;

    /**
     * 未入库退货数量
     */
    private Integer unboundQuantity;

    /**
     * 未入库退货金额
     */
    private BigDecimal unboundAmount;

    /**
     * 货损数量
     */
    private Integer damageQuantity;

    /**
     * 货损金额
     */
    private BigDecimal damageAmount;

    /**
     * 货损退货数量
     */
    private Integer damageBackQuantity;

    /**
     * 货损退货金额
     */
    private BigDecimal damageBackAmount;

    /**
     * 销售出库数量
     */
    private Integer outboundQuantity;

    /**
     * 销售出库金额
     */
    private BigDecimal outboundAmount;

    /**
     * 损售比
     */
    private BigDecimal damageSaleRatio;

    /**
     * 仓库供应商
     */
    private String warehouseServiceProvider;
}
