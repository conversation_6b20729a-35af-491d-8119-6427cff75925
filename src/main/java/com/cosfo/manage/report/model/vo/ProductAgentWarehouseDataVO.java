package com.cosfo.manage.report.model.vo;

import com.cosfo.manage.product.model.dto.WarehouseSkuFenceAreaDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/3
 */
@Data
public class ProductAgentWarehouseDataVO implements Serializable {
    /**
     * skuId
     */
    private Long skuId;

    /**
     * 鲜沐sku
     */
    private String sku;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 商品图片
     */
    private String mainPicture;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 商品规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 覆盖城市数
     */
    private List<String> cityNames;
    /**
     * 城市数
     */
    private Integer cityNum;
    /**
     * 今日销量
     */
    private Integer saleAmount;
    /**
     * 近15天销量
     */
    private Integer fifteenDaysSaleAmount;
    /**
     * 近30天销量
     */
    private Integer thirtyDaysSaleAmount;

    /**
     * 安全库存下限
     */
    private Integer stockLevelMinimum;

    /**
     * 安全库存上限
     */
    private Integer stockLevelMaximum;

    /**
     * 仓库库存
     */
    private Integer quantity;
    /**
     * 货值
     */
    private BigDecimal goodsValue;
    /**
     * 锁定库存
     */
    private Integer safeQuantity;
    /**
     * 冻结库存
     */
    private Integer lockQuantity;
    /**
     * 在途库存
     */
    private Integer roadQuantity;
    /**
     * 调拨在途库存
     */
    private Integer allocationRoadQuantity;

    /**
     * 采购在途库存
     */
    private Integer purchaseRoadQuantity;

    /**
     * 可用库存
     */
    private Integer enabledQuantity;

    /**
     * 剩余保质期
     */
    private Integer leftShelfLife;

    /**
     * 最近批次
     */
    private String batch;
    /**
     * warehouseSkuFenceAreaDTOList
     */
    private List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList;
    /**
     * 仓库服务商
     */
    private String warehouseProvider;
    /**
     * 上下架 0上架1下架
     */
    private Integer onSale;

    /**
     * 上下架描述
     */
    private String onSaleDesc;

    /**
     * 同步状态描述
     */
    private String syncDesc;

    /**
     * 仓所属租户
     */
    private Long warehouseTenantId;

    /**
     * 库存同步状态，1：同步，0：不同步
     */
    private Integer sync;

    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;

    /**
     * sku是否是自营仓类型：0：否;1：是
     */
    private Integer selfWarehouseType;
}
