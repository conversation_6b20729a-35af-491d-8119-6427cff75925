package com.cosfo.manage.report.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class StockForWaringConfigDTO implements Serializable {
    private static final long serialVersionUID = -1513691778134269703L;

    /**
     * 计算天数
     */
    @NotNull(message = "天数不能为空")
    @Min(value = 1, message = "天数不能小于1")
    @Max(value = 30, message = "天数不能大于30")
    private Integer day;

    /**
     * 0,平均销量, 1累计销量
     */
    @NotNull(message = "预警类型不能为空")
//    @Pattern(message = "预警类型不正确", regexp = "^[01]$")
    private Integer waringType;

    /**
     * 是否生效
     */
    private Boolean isActive;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 生效时间
     */
    private LocalDateTime activeTime;
}
