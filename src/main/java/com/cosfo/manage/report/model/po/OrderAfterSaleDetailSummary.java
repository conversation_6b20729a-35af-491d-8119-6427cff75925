package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 售后订单汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("order_after_sale_detail_summary")
public class OrderAfterSaleDetailSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyy-MM-dd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 售后单号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 订单单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 下单时间
     */
    @TableField("order_time")
    private LocalDateTime orderTime;

    /**
     * 售后成功时间
     */
    @TableField("finished_time")
    private LocalDateTime finishedTime;

    /**
     * 商品名称
     */
    @TableField("item_title")
    private String itemTitle;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 自有编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 商品规格
     */
    @TableField("item_specification")
    private String itemSpecification;

    /**
     * 商品数量
     */
    @TableField("item_amount")
    private Integer itemAmount;

    /**
     * 售后数量
     */
    @TableField("after_sale_amount")
    private Integer afterSaleAmount;

    /**
     * 售后类型 0 已到货 1 未到货
     */
    @TableField("after_sale_type")
    private Integer afterSaleType;

    /**
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发 7、退款录入余额 8、 退货退款录入余额
     */
    @TableField("service_type")
    private Integer serviceType;

    /**
     * 售后责任方 0、供应商1、品牌方2、门店
     */
    @TableField("responsibility_type")
    private Integer responsibilityType;

    /**
     * 售后原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 售后总金额
     */
    @TableField("total_refund_price")
    private BigDecimal totalRefundPrice;

    /**
     * 商品退款金额
     */
    @TableField("item_refund_price")
    private BigDecimal itemRefundPrice;

    /**
     * 运费退款金额
     */
    @TableField("delivery_refund_fee")
    private BigDecimal deliveryRefundFee;

    /**
     * 货品名称
     */
    @TableField("goods_title")
    private String goodsTitle;

    /**
     * 货品sku
     */
    @TableField("goods_sku")
    private String goodsSku;

    /**
     * 货品规格
     */
    @TableField("goods_specification")
    private String goodsSpecification;

    /**
     * 货品供应商名称
     */
    @TableField("goods_supplier_name")
    private String goodsSupplierName;

    /**
     * 货品类型 1、鲜沐直供 2、代仓
     */
    @TableField("goods_type")
    private Integer goodsType;

    /**
     * 货品代仓费用
     */
    @TableField("goods_agent_fee")
    private BigDecimal goodsAgentFee;

    /**
     * 货品退款金额
     */
    @TableField("goods_refund_price")
    private BigDecimal goodsRefundPrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 支付方式
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 一级分组
     */
    @TableField("first_classification")
    private String firstClassification;

    /**
     * 二级分组
     */
    @TableField("second_classification")
    private String secondClassification;

    /**
     * 门店编号
     */
    @TableField("store_no")
    private String storeNo;

    /**
     * 售后时间
     */
    @TableField("after_sale_time")
    private LocalDateTime afterSaleTime;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 货品配送费退款
     */
    @TableField("goods_delivery_fee_refund")
    private BigDecimal goodsDeliveryFeeRefund;

    /**
     * 是否退运费 0 不退 1退
     */
    @TableField("delivery_refund_fee_flag")
    private Integer deliveryRefundFeeFlag;

    /**
     * 特殊时间标签（用于处理跨期）
     */
    @TableField("special_time_tag")
    private String specialTimeTag;
}
