package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 19:28
 */
@Data
public class MarketItemSalesRankingExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 一级分类
     */
    private String firstClassification;

    /**
     * 二级分类
     */
    private String secondClassification;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 总售价
     */
    private BigDecimal totalSalesAmount;

    /**
     * 平均单价
     */
    private BigDecimal averagePrice;

    /**
     * 销售数量
     */
    private Integer salesQuantity;
}
