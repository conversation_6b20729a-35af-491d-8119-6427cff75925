package com.cosfo.manage.report.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/3/3 15:54
 */
@Data
public class MerchantStorePurchaseReportDetailVO implements Serializable {
    /**
     * 商品编码
     */
    private Long productNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 0自营仓、1三方仓
     */
    private Integer warehouseType;
    /**
     * 0品牌方配送、1三方配送、-1非法数据
     */
    private Integer deliveryType;

    /**
     * 前台分组
     */
    private String marketClassification;

    /**
     * 后台类目
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品总金额
     */
    private BigDecimal amount;

    /**
     * 配送日期
     */
    private LocalDateTime deliveryTime;

    /**
     * 实际收货数量
     */
    private Integer receivedQuantity;

    /**
     * 补发数量
     */
    private Integer reissueQuantity;

    /**
     * 售后总金额
     */
    private BigDecimal afterSaleAmount;


}
