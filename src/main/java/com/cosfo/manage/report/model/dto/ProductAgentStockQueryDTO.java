package com.cosfo.manage.report.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description:
 */
@Data
public class ProductAgentStockQueryDTO {
    /**
     * 页大小
     */
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 供应城市
     */
    private List<Long> cityIds;

    /**
     * skuIds列表
     */
    private List<Long> skuIds;

    /**
     * 是否售罄 0:否;1是
     */
    private Integer saleOut;

    /**
     * 上下架状态 1上架 0下架
     */
    private Integer onSale;

    private Long tenantId;

    /**
     * 是否过滤生效时间，有值时过滤
     */
    private LocalDateTime effectTime;
}
