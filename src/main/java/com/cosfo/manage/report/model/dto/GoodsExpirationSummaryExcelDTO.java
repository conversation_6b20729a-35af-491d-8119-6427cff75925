package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 货品过期汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
public class GoodsExpirationSummaryExcelDTO implements Serializable {

    /**
     * 有效期日期
     */
    private LocalDate expirationDate;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库
     */
    private String warehouseName;

    /**
     * 批次
     */
    private String batch;


    /**
     * 过期时批次库存数量
     */
    private Integer expirationBatchStock;

    /**
     * 期末库存
     */
    private Integer endingBatchStock;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 货品名称
     */
    private String title;

    /**
     * 货品规格
     */
    private String specification;

    /**
     * 一级类目
     */
    private String firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品规则
     */
    private String itemSpecification;


    /**
     * 一级分类
     */
    private String firstClassificationName;

    /**
     * 二级分类
     */
    private String secondClassificationName;
}
