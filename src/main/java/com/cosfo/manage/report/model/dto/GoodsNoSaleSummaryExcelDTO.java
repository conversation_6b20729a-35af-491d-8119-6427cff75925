package com.cosfo.manage.report.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2023-11-27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoodsNoSaleSummaryExcelDTO {


    /**
     * 货品skuId
     */
    private Long skuId;

    /**
     * 货品名称
     */
    private String title;

    /**
     * 货品规格
     */
    private String specification;

    /**
     * 一级类目
     */
    private String firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品规则
     */
    private String itemSpecification;

    /**
     * 默认价格
     */
    private BigDecimal salePrice;

    /**
     * 一级分类
     */
    private String firstClassificationName;

    /**
     * 二级分类
     */
    private String secondClassificationName;
}
