package com.cosfo.manage.report.model.dto;

import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 18:41
 */
@Data
public class OrderItemDetailSummaryExcelDTO extends OrderItemDetailSummary {

    /**
     * 门店类型描述
     */
    private String storeTypeDesc;

    /**
     * 支付方式描述
     */
    private String payTypeDesc;

    /**
     * 定价类型描述
     */
    private String itemPricingTypeDesc;

    /**
     * 货品类型描述
     */
    private String goodsTypeDesc;

    /**
     * 采购应付总计（不含配送费）
     */
    private BigDecimal goodsTotalPriceDeductedDeliveryFee;

}
