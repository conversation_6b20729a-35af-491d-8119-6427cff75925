package com.cosfo.manage.report.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date : 2023/3/3 15:54
 */
@Data
public class MerchantStorePurchaseReportExportDTO implements Serializable {
    /**
     * 门店编号
     */
    @ExcelProperty(index = 0)
    private Long storeNo;

    @ExcelProperty(index = 0)
    private String storeCode;

    /**
     * 门店名称
     */
    @ExcelProperty(index = 1)
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    @ExcelProperty(index = 2)
    private String type;

    /**
     * 门店分组
     */
    @ExcelProperty(index = 3)
    private String storeGroup;
    /**
     * 商品编码
     */
    @ExcelProperty(index = 4)
    private Long productNo;

    /**
     * 商品名称
     */
    @ExcelProperty(index = 5)
    private String productName;

    /**
     * 规格
     */
    @ExcelProperty(index = 6)
    private String specification;

    /**
     * 规格单位
     */
    @ExcelProperty(index = 7)
    private String specificationUnit;

    /**
     * 货源：0自营仓、1三方仓
     */
    @ExcelProperty(index = 8)
    private String warehouseType;

    /**
     * 前台分组
     */
    @ExcelProperty(index = 9)
    private String marketClassification;

    /**
     * 后台类目
     */
    @ExcelProperty(index = 10)
    private String category;

    /**
     * 品牌
     */
    @ExcelProperty(index = 11)
    private String brand;

    /**
     * 商品数量
     */
    @ExcelProperty(index = 12)
    private Integer quantity;

    /**
     * 商品总金额
     */
    @ExcelProperty(index = 13)
    private BigDecimal amount;

    /**
     * 配送日期
     */
    @ExcelProperty(index = 14)
    private String deliveryTime;

    /**
     * 实际收货数量
     */
    @ExcelProperty(index = 15)
    private Integer receivedQuantity;

    /**
     * 补发数量
     */
    @ExcelProperty(index = 16)
    private Integer reissueQuantity;

    /**
     * 售后总金额
     */
    @ExcelProperty(index = 17)
    private BigDecimal afterSaleAmount;


    /**
     * 商品类型 0无货 1报价商品 2自营
     */
    private String goodsType;
}
