package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ProductStockForewarningReportDTO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * skucode
     */
    private String skuCode;

    /**
     * 仓库
     */
    private Integer warehouseNo;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 销售天数
     */
    private Integer saleDays;

    /**
     * 累计销量
     */
    private Integer saleQuantity;

    /**
     * 库存数
     */
    private Integer quantity;

    /**
     * 预警状态 0-正常 1-预警 2-售罄
     */
    private Integer forewarningStatus;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


    /**
     * 货品停用状态 0停用1启用
     */
    private Integer useFlag;
}
