package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/13 14:06
 */
@Data
public class ReportCommonQueryDTO {
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 货品名称
     */
    private String name;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 报表类型
     */
    private Integer excelType;

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 分页数量
     */
    private Integer pageSize;
}
