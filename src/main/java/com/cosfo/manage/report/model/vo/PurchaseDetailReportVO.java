package com.cosfo.manage.report.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/13 11:48
 */
@Data
public class PurchaseDetailReportVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购批次
     */
    private String purchaseNo;

    /**
     * 采购人
     */
    private String purchaser;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * 采购单状态
     */
    private String purchaseStatus;

    /**
     * sku
     */
    private Long skuId;

    /**
     * spu_id
     */
    private Long spuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 供应商名称
     */
    private String supplier;

    /**
     * 供应商
     */
    private Integer supplierId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 入库状态
     */
    private String inboundStatus;

    /**
     * 入库时间
     */
    private LocalDateTime inboundDate;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 实际入库金额
     */
    private BigDecimal inboundAmount;

    /**
     * 采购数量
     */
    private Integer purchaseQuantity;

    /**
     * 实际入库数量
     */
    private Integer inboundQuantity;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 三级类目
     */
    private Integer categoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 仓库服务商
     */
    private String warehouseServiceProvider;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    private static final long serialVersionUID = 1L;
}
