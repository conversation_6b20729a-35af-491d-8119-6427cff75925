package com.cosfo.manage.report.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 */
@Data
public class PosOrderAuditExportDTO implements Serializable {
    /**
     * 稽核区间
     */
    private String auditTime;
    /**
     * 门店名称
     */
    private String outStoreName;
    /**
     * 门店编码
     */
    private String outStoreCode;

    /**
     * 货品名称
     */
    private String outItemName;

    /**
     * 货品编码
     */
    private String outItemCode;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 规格
     */
    private String specification;
    /**
     * 销用总量
     */
    private BigDecimal useCount;

    /**
     * 应进货总量
     */
    private BigDecimal needBuyCount;

    /**
     * 实际帆台进货总量
     */
    private BigDecimal realBuyCount;

    /**
     * 差异总量
     */
    private BigDecimal diffCount;

    /**
     * 是否疑似私采状态
     * 正常=0 私采=1
     */
    private String status;
    /**
     * 差异比例
     */
    private String privateProcurement;
}
