package com.cosfo.manage.report.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DamageDetailReportVO implements Serializable {

    private Long id;

    /**
     * 出库日期
     */
    private LocalDate outboundDate;

    /**
     * 批次
     */
    private String damageNo;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * sku
     */
    private Long skuId;

    /**
     * spu_id
     */
    private Long spuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 采购人
     */
    private String purchaser;

    /**
     * 货损类型
     */
    private String damageType;

    /**
     * 货损凭证
     */
    private String credentials;

    /**
     * 货损金额
     */
    private BigDecimal damageAmount;

    /**
     * 货损数量
     */
    private Integer damageQuantity;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 一级类目
     */
    private Integer categoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 仓库供应商
     */
    private String warehouseServiceProvider;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;
}
