package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 近30天库存周转天数汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Getter
@Setter
@TableName("stock_turnover_summary")
public class StockTurnoverSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 货品id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 仓库号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 近30天库存周转天数
     */
    @TableField("turnover_days")
    private BigDecimal turnoverDays;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
