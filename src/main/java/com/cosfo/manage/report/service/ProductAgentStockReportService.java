package com.cosfo.manage.report.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.ProductStockWarnVO;
import com.cosfo.manage.report.model.dto.ProductAgentStockQueryDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.List;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description:
 */
public interface ProductAgentStockReportService {

    /**
     * 查询库存预警列表
     *
     * @param productAgentStockQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<ProductStockWarnVO> queryProductWarnList(ProductAgentStockQueryDTO productAgentStockQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 导出库存预警列表
     *
     * @param productAgentStockQueryDTO
     * @return
     */
    CommonResult exportProductWarnList(ProductAgentStockQueryDTO productAgentStockQueryDTO);

    /**
     * 同步符合条件的商品库存
     * @param agentTenantSkuDTOList
     * @param needThrow rpc接口异常是否直接抛出
     */
    void syncSkuCityStock(List<AgentTenantSkuDTO> agentTenantSkuDTOList, boolean needThrow);

    /**
     * 修正数据接口
     * @param skuId
     * @return
     */
    void correction(Long skuId);

    /**
     * 库存变更数据同步
     * @param productAgentSkuMapping
     * @param areaNo
     */
    void stockChangeSync(ProductAgentSkuMapping productAgentSkuMapping, int areaNo);
}
