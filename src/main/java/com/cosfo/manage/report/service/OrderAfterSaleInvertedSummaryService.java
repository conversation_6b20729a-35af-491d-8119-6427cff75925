package com.cosfo.manage.report.service;

import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
public interface OrderAfterSaleInvertedSummaryService {
    /**
     * 根据条件查询
     *
     * @param tenantId
     * @param startTime
     * @param endTime
     * @param resultHandler
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler);

    /**
     * @param tenantId  租户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    long count(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime);
}
