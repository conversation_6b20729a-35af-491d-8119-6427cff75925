package com.cosfo.manage.report.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStorePurchaseQueryDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStorePurchaseVO;
import com.cosfo.manage.product.model.dto.ProductDetailSalesQueryDTO;
import com.cosfo.manage.product.model.dto.ProductMovementQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSalesOverviewQueryDTO;
import com.cosfo.manage.product.model.vo.ProductMovementVO;
import com.cosfo.manage.product.model.vo.ProductSalesOverviewVO;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.dto.ReportQueryDTO;
import com.cosfo.manage.report.model.vo.*;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/11 15:37
 */
public interface ReportService {

    /**
     * 查询商品的销售概况
     * @param productSalesOverviewQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<ProductSalesOverviewVO> queryProductSalesOverview(ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询商品的动销情况
     * @param productMovementQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<ProductMovementVO> queryProductMovement(ProductMovementQueryDTO productMovementQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询门店采购概况
     * @param merchantStorePurchaseQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<MerchantStorePurchaseVO> queryMerchantStorePurchase(MerchantStorePurchaseQueryDTO merchantStorePurchaseQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 导出excel
     * @param reportQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult exportReportExcel(ReportQueryDTO reportQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 生成商品明细excel文件
     * @param productDetailSalesQueryDTO
     * @param fileDownloadRecordId
     */
    void generateProductDetailSalesExcel(ProductDetailSalesQueryDTO productDetailSalesQueryDTO, Long fileDownloadRecordId);


    /**
     * 货损明细
     *
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult<PageInfo<DamageDetailReportVO>> queryDamageDetail(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 货损明细导出
     *
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult exportDamageDetailReport(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 损售比明细列表
     *
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult<PageInfo<DamageSaleRatioDetailReportVO>> queryDamageSaleRatioDetail(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 损售比明细导出
     *
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult exportDamageSaleRatioDetailReport(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 数据概况
     *
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult<PurchaseDetailAggVO> queryPurchaseDetailAgg(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 查询采购明细
     *
     * @param queryDTO
     * @return
     */
    CommonResult<PageInfo<PurchaseDetailReportVO>> queryPurchaseDetailReport(ReportCommonQueryDTO queryDTO);

    /**
     * 采购明细导出
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult exportPurchaseDetailReport(ReportCommonQueryDTO reportCommonQueryDTO);

    /**
     * 查询采购退货明细
     *
     * @param queryDTO
     * @return
     */
    CommonResult<PageInfo<PurchasesBackDetailReportVO>> queryPurchaseBackDetailReport(ReportCommonQueryDTO queryDTO);

    /**
     * 采购退货明细导出
     * @param reportCommonQueryDTO
     * @return
     */
    CommonResult exportPurchaseBackDetailReport(ReportCommonQueryDTO reportCommonQueryDTO);
}
