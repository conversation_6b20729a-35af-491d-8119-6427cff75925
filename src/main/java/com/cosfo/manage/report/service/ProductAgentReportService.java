package com.cosfo.manage.report.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.ProductStockChangeRecordQueryDTO;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseShelfLifeVO;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.manage.product.model.vo.ProductStockChangeRecordVO;
import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseOverviewQueryDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseAggregationVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseOverviewVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/3
 */
public interface ProductAgentReportService {

    /**
     * 查询品牌方仓库列表
     *
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<List<ProductAgentWarehouseVO>> queryWarehouseList(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 分页查询仓库库存数据
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<PageInfo<ProductAgentWarehouseDataVO>> warehouseDataList(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 分页查询商品维度聚合库存数据
     * @param productAgentWarehouseDateQueryDTO
     * @param merchantInfoDTO
     * @return
     */
    CommonResult<PageInfo<ProductAgentWarehouseAggregationVO>> warehouseAggregationList(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 仓库库存总览信息
     * @param productAgentWarehouseOverviewQueryDTO
     * @param merchantInfoDTO
     * @return
     */
    CommonResult<ProductAgentWarehouseOverviewVO> warehouseOverview(ProductAgentWarehouseOverviewQueryDTO productAgentWarehouseOverviewQueryDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 导出报表
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult export(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 实时查询库存批次有效期
     *
     * @param queryDTO
     * @param tenantId
     * @return
     */
    CommonResult<PageInfo<ProductAgentWarehouseShelfLifeVO>> pageQueryBatchInventory(ProductAgentShelfLifeQueryDTO queryDTO, Long tenantId);

    /**
     * 查询库存变动记录
     * @param productStockChangeRecordQueryVO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<PageInfo<ProductStockChangeRecordVO>> pageQueryStockChangeRecordOld(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryVO, LoginContextInfoDTO loginContextInfoDTO);
    CommonResult<PageInfo<ProductStockChangeRecordVO>> pageQueryStockChangeRecord(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryVO, LoginContextInfoDTO loginContextInfoDTO);
}
