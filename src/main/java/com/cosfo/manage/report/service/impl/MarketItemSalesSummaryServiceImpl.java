package com.cosfo.manage.report.service.impl;

import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.manage.report.model.dto.MarketItemSummaryQueryDTO;
import com.cosfo.manage.report.model.po.MarketItemSalesSummary;
import com.cosfo.manage.report.repository.impl.MarketItemSalesSummaryRepositoryImpl;
import com.cosfo.manage.report.service.MarketItemSalesSummaryService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 13:44
 */
@Service
public class MarketItemSalesSummaryServiceImpl implements MarketItemSalesSummaryService {

    @Resource
    private MarketItemSalesSummaryRepositoryImpl marketItemSalesSummaryRepository;
    @Override
    public void queryByConditionWithHandler(MarketItemSummaryQueryDTO marketItemSummaryQueryDTO, ResultHandler<?> resultHandler) {
        marketItemSalesSummaryRepository.queryByConditionWithHandler(marketItemSummaryQueryDTO, resultHandler);
    }
}
