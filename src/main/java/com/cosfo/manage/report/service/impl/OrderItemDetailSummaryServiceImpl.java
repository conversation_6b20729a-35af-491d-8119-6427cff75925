package com.cosfo.manage.report.service.impl;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.report.model.dto.OrderPaymentStatistics;
import com.cosfo.manage.report.model.dto.OrderSummaryDTO;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import com.cosfo.manage.report.repository.OrderItemDetailSummaryRepository;
import com.cosfo.manage.report.service.OrderItemDetailSummaryService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/4/25 17:00
 */
@Service
public class OrderItemDetailSummaryServiceImpl implements OrderItemDetailSummaryService {

    @Resource
    private OrderItemDetailSummaryRepository orderItemDetailSummaryRepository;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler) {
        orderItemDetailSummaryRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, goodsType, resultHandler);
    }

    @Override
    public Integer queryItemCount(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderItemDetailSummaryRepository.queryItemCount(tenantId, startTime, endTime);
    }

    @Override
    public Integer queryStoreCountByPayType(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Integer payType) {
        return orderItemDetailSummaryRepository.queryStoreCountByPayType(tenantId, startTime, endTime, payType);
    }

    @Override
    public List<OrderPaymentStatistics> queryStoreCounts(TenantBillQueryDTO tenantBillQueryDTO) {
        return orderItemDetailSummaryRepository.queryStoreCounts(tenantBillQueryDTO);
    }

    @Override
    public OrderSummaryDTO querySummary(TenantBillQueryDTO tenantBillQueryDTO) {
        return orderItemDetailSummaryRepository.querySummary(tenantBillQueryDTO);
    }

    @Override
    public List<OrderItemDetailSummary> queryByCondition(Long tenantId, Long supplierId, Integer goodsType, List<String> orderNos) {
        return orderItemDetailSummaryRepository.queryByCondition(tenantId, supplierId, goodsType, orderNos);
    }
}

