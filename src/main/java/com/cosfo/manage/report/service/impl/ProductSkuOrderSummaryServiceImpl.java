package com.cosfo.manage.report.service.impl;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.facade.OfcFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.product.model.po.ProductSkuOrderSummary;
import com.cosfo.manage.product.repository.ProductSkuOrderSummaryRepository;
import com.cosfo.manage.report.service.ProductSkuOrderSummaryService;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.ofc.client.resp.goodssupply.GoodsSupplyOrderDetailQueryResp;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2023/11/1 上午11:21
 */
@Slf4j
@Service
public class ProductSkuOrderSummaryServiceImpl implements ProductSkuOrderSummaryService {

    @Resource
    private OfcFacade ofcFacade;
    @Resource
    private ProductSkuOrderSummaryRepository productSkuOrderSummaryRepository;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ProductFacade productFacade;
    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;


    /**
     * 统计销量，有效的订单状态
     */
    private static Set<Integer> VALID_ORDER_STATUS_SET = Sets.newHashSet(OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(), OrderStatusEnum.OUT_OF_STORAGE.getCode(), OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.FINISHED.getCode());

    @Override
    public void reportOrderSkuQuantity(List<String> orderNoList) {
        List<OrderResp> orderResps = orderQueryFacade.queryByNos(orderNoList);
        reportOrderSkuQuantityByOrderList(orderResps);
    }

    private void reportOrderSkuQuantityByOrderList(List<OrderResp> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        // 过滤无仓订单，无仓订单不统计货品销量
        orderList = orderList.stream().filter(e -> !WarehouseTypeEnum.PROPRIETARY.getCode().equals(e.getWarehouseType()) && VALID_ORDER_STATUS_SET.contains(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        handleSelfOrder(orderList);

        handlethreePartiesOrder(orderList);

    }

    private void handlethreePartiesOrder(List<OrderResp> orderList) {
        List<OrderResp> threePartiesOrderList = orderList.stream().filter(e -> WarehouseTypeEnum.THREE_PARTIES.getCode().equals(e.getWarehouseType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(threePartiesOrderList)) {
            return;
        }

        // 查询三方仓订单销量
        List<String> threePartiesOrderNoList = threePartiesOrderList.stream().map(OrderResp::getOrderNo).collect(Collectors.toList());
        List<GoodsSupplyOrderDetailQueryResp> goodsSupplyList = ofcFacade.queryGoodsSupplyOrderDetail(threePartiesOrderNoList);
        if (CollectionUtils.isEmpty(goodsSupplyList)) {
            return;
        }

        Map<String, List<GoodsSupplyOrderDetailQueryResp>> goodsSupplyMap = goodsSupplyList.stream().collect(Collectors.groupingBy(GoodsSupplyOrderDetailQueryResp::getOrderNo));

        List<String> skuCodes = goodsSupplyList.stream().map(GoodsSupplyOrderDetailQueryResp::getSkuCode).distinct().collect(Collectors.toList());
        List<ProductsMappingResp>  productAgentSkuMappingList = productFacade.queryBySkuCodes(skuCodes);

        Map<String, Long> skucode2skuIdMap = productAgentSkuMappingList.stream().collect(Collectors.toMap(ProductsMappingResp::getSku, ProductsMappingResp::getSkuId, (v1, v2) -> v1));

        for (OrderResp orderDTO : threePartiesOrderList) {
            // 重复请求，跳过
            if (isRepeatRequest(orderDTO.getOrderNo())) {
                continue;
            }

            List<ProductSkuOrderSummary> productSkuOrderSummaryList = new ArrayList<>();

            List<GoodsSupplyOrderDetailQueryResp> tmpList = goodsSupplyMap.get(orderDTO.getOrderNo());
            if (CollectionUtils.isEmpty(tmpList)) {
                continue;
            }

            for (GoodsSupplyOrderDetailQueryResp goodsSupplyOrderDetailQueryResp : tmpList) {
                ProductSkuOrderSummary productSkuOrderSummary = new ProductSkuOrderSummary();
                productSkuOrderSummary.setTenantId(orderDTO.getTenantId());
                productSkuOrderSummary.setSkuId(skucode2skuIdMap.get(goodsSupplyOrderDetailQueryResp.getSkuCode()));
                productSkuOrderSummary.setSkuCode(goodsSupplyOrderDetailQueryResp.getSkuCode());
                productSkuOrderSummary.setWarehouseNo(goodsSupplyOrderDetailQueryResp.getWarehouseNo().intValue());
                productSkuOrderSummary.setOrderQuantity(goodsSupplyOrderDetailQueryResp.getSupplyQuantity());
                productSkuOrderSummary.setOrderTime(orderDTO.getCreateTime().toLocalDate());
                productSkuOrderSummaryList.add(productSkuOrderSummary);
            }
            productSkuOrderSummaryRepository.batchSaveOrUpdate(productSkuOrderSummaryList);
        }
    }


    private void handleSelfOrder(List<OrderResp> orderList) {
        List<OrderResp> selfOrderList = orderList.stream().filter(e -> WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(e.getWarehouseType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selfOrderList)) {
            return;
        }

        // 查询自营仓订单销量
        List<Long> selfOrderIds = selfOrderList.stream().map(OrderResp::getId).collect(Collectors.toList());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(selfOrderIds);
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryOrderItemList(orderItemQueryReq);
        if (CollectionUtils.isEmpty(orderItemAndSnapshotResps)) {
            return;
        }
        Map<Long, List<OrderItemAndSnapshotResp>> orderItemAndSnapshotDTOMap = orderItemAndSnapshotResps.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));

        List<Long> selfSkuIds = orderItemAndSnapshotResps.stream().map(OrderItemAndSnapshotResp::getSkuId).distinct().collect(Collectors.toList());

        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setSkuIds(selfSkuIds);
        List<ProductsMappingResp> selfProductAgentSkuMappings = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        Map<Long, String> skuId2skuCodeMap = selfProductAgentSkuMappings.stream().collect(Collectors.toMap(ProductsMappingResp::getSkuId, ProductsMappingResp::getSku, (v1, v2) -> v1));

        for (OrderResp orderDTO : selfOrderList) {
            // 重复请求，跳过
            if (isRepeatRequest(orderDTO.getOrderNo())) {
                continue;
            }

            List<ProductSkuOrderSummary> productSkuOrderSummaryList = new ArrayList<>();

            List<OrderItemAndSnapshotResp> snapshotDTOS = orderItemAndSnapshotDTOMap.get(orderDTO.getId());
            if (CollectionUtils.isEmpty(snapshotDTOS)) {
                continue;
            }

            for (OrderItemAndSnapshotResp snapshotDTO : snapshotDTOS) {
                ProductSkuOrderSummary productSkuOrderSummary = new ProductSkuOrderSummary();
                productSkuOrderSummary.setTenantId(orderDTO.getTenantId());
                productSkuOrderSummary.setSkuId(snapshotDTO.getSkuId());
                productSkuOrderSummary.setSkuCode(skuId2skuCodeMap.get(snapshotDTO.getSkuId()));
                productSkuOrderSummary.setWarehouseNo(NumberUtils.toInt(orderDTO.getWarehouseNo(), -1));
                productSkuOrderSummary.setOrderQuantity(snapshotDTO.getAmount());
                productSkuOrderSummary.setOrderTime(orderDTO.getCreateTime().toLocalDate());
                productSkuOrderSummaryList.add(productSkuOrderSummary);
            }

            productSkuOrderSummaryRepository.batchSaveOrUpdate(productSkuOrderSummaryList);
        }
    }

    /**
     * 检查订单是否重复计算销量，true-是 false-否
     *
     * @param orderNo
     * @return
     */
    private boolean isRepeatRequest(String orderNo) {
        try {
            String redisKey = RedisKeyEnum.CM00009.join(orderNo);
            RLock lock = redissonClient.getLock(redisKey);
            // 获取到锁，不是重复请求
            if (lock.tryLock(0, 2, TimeUnit.DAYS)) {
                return false;
            }
        } catch (InterruptedException e) {
            log.error("货品销量统计获取redis锁异常", e);
        }

        // 未获取到锁，重复请求
        return true;
    }

    @Override
    public void initProductSkuOrderSummary(Integer saleDays) {
        LocalDateTime startTime = LocalDate.now().minusDays(saleDays).atStartOfDay();
        LocalDateTime endTime = LocalDate.now().atStartOfDay();

        OrderQueryReq orderQueryReq = OrderQueryReq.builder().createStartTime(startTime).createEndTime(endTime).build();
        orderQueryReq.setMaxId(0L);
        orderQueryReq.setBatchSize(200);

        boolean loopFlag = true;
        while (loopFlag) {
            List<OrderResp> orderResps = orderQueryFacade.queryOrderList(orderQueryReq);
            if (CollectionUtils.isEmpty(orderResps)) {
                loopFlag = false;
                break;
            }
            orderQueryReq.setMaxId(orderResps.get(orderResps.size() - 1).getId());

            reportOrderSkuQuantityByOrderList(orderResps);
        }
    }
}
