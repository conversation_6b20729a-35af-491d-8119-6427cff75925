package com.cosfo.manage.report.mapper;

import com.cosfo.manage.product.model.dto.ProductDetailSalesDTO;
import com.cosfo.manage.product.model.dto.ProductDetailSalesQueryDTO;
import java.util.List;
import org.apache.ibatis.session.ResultHandler;

/**
 * <AUTHOR>
 */
public interface ProductDetailSalesMapper {

    /**
     * 查询所有
     * @param productDetailSalesQueryDTO
     * @return
     */
    List<ProductDetailSalesDTO> queryAll(ProductDetailSalesQueryDTO productDetailSalesQueryDTO);

    /**
     * 流式查询所有
     * @param productDetailSalesQueryDTO
     * @return
     */
    void queryAll(ProductDetailSalesQueryDTO productDetailSalesQueryDTO, ResultHandler<?> resultHandler);
}
