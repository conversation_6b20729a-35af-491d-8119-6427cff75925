package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.po.PurchaseDetailReport;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 采购明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Mapper
public interface PurchaseDetailReportMapper extends BaseMapper<PurchaseDetailReport> {

    @Select("select * from purchase_detail_report ${ew.customSqlSegment}")
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(PurchaseDetailReport.class)
    void listPurchaseDetailForExport(@Param(Constants.WRAPPER) Wrapper wrapper, ResultHandler<?> resultHandler);


    Page<PurchaseSummarySkuVO> queryPurchaseSummaryListBySku(Page<PurchaseSummarySkuVO> page, @Param("queryDTO") PurchaseSummarySkuQueryDTO queryDTO);

    Page<PurchaseSummarySupplierVO> queryPurchaseSummaryListBySupplier(Page<PurchaseSummarySupplierVO> page, @Param("queryDTO") PurchaseSummarySupplierQueryDTO queryDTO);

}
