package com.cosfo.manage.report.mapper;

import com.cosfo.manage.product.model.dto.ProductSalesOverviewQueryDTO;
import com.cosfo.manage.product.model.po.ProductSalesOverview;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/12 13:11
 */
public interface ProductSalesOverviewMapper {


    /**
     * 查询全部
     * @param productSalesOverviewQueryDTO
     * @return
     */
    List<ProductSalesOverview> queryAll(ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO);

    /**
     * 查询总计
     * @param productSalesOverviewQueryDTO
     * @return
     */
    ProductSalesOverview querySummary(ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO);

    /**
     * 批量查询总计
     * @param productSalesOverviewQueryDTO
     * @return
     */
    List<ProductSalesOverview> batchQuerySummary(ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO);
}
