package com.cosfo.manage.report.mapper;

import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportion;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionAnalysisExcelDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 门店订货占比分析 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Mapper
public interface MerchantStoreOrderProportionAnalysisMapper extends BaseMapper<MerchantStoreOrderProportionAnalysis> {

    /**
     * 查询占比数据
     * @param merchantStoreOrderProportionQueryDTO
     * @return
     */
    List<MerchantStoreOrderProportion> querySumByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);

    /**
     * 查询占比数据
     * @param merchantStoreOrderProportionQueryDTO
     * @return
     */
    List<MerchantStoreOrderProportionAnalysis> listByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);

    /**
     * 导出
     * @param merchantStoreOrderProportionQueryDTO
     * @param handler
     */
    void exportByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO, LargeDataSetExporter<MerchantStoreOrderProportionAnalysis, MerchantStoreOrderProportionAnalysisExcelDTO> handler);
}
