package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cosfo.manage.report.model.po.PurchasesBackDetailReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 采购退货明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Mapper
public interface PurchasesBackDetailReportMapper extends BaseMapper<PurchasesBackDetailReport> {

    @Select("select * from purchases_back_detail_report ${ew.customSqlSegment}")
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(PurchasesBackDetailReport.class)
    void listPurchaseBackDetailForExport(@Param(Constants.WRAPPER) Wrapper wrapper, ResultHandler<?> resultHandler);
}
