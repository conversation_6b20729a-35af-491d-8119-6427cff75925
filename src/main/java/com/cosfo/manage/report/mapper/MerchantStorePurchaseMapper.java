package com.cosfo.manage.report.mapper;

import com.cosfo.manage.merchant.model.dto.MerchantStorePurchaseQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStorePurchase;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/13 15:14
 */
public interface MerchantStorePurchaseMapper {

    /**
     * 查询
     * @param merchantStorePurchaseQueryDTO
     * @return
     */
    MerchantStorePurchase querySummary(MerchantStorePurchaseQueryDTO merchantStorePurchaseQueryDTO);

    /**
     * 批量查询
     * @param merchantStorePurchaseQueryDTO
     * @return
     */
    List<MerchantStorePurchase> batchQuerySummary(MerchantStorePurchaseQueryDTO merchantStorePurchaseQueryDTO);
}
