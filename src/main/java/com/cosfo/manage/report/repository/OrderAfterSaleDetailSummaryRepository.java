package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.OrderAfterSaleDetailSummaryQueryDTO;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 售后订单汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface OrderAfterSaleDetailSummaryRepository extends IService<OrderAfterSaleDetailSummary> {

    /**
     * 根据条件查询
     * @param tenantId
     * @param supplierId
     * @param startTime
     * @param endTime
     * @param resultHandler
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler);

    /**
     * 根据条件查询
     * @param orderAfterSaleDetailSummaryQueryDTO
     * @return
     */
    OrderAfterSaleDetailSummary queryByConditionSum(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO);

    /**
     * 根据条件查询
     * @param orderAfterSaleDetailSummaryQueryDTO
     * @return
     */
    List<OrderAfterSaleDetailSummary> queryByCondition(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO);
}
