package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.TenantMetricsSummaryMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.report.repository.TenantMetricsSummaryRepository;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 租户指标汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class TenantMetricsSummaryRepositoryImpl extends ServiceImpl<TenantMetricsSummaryMapper, TenantMetricsSummary> implements TenantMetricsSummaryRepository {

    @Resource
    private TenantMetricsSummaryMapper tenantMetricsSummaryMapper;
    @Override
    public TenantMetricsSummary queryByTenantAndTimeTag(Long tenantId, String timeTag) {
        QueryWrapper<TenantMetricsSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), "tenant_id", tenantId);
        queryWrapper.eq(Objects.nonNull(timeTag), "time_tag", timeTag);
        return tenantMetricsSummaryMapper.selectOne(queryWrapper);
    }
    @Override
    public List<TenantMetricsSummary> selectByTenantIdAndDate(Long tenantId, String startDate, String endDate) {

        return this.lambdaQuery()
                .eq(TenantMetricsSummary::getTenantId, tenantId)
                .ge(TenantMetricsSummary::getTimeTag, startDate)
                .le(TenantMetricsSummary::getTimeTag, endDate)
                .orderByDesc(TenantMetricsSummary::getTimeTag)
                .list();
    }
}
