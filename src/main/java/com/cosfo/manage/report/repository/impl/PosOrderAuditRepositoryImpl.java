package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.file.model.po.CommonResource;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import com.cosfo.manage.report.model.dto.PosOrderAuditQueryDTO;
import com.cosfo.manage.report.model.po.PosOrderAudit;
import com.cosfo.manage.report.mapper.PosOrderAuditMapper;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;
import com.cosfo.manage.report.repository.PosOrderAuditRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 门店进销稽核表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Service
public class PosOrderAuditRepositoryImpl extends ServiceImpl<PosOrderAuditMapper, PosOrderAudit> implements PosOrderAuditRepository {

    @Override
    public Page<PosOrderAuditVO> listPage(PosOrderAuditQueryDTO dto) {
        Page<PosOrderAuditVO> page = new Page<>(dto.getPageIndex (), dto.getPageSize());
        return getBaseMapper ().listPage(page,dto);
    }
}
