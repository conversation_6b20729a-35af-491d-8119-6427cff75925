package com.cosfo.manage.report.repository.impl;

/**
 * <p>
 * 售价低于成本价订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
//@Service
//public class OrderSoldBelowSupplySummaryRepositoryImpl extends ServiceImpl<OrderSoldBelowSupplySummaryMapper, OrderSoldBelowSupplySummary> implements OrderSoldBelowSupplySummaryRepository {
//
//    @Resource
//    private OrderSoldBelowSupplySummaryMapper orderSoldBelowSupplySummaryMapper;
//
//    @Override
//    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler) {
//        LambdaQueryWrapper<OrderSoldBelowSupplySummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(OrderSoldBelowSupplySummary::getTenantId, tenantId);
//        lambdaQueryWrapper.between(OrderSoldBelowSupplySummary::getTimeTag, startTime, endTime);
//        lambdaQueryWrapper.eq(OrderSoldBelowSupplySummary::getSupplierId, supplierId);
//        orderSoldBelowSupplySummaryMapper.queryByConditionWithHandler(lambdaQueryWrapper, resultHandler);
//    }
//}
