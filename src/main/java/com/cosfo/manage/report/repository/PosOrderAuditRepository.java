package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.report.model.dto.PosOrderAuditQueryDTO;
import com.cosfo.manage.report.model.po.PosOrderAudit;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;

/**
 * <p>
 * 门店进销稽核表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface PosOrderAuditRepository extends IService<PosOrderAudit> {

    Page<PosOrderAuditVO> listPage(PosOrderAuditQueryDTO dto);
}
