package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.report.mapper.SupplierMetricsSummaryMapper;
import com.cosfo.manage.report.model.po.SupplierMetricsSummary;
import com.cosfo.manage.report.repository.SupplierMetricsSummaryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 供应商指标汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class SupplierMetricsSummaryRepositoryImpl extends ServiceImpl<SupplierMetricsSummaryMapper, SupplierMetricsSummary> implements SupplierMetricsSummaryRepository {

    @Resource
    private SupplierMetricsSummaryMapper supplierMetricsSummaryMapper;

    @Override
    public List<SupplierMetricsSummary> listByTenantIdAndTimeTag(Long tenantId, String timeTag, String sortField) {
        QueryWrapper<SupplierMetricsSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId).eq("time_tag", timeTag);
        queryWrapper.orderByDesc(!StringUtils.isBlank(sortField), sortField);
        return supplierMetricsSummaryMapper.selectList(queryWrapper);
    }
}
