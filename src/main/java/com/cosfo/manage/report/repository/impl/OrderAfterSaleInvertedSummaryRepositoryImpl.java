package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.OrderAfterSaleInvertedSummaryMapper;
import com.cosfo.manage.report.model.po.OrderAfterSaleInvertedSummary;
import com.cosfo.manage.report.repository.OrderAfterSaleInvertedSummaryRepository;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
@Service
public class OrderAfterSaleInvertedSummaryRepositoryImpl extends ServiceImpl<OrderAfterSaleInvertedSummaryMapper, OrderAfterSaleInvertedSummary> implements OrderAfterSaleInvertedSummaryRepository {

    @Resource
    private OrderAfterSaleInvertedSummaryMapper orderAfterSaleInvertedSummaryMapper;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<OrderAfterSaleInvertedSummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderAfterSaleInvertedSummary::getTenantId, tenantId);
        lambdaQueryWrapper.eq(OrderAfterSaleInvertedSummary::getSupplierId, supplierId);
        lambdaQueryWrapper.between(OrderAfterSaleInvertedSummary::getTimeTag, startTime, endTime);
        orderAfterSaleInvertedSummaryMapper.queryByConditionWithHandler(lambdaQueryWrapper, resultHandler);
    }
}
