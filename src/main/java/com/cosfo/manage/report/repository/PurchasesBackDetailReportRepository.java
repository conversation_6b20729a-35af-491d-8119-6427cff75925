package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.PurchasesBackDetailReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 采购退货明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
public interface PurchasesBackDetailReportRepository extends IService<PurchasesBackDetailReport> {

    /**
     * 获取采购退货报表汇总信息
     * @param queryDTO
     * @return
     */
    PurchaseDetailAggVO getPurchaseBackDetailAgg(ReportCommonQueryDTO queryDTO);


    /**
     * 查询所有
     * @param queryDTO
     * @return
     */
    Page<PurchasesBackDetailReport> queryPurchaseBackDetailPage(ReportCommonQueryDTO queryDTO);


    /**
     * 获取采购退货明细导出列表，限制3000条
     *
     * @param reportCommonQueryDTO
     * @return
     */
    void listPurchaseBackDetailForExport(ReportCommonQueryDTO reportCommonQueryDTO, ResultHandler<?> resultHandler);

}
