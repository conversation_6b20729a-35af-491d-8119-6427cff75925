package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail;

import java.util.List;

/**
 * <p>
 * 商品上架售罄汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface MarketItemOnSaleSoldOutDetailRepository extends IService<MarketItemOnSaleSoldOutDetail> {

    /**
     * 根据租户id和时间标签查询
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    MarketItemOnSaleSoldOutDetail querySummary(Long tenantId, String startTime, String endTime);


    List<MarketItemOnSaleSoldOutDetail> selectByTenantIdAndTimeTag(Long tenantId, String startTime, String endTime);

}
