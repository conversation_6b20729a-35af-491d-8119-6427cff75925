package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.DamageDetailReportMapper;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.DamageDetailReport;
import com.cosfo.manage.report.repository.DamageDetailReportRepository;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 货损明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Service
public class DamageDetailReportRepositoryImpl extends ServiceImpl<DamageDetailReportMapper, DamageDetailReport> implements DamageDetailReportRepository {

    @Resource
    private DamageDetailReportMapper damageDetailReportMapper;

    @Override
    public Page<DamageDetailReport> queryDamageDetailPage(ReportCommonQueryDTO reportCommonQueryDTO) {
        LambdaQueryWrapper<DamageDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DamageDetailReport::getTenantId, reportCommonQueryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(reportCommonQueryDTO.getStartTime()) && StringUtils.isNotBlank(reportCommonQueryDTO.getEndTime()),DamageDetailReport::getOutboundDate, reportCommonQueryDTO.getStartTime(), reportCommonQueryDTO.getEndTime());
        queryWrapper.eq(reportCommonQueryDTO.getWarehouseId() != null, DamageDetailReport::getWarehouseId, reportCommonQueryDTO.getWarehouseId());
        queryWrapper.eq(reportCommonQueryDTO.getSkuId() != null, DamageDetailReport::getSkuId, reportCommonQueryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(reportCommonQueryDTO.getName()), DamageDetailReport::getName, reportCommonQueryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds()), DamageDetailReport::getCategoryId, reportCommonQueryDTO.getCategoryIds());
        queryWrapper.orderByDesc(DamageDetailReport::getOutboundDate);
        Page<DamageDetailReport> page = page(new Page<>(reportCommonQueryDTO.getPageIndex(),reportCommonQueryDTO.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void listDamageDetailForExport(ReportCommonQueryDTO reportCommonQueryDTO, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<DamageDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DamageDetailReport::getTenantId, reportCommonQueryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(reportCommonQueryDTO.getStartTime()) && StringUtils.isNotBlank(reportCommonQueryDTO.getEndTime()),DamageDetailReport::getOutboundDate, reportCommonQueryDTO.getStartTime(), reportCommonQueryDTO.getEndTime());
        queryWrapper.eq(reportCommonQueryDTO.getWarehouseId() != null, DamageDetailReport::getWarehouseId, reportCommonQueryDTO.getWarehouseId());
        queryWrapper.eq(reportCommonQueryDTO.getSkuId() != null, DamageDetailReport::getSkuId, reportCommonQueryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(reportCommonQueryDTO.getName()), DamageDetailReport::getName, reportCommonQueryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds()), DamageDetailReport::getCategoryId, reportCommonQueryDTO.getCategoryIds());
        queryWrapper.orderByDesc(DamageDetailReport::getOutboundDate);
//        queryWrapper.last("limit 3000");
        damageDetailReportMapper.listDamageDetailForExport(queryWrapper, resultHandler);
    }
}
