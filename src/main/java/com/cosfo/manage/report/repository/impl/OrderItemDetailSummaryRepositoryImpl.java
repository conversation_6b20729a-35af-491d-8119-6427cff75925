package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.report.model.dto.OrderPaymentStatistics;
import com.cosfo.manage.report.model.dto.OrderSummaryDTO;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import com.cosfo.manage.report.mapper.OrderItemDetailSummaryMapper;
import com.cosfo.manage.report.repository.OrderItemDetailSummaryRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单明细详情汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
public class OrderItemDetailSummaryRepositoryImpl extends ServiceImpl<OrderItemDetailSummaryMapper, OrderItemDetailSummary> implements OrderItemDetailSummaryRepository {

    @Resource
    private OrderItemDetailSummaryMapper orderItemDetailSummaryMapper;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<OrderItemDetailSummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderItemDetailSummary::getTenantId, tenantId);
        lambdaQueryWrapper.eq(Objects.nonNull(supplierId), OrderItemDetailSummary::getSupplierId, supplierId);
        lambdaQueryWrapper.between(OrderItemDetailSummary::getTimeTag, startTime, endTime);
        lambdaQueryWrapper.eq(OrderItemDetailSummary::getGoodsType, goodsType);
        orderItemDetailSummaryMapper.queryByConditionWithHandler(lambdaQueryWrapper, resultHandler);
    }

    @Override
    public Integer queryItemCount(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OrderItemDetailSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct item_id").eq("tenant_id", tenantId).between("time_tag", startTime, endTime);
        Long itemCount = orderItemDetailSummaryMapper.selectCount(queryWrapper);
        return itemCount == null ? 0 : itemCount.intValue();
    }

    @Override
    public Integer queryStoreCountByPayType(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Integer payType) {
        QueryWrapper<OrderItemDetailSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct store_name").eq("tenant_id", tenantId).between("time_tag", startTime, endTime).eq("pay_type", payType);
        Long storeCount = orderItemDetailSummaryMapper.selectCount(queryWrapper);
        return storeCount == null ? 0 : storeCount.intValue();
    }

    @Override
    public List<OrderPaymentStatistics> queryStoreCounts(TenantBillQueryDTO tenantBillQueryDTO) {
        return orderItemDetailSummaryMapper.queryStoreCounts(tenantBillQueryDTO);
    }

    @Override
    public OrderSummaryDTO querySummary(TenantBillQueryDTO tenantBillQueryDTO) {
        return orderItemDetailSummaryMapper.querySummary(tenantBillQueryDTO);
    }

    @Override
    public List<OrderItemDetailSummary> queryByCondition(Long tenantId, Long supplierId, Integer goodsType, List<String> orderNos) {
        LambdaQueryWrapper<OrderItemDetailSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItemDetailSummary::getTenantId, tenantId);
        queryWrapper.eq(Objects.nonNull(supplierId), OrderItemDetailSummary::getSupplierId, supplierId);
        queryWrapper.eq(OrderItemDetailSummary::getGoodsType, goodsType);
        queryWrapper.in(OrderItemDetailSummary::getOrderNo, orderNos);
        return orderItemDetailSummaryMapper.selectList(queryWrapper);
    }
}
