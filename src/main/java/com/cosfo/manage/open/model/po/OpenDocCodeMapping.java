package com.cosfo.manage.open.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 外部单据映射关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@TableName("open_doc_code_mapping")
public class OpenDocCodeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 映射类型 1=订单
     */
    @TableField("doc_type")
    private Integer docType;

    /**
     * 外部系统类型 1=金蝶
     */
    @TableField("open_type")
    private Integer openType;

    /**
     * 请求
     */
    @TableField("req")
    private String req;

    /**
     * 响应
     */
    @TableField("resp")
    private String resp;

    /**
     * 外部系统code
     */
    @TableField("out_code")
    private String outCode;

    /**
     * 内部系统code
     */
    @TableField("doc_code")
    private String docCode;


}
