package com.cosfo.manage.facade.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 履约单入库信息
 * @author: xiaowk
 * @date: 2023/4/13 下午10:48
 */
@Data
public class FulfillmentInboundDTO {
    /**
     * 入库时间
     */
    private LocalDateTime inBoundTime;

    /**
     * 入库凭证照片
     */
    private String inboundPics;


    /**
     * 计划量
     */
    private Integer quantity;


    /**
     * 实际入库量
     */
    private Integer actualQuantity;


    /**
     * 原因，入库说明
     */
    private String reason;


    /**
     * 入库状态,0-未完成，1-已完成
     *
     * @see net.summerfarm.ofc.client.enums.FulfillmentInboundStatusEnum
     */
    private Integer inboundStatus;
}
