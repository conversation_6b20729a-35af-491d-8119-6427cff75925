package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.facade.dto.FulfillmentDeliveryInfoDTO;
import com.cosfo.manage.facade.dto.FulfillmentInboundDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleDeliveryDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.FulfillmentDeliveryInfoReq;
import net.summerfarm.ofc.client.req.InsertAfterSaleLogisticsReq;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.req.UpdateFulfillmentDeliveryInfoReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.summerfarm.ofc.client.resp.OfcInBoundTaskInfoResp;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseStorageQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  售后调用ofc接口
 * @author: xiaowk
 * @date: 2023/4/13 下午9:47
 */
@Component
@Slf4j
public class OfcAfterSaleFacade {

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;
    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;
    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;


    /**
     * 提交物流信息
     * @param orderAfterSaleDeliveryDTO
     * @param operator
     */
    public void insertAfterSaleLogistics(OrderAfterSaleDeliveryDTO orderAfterSaleDeliveryDTO, String operator) {
        InsertAfterSaleLogisticsReq req = new InsertAfterSaleLogisticsReq();
        req.setAfterSaleOrderNo(orderAfterSaleDeliveryDTO.getAfterSaleOrderNo());
        req.setOperator(operator);
        FulfillmentDeliveryInfoReq deliveryInfoReq = new FulfillmentDeliveryInfoReq();
        deliveryInfoReq.setType(orderAfterSaleDeliveryDTO.getDeliveryType());
        deliveryInfoReq.setLogisticsCompany(orderAfterSaleDeliveryDTO.getDeliveryCompany());
        deliveryInfoReq.setLogisticsNo(orderAfterSaleDeliveryDTO.getDeliveryNo());
        deliveryInfoReq.setRemark(orderAfterSaleDeliveryDTO.getRemark());
        req.setDeliveryInfo(deliveryInfoReq);

        log.info("insertAfterSaleLogistics InsertAfterSaleLogisticsReq={}", JSON.toJSONString(req));
        DubboResponse<Void> response = fulfillmentOrderOperateProvider.insertAfterSaleLogistics(req);
        log.info("insertAfterSaleLogistics response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("insertAfterSaleLogistics error response={}", JSON.toJSONString(response));
            throw new ProviderException(response.getMsg());
        }
    }

    /**
     * 查看物流信息及出库信息 根据售后单号
     * deliveryType=2 自营仓补发单查物流和出库信息
     * deliveryType=1 无仓补发单查物流信息
     * deliveryType=3 买家退货物流
     * @param sourceNoList
     * @return
     */
    public List<FulfillmentDeliveryInfoDTO> queryDelivery(List<String> sourceNoList) {
        QueryFulfillmentDeliveryReq req = new QueryFulfillmentDeliveryReq();
        req.setOrderNoList(sourceNoList);
        DubboResponse<List<FulfillmentDeliveryResp>> resp = fulfillmentOrderQueryProvider.queryOrderDelivery(req);
        if (!resp.isSuccess()) {
            throw new ProviderException(resp.getMsg());
        }
        List<FulfillmentDeliveryResp> data = resp.getData();
        if (CollectionUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.stream().map(e -> {
            FulfillmentDeliveryInfoDTO fulfillmentDeliveryInfoDTO = new FulfillmentDeliveryInfoDTO();
            BeanUtils.copyProperties(e, fulfillmentDeliveryInfoDTO);
            return fulfillmentDeliveryInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询入库信息 （售后退货入库信息查询）
     * @param afterSaleNo
     * @return
     */
    public FulfillmentInboundDTO queryInboundInfo(String afterSaleNo) {
        DubboResponse<OfcInBoundTaskInfoResp> response = fulfillmentOrderQueryProvider.queryInboundInfo(afterSaleNo);
        if (!response.isSuccess()) {
            throw new ProviderException("查询售后入库信息失败：" + response.getMsg());
        }

        OfcInBoundTaskInfoResp data = response.getData();
        FulfillmentInboundDTO fulfillmentInboundDTO = new FulfillmentInboundDTO();
        BeanUtils.copyProperties(data, fulfillmentInboundDTO);
        if(StringUtils.isNotBlank(fulfillmentInboundDTO.getInboundPics())){
            try {
                fulfillmentInboundDTO.setInboundPics("");
                List<String> piclist = JSONArray.parseArray(fulfillmentInboundDTO.getInboundPics(), String.class);
                if(!CollectionUtils.isEmpty(piclist)) {
                    fulfillmentInboundDTO.setInboundPics(String.join(",", piclist));
                }
            } catch (Exception e) {
                log.error("解析错误 inboundpics=" + fulfillmentInboundDTO.getInboundPics(), e);
            }
        }
        return fulfillmentInboundDTO;
    }

    /**
     * 查询仓库信息
     * @param warehouseNo
     * @return
     */
    public WarehouseStorageResp queryWarehouse(Integer warehouseNo) {
        WarehouseStorageQueryReq req = new WarehouseStorageQueryReq();
        req.setWarehouseNo(warehouseNo);
        DubboResponse<WarehouseStorageResp> response = warehouseStorageQueryProvider.queryOneWarehouseStorage(req);
        if (!response.isSuccess()) {
            throw new ProviderException("查询仓库信息：" + response.getMsg());
        }

        return response.getData();
    }

    /**
     * 更新无仓售后补发物流信息
     * @param dto
     * @param operator
     */
    public void updateDeliveryInfo(OrderAfterSaleDeliveryDTO dto, String operator) {
        List<FulfillmentDeliveryInfoDTO> deliveryInfoDTOList = queryDelivery(Lists.newArrayList(dto.getAfterSaleOrderNo()));
        if (CollectionUtil.isEmpty(deliveryInfoDTOList)) {
            throw new DefaultServiceException("售后物流信息不存在，afterSaleOrderNo:" + dto.getAfterSaleOrderNo());
        }
        UpdateFulfillmentDeliveryInfoReq req = new UpdateFulfillmentDeliveryInfoReq();
        req.setSourceOrderNo(dto.getAfterSaleOrderNo());
        req.setOperator(operator);
        req.setBatchNo(deliveryInfoDTOList.get(0).getBatchNo());
        FulfillmentDeliveryInfoReq fulfillmentDeliveryInfoReq = new FulfillmentDeliveryInfoReq();
        fulfillmentDeliveryInfoReq.setType(dto.getDeliveryType());
        fulfillmentDeliveryInfoReq.setLogisticsCompany(dto.getDeliveryCompany());
        fulfillmentDeliveryInfoReq.setLogisticsNo(dto.getDeliveryNo());
        fulfillmentDeliveryInfoReq.setRemark(dto.getRemark());
        req.setNewDeliveryInfo(fulfillmentDeliveryInfoReq);
        DubboResponse<Void> resp = fulfillmentOrderOperateProvider.updateDeliveryInfo(req);
        if (!resp.isSuccess()) {
            throw new ProviderException(resp.getMsg());
        }
    }
}
