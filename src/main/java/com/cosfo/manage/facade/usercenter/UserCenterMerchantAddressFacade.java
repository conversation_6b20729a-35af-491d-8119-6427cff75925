package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-31
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantAddressFacade {

    @DubboReference
    private MerchantAddressCommandProvider merchantAddressCommandProvider;

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;

//    /**
//     * 注意：模型目前是允许门店没有默认地址的，不过门店一定会有一个状态正常的地址。
//     * 查询门店默认地址
//     * defaultFlag：是
//     * status：正常
//     * @param
//     * @return
//     */
//    public MerchantAddressResultResp getDefaultMerchantAddress(Long storeId, Long tenantId){
//        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
//        merchantAddressQueryReq.setStoreId(storeId);
//        merchantAddressQueryReq.setTenantId(tenantId);
//        DubboResponse<MerchantAddressResultResp> response = merchantAddressQueryProvider.getDefaultMerchantAddress(merchantAddressQueryReq);
//        if (!response.isSuccess()) {
//            String errorMsg = StringUtils.builderErrorMsg(response,"查询门店默认地址失败");
//            throw new BizException(errorMsg);
//        }
//        return response.getData();
//    }

    /**
     * 根据指定参数查询门店地址列表
     * @param
     * @return
     */
    public List<MerchantAddressResultResp> getMerchantAddressList(MerchantAddressQueryReq req){
        DubboResponse<List<MerchantAddressResultResp>> response = merchantAddressQueryProvider.getMerchantAddressList(req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询门店地址列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 查询指定租户下所有的省、市、区
     * @param
     * @return
     */
    public List<String> getConcatAddress(Long tenantId){
        DubboResponse<List<String>> response = merchantAddressQueryProvider.getConcatAddress(tenantId);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询指定租户下所有的省、市、区失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

//    /**
//     * 查询默认地址与联系人列表
//     * @param
//     * @return
//     */
//    public List<MerchantDeliveryAddressResultResp> getDefaultMerchantAddressList(List<Long> storeIdList, Long tenantId){
//        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
//        merchantAddressQueryReq.setStoreIdList(storeIdList);
//        merchantAddressQueryReq.setTenantId(tenantId);
//        DubboResponse<List<MerchantDeliveryAddressResultResp>> response = merchantAddressQueryProvider.getDefaultMerchantAddressList(merchantAddressQueryReq);
//        if (!response.isSuccess()) {
//            String errorMsg = StringUtils.builderErrorMsg(response,"查询默认地址与联系人列表失败");
//            throw new BizException(errorMsg);
//        }
//        return response.getData();
//    }

}
