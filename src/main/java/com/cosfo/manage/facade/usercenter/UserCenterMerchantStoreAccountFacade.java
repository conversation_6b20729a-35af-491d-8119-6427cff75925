package com.cosfo.manage.facade.usercenter;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreAccountFacade {

    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;
    @DubboReference
    private MerchantStoreAccountCommandProvider merchantStoreAccountCommandProvider;

    /**
     * 批量获取门店账号信息
     *
     * @param accountIds
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreAccountInfo(List<Long> accountIds) {
        if (CollectionUtil.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountByIds(accountIds);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 获取门店账号信息
     *
     * @param accountId
     * @return
     */
    public MerchantStoreAccountResultResp getMerchantStoreAccountInfo(Long accountId) {
        DubboResponse<MerchantStoreAccountResultResp> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountById(accountId);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }


    /**
     * 获取门店账号信息
     *
     * @param merchantStoreAccountQueryReq
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreList(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(merchantStoreAccountQueryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
    /**
     * 获取门店账号信息
     * 手机号模糊匹配
     * @param merchantStoreAccountQueryReq
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreAccountsWithFuzzy(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountsWithFuzzy(merchantStoreAccountQueryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 创建门店账号信息
     *
     * @param merchantStoreAccountCommandReq
     * @return
     */
    public Long createMerchantStoreAccount(MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        DubboResponse<Long> response = merchantStoreAccountCommandProvider.create(SystemOriginEnum.COSFO_MANAGE, merchantStoreAccountCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"创建门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 批量更新账户状态
     *
     * @param idList
     * @param status
     * @return
     */
    public Boolean updateStatusBatch(List<Long> idList, Integer status) {
        DubboResponse<Boolean> response = merchantStoreAccountCommandProvider.updateStatusBatch(SystemOriginEnum.COSFO_MANAGE, idList, status);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"批量更新账户状态失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 删除门店账号信息
     *
     * @param merchantStoreAccountCommandReq
     * @return
     */
    public Boolean removeMerchantStoreAccount(MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        DubboResponse<Boolean> response = merchantStoreAccountCommandProvider.remove(SystemOriginEnum.COSFO_MANAGE, merchantStoreAccountCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"删除门店账号信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}
