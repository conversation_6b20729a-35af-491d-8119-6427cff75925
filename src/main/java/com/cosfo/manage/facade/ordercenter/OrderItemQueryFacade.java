package com.cosfo.manage.facade.ordercenter;

import com.cosfo.manage.order.convert.OrderConvert;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemQueryFacade {

    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;

    /**
     * 根据 orderItemId 获取订单明细
     *
     * @param orderItemId
     * @return
     */
    public OrderItemResp queryById(Long orderItemId) {
        DubboResponse<OrderItemResp> response = orderItemQueryProvider.queryById(orderItemId);
        if (!response.isSuccess()) {
            log.error("根据 orderItemId 获取订单明细失败, orderItemId: {}, response: {}", orderItemId, response);
            throw new ProviderException("获取订单明细失败");
        }
        return response.getData();
    }

    /**
     * 根据 orderItemId 批量查询
     *
     * @param orderItemIds
     * @return
     */
    public List<OrderItemResp> queryByIds(List<Long> orderItemIds) {
        DubboResponse<List<OrderItemResp>> response = orderItemQueryProvider.queryByIds(orderItemIds);
        if (!response.isSuccess()) {
            log.error("根据 orderItemId 批量查询失败, orderItemIds: {}, response: {}", orderItemIds, response);
            throw new ProviderException("批量查询订单明细失败");
        }
        return response.getData();
    }

    /**
     * 查询订单明细项以及快照
     *
     * @param orderItemId
     * @return
     */
    public OrderItemAndSnapshotResp queryDetailById(Long orderItemId) {
        DubboResponse<OrderItemAndSnapshotResp> response = orderItemQueryProvider.queryDetailById(orderItemId);
        if (!response.isSuccess()) {
            log.error("查询订单明细项以及快照失败, orderItemId: {}, response: {}", orderItemId, response);
            throw new ProviderException("查询订单明细项以及快照失败");
        }
        return response.getData();
    }

    /**
     * 根据订单id获取订单明细列表,包含快照信息
     *
     * @param orderId
     * @return
     */
    public List<OrderItemAndSnapshotResp> queryByOrderId(Long orderId) {
        DubboResponse<List<OrderItemAndSnapshotResp>> response = orderItemQueryProvider.queryByOrderId(orderId);
        if (!response.isSuccess()) {
            log.error("根据订单id获取订单明细列表,包含快照信息失败, orderId: {}, response: {}", orderId, response);
            throw new ProviderException("根据订单id获取订单明细列表,包含快照信息失败");
        }
        return response.getData();
    }

    /**
     * 查询单个订单的订单项信息，包含快照信息
     *
     * @param orderId
     * @return
     */
    public List<OrderItemVO> queryOrderItemByOrderId(Long orderId) {
        List<OrderItemAndSnapshotResp> itemAndSnapshotResps = queryByOrderId(orderId);
        return OrderConvert.INSTANCE.convertOrderItemResp2VOs(itemAndSnapshotResps);
    }

    /**
     * 根据订单id获取订单明细列表
     *
     * @param orderId
     * @return
     */
    public List<OrderItemResp> queryOrderItemList(Long orderId) {
        DubboResponse<List<OrderItemResp>> response = orderItemQueryProvider.queryOrderItemList(orderId);
        if (!response.isSuccess()) {
            log.error("根据订单id获取订单明细列表失败, orderId: {}, response: {}", orderId, response);
            throw new ProviderException("根据订单id获取订单明细列表失败");
        }
        return response.getData();
    }

    /**
     * 根据条件获取订单明细和快照信息
     *
     * @param orderItemQueryReq
     * @return
     */
    public List<OrderItemAndSnapshotResp> queryOrderItemList(OrderItemQueryReq orderItemQueryReq) {
        DubboResponse<List<OrderItemAndSnapshotResp>> response = orderItemQueryProvider.queryOrderItemList(orderItemQueryReq);
        if (!response.isSuccess()) {
            log.error("根据条件获取订单明细和快照信息失败, orderItemQueryReq: {}, response: {}", orderItemQueryReq, response);
            throw new ProviderException("根据条件获取订单明细和快照信息失败");
        }
        return response.getData();
    }

    public List<OrderItemVO> queryOrderItemListVO(OrderItemQueryReq req) {
        List<OrderItemAndSnapshotResp> result = queryOrderItemList(req);
        return OrderConvert.INSTANCE.convertOrderItemResp2VOs(result);
    }
}
