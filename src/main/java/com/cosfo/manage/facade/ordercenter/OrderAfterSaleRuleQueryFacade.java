package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleRuleQueryFacade {

    @DubboReference
    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;

    /**
     * 查询租户的售后规则
     * @param tenantId
     * @return
     */
    public List<OrderAfterSaleRuleResp> queryByTenantId(Long tenantId) {
        DubboResponse<List<OrderAfterSaleRuleResp>> response = orderAfterSaleRuleQueryProvider.queryByTenantId(tenantId);
        if (!response.isSuccess()) {
            log.error("查询租户的售后规则失败, tenantId:{}", tenantId);
            throw new ProviderException("查询租户的售后规则失败");
        }
        return response.getData();
    }
}
