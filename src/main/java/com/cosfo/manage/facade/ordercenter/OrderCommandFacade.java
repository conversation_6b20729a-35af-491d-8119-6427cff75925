package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderPresaleSetDeliveryDateReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderCommandFacade {

    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    public Boolean setDeliveryDatePresaleOrder(OrderPresaleSetDeliveryDateReq req) {
        DubboResponse<Boolean> dubboResponse = orderCommandProvider.setDeliveryDatePresaleOrder(req);
        return RpcResultUtil.handle(dubboResponse);
    }
}
