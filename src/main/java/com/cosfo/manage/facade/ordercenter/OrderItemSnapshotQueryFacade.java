package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemSnapshotQueryFacade {
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;

    /**
     * 根据 orderItemId 获取订单明细快照
     *
     * @param orderItemId
     * @return
     */
    public OrderItemSnapshotResp queryByOrderItemId(Long orderItemId) {
        DubboResponse<OrderItemSnapshotResp> response = orderItemSnapshotQueryProvider.queryByOrderItemId(orderItemId);
        if (!response.isSuccess()) {
            log.error("根据 orderItemId 获取订单明细快照失败, orderItemId: {}, response: {}", orderItemId, response);
            throw new ProviderException("获取订单明细快照失败");
        }
        return response.getData();
    }

    /**
     * 根据订单明细id批量查询快照
     *
     * @param orderItemIds
     * @return
     */
    public List<OrderItemSnapshotResp> queryByOrderItemIds(List<Long> orderItemIds) {
        DubboResponse<List<OrderItemSnapshotResp>> response = orderItemSnapshotQueryProvider.queryByOrderItemIds(orderItemIds);
        if (!response.isSuccess()) {
            log.error("根据订单明细id批量查询快照失败, orderItemIds: {}, response: {}", orderItemIds, response);
            throw new ProviderException("查询快照失败");
        }
        return response.getData();
    }

    /**
     * 根据条件查询订单明细快照
     *
     * @param queryReq
     * @return
     */
    public List<OrderItemSnapshotResp> queryList(OrderItemSnapshotQueryReq queryReq) {
        DubboResponse<List<OrderItemSnapshotResp>> response = orderItemSnapshotQueryProvider.queryList(queryReq);
        if (!response.isSuccess()) {
            log.error("根据条件查询订单明细快照失败, queryReq: {}, response: {}", queryReq, response);
            throw new ProviderException("查询快照失败");
        }
        return response.getData();
    }
}
