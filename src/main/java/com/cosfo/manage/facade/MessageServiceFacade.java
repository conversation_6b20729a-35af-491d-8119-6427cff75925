package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.context.StoreAccountTypeEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.msg.model.req.MsgNoticeEditDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeQueryDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeStoreQueryDTO;
import com.cosfo.manage.msg.model.req.ReadOrSupportLogQeryDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeDetailVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeListVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeMerchantStoreVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeStoreAccountVO;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.provider.MessageSendProviderV2;
import com.cosfo.message.client.provider.NoticeProvider;
import com.cosfo.message.client.req.*;
import com.cosfo.message.client.resp.MsgNoticeReceiverResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.client.resp.NoticeDetailResultResp;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.resp.NoticeList4ManagerResultResp;
import com.cosfo.message.client.resp.NoticeReadLogResultResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Slf4j
@Component
public class MessageServiceFacade {
//    @Autowired
//    private MerchantStoreMapper merchantStoreMapper;
//    @Autowired
//    private MerchantStoreAccountMapper merchantStoreAccountMapper;
    @DubboReference
    private NoticeProvider noticeProvider;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private MerchantStoreService storeService;
    @DubboReference
    private MessageSendProviderV2 messageSendProviderV2;

    public PageInfo<MsgNoticeListVO> pageNotic(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO) {

        NoticeQuery4ManagerReq noticeQuery4ManagerReq = new NoticeQuery4ManagerReq();
        noticeQuery4ManagerReq.setTenantId(contextInfoDTO.getTenantId());
        noticeQuery4ManagerReq.setId(req.getId());
        noticeQuery4ManagerReq.setPushStatus(req.getPushStatus());
        noticeQuery4ManagerReq.setTitle(req.getTitle());
        noticeQuery4ManagerReq.setPushTimeBegin(req.getPushTimeBegin());
        noticeQuery4ManagerReq.setPushTimeEnd(req.getPushTimeEnd());
        noticeQuery4ManagerReq.setSortBy(0);
        noticeQuery4ManagerReq.setSortKey("id");
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(req.getPageNum());
        pageQueryReq.setPageSize(req.getPageSize());
        log.error("查询message.pageNotic：{}" , JSONObject.toJSONString(noticeQuery4ManagerReq));
        DubboResponse<PageResp<NoticeList4ManagerResultResp>> resp = noticeProvider.page4Manager(noticeQuery4ManagerReq, pageQueryReq);
        if (!resp.isSuccess()) {
            log.error("查询message.pageNotic：req={},resp={}" , JSONObject.toJSONString(noticeQuery4ManagerReq), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
        log.error("查询message.pageNotic：req={},resp={}" , JSONObject.toJSONString(noticeQuery4ManagerReq), JSONObject.toJSONString(resp));
        PageResp<NoticeList4ManagerResultResp> data = resp.getData();
        List<NoticeList4ManagerResultResp> list = data.getData();
        if(CollectionUtil.isEmpty(list)) {
            return PageInfoHelper.createPageInfo(Collections.emptyList(),req.getPageSize());
        }
        Set<Long> uIds = new HashSet<>();
        Set<Long> createUids = list.stream().filter(e->ObjectUtil.isNotNull(e.getCreateUid())).map(NoticeList4ManagerResultResp::getCreateUid).collect(Collectors.toSet());
        Set<Long> editUids = list.stream().filter(e -> ObjectUtil.isNotNull(e.getEditUid())).map(NoticeList4ManagerResultResp::getEditUid).collect(Collectors.toSet());
        Set<Long> pushUids = list.stream().filter(e -> ObjectUtil.isNotNull(e.getPushUid())).map(NoticeList4ManagerResultResp::getPushUid).collect(Collectors.toSet());

        if(CollectionUtil.isNotEmpty(createUids)){
            uIds.addAll(createUids);
        }
        if(CollectionUtil.isNotEmpty(editUids)){
            uIds.addAll(editUids);
        }
        if(CollectionUtil.isNotEmpty(pushUids)){
            uIds.addAll(pushUids);
        }
        List<TenantResultResp> tenants = tenantService.listByIds(uIds);
        Map<Long, String> tenantMap = tenants.stream().collect(Collectors.toMap(TenantResultResp::getId, TenantResultResp::getTenantName));

        PageInfo pageInfo = PageInfoHelper.createPageInfo(list, req.getPageSize());
        List<MsgNoticeListVO> result = list.stream().map(e -> {
            MsgNoticeListVO msgNoticeListVO = new MsgNoticeListVO();
            BeanUtils.copyProperties(e, msgNoticeListVO);
            Long createUid = e.getCreateUid();
            Long editUid = e.getEditUid();
            Long pushUid = e.getPushUid();
            msgNoticeListVO.setUpdateTime(e.getUpdateTime());
            msgNoticeListVO.setCreateUserName(tenantMap.get(createUid));
            msgNoticeListVO.setEditUserName(tenantMap.get(editUid));
            msgNoticeListVO.setPushUserName(tenantMap.get(pushUid));
            return msgNoticeListVO;
        }).collect(Collectors.toList());
        pageInfo.setList(result);
        pageInfo.setIsLastPage(data.getIsLastPage());
        pageInfo.setTotal(data.getTotal());
        return pageInfo;
    }


    public MsgNoticeDetailVO detail(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO) {
        MsgReadLogReq msgReadLogReq = new MsgReadLogReq();
        msgReadLogReq.setNoticeId(req.getId());
        log.error("查询message.pageNotic：{}" , JSONObject.toJSONString(msgReadLogReq));
        DubboResponse<NoticeDetailResultResp> resp = noticeProvider.getNoticeById(msgReadLogReq, Boolean.FALSE);
        if (!resp.isSuccess()) {
            log.error("查询message.detail：req={},resp={}" , JSONObject.toJSONString(msgReadLogReq), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
        log.error("查询message.detail：req={},resp={}" , JSONObject.toJSONString(msgReadLogReq), JSONObject.toJSONString(resp));
        MsgNoticeDetailVO msgDetailVO = new MsgNoticeDetailVO();
        NoticeDetailResultResp data = resp.getData();
        if(ObjectUtil.isNotNull(data)) {
            BeanUtils.copyProperties(data, msgDetailVO);
            List<Long> receiverIds = data.getReceiverIds();
            if(CollectionUtil.isNotEmpty(receiverIds)){
                if(!receiverIds.contains(0L)){
                    List<MerchantStoreVO> groups = storeService.listMerchantStoreVO(data.getReceiverIds(),contextInfoDTO.getTenantId());
                    msgDetailVO.setStoreList(groups);
                }
            }
        }
        return msgDetailVO;
    }

    public Long edit(MsgNoticeEditDTO dto, LoginContextInfoDTO contextInfoDTO) {
        MsgNoticeEditReq req = new MsgNoticeEditReq();
        req.setId(dto.getId());
        req.setTitle(dto.getTitle());
        req.setContent(dto.getContent());
        req.setPushTime(dto.getPushTime());
        req.setStoreIds(dto.getStoreIds());
        if(CollectionUtil.isNotEmpty(dto.getStoreIds()) && dto.getStoreIds().contains(0L)){
//            MerchantStoreQueryDTO storeQueryDTO = new MerchantStoreQueryDTO();
//            storeQueryDTO.setTenantId(contextInfoDTO.getTenantId());
//            List<MerchantStore> merchantStores = merchantStoreMapper.listStore(storeQueryDTO);
            List<MerchantStore> merchantStores = storeService.selectByTenantId(contextInfoDTO.getTenantId());
            List<Long> ids = new ArrayList<>();
            ids.add(0L);
            if(CollectionUtil.isNotEmpty(merchantStores)){
                ids.addAll(merchantStores.stream().map(MerchantStore::getId).collect(Collectors.toList()));
            }
            req.setStoreIds(ids);
        }
        req.setPushType(dto.getPushType());
        req.setSupportSwitch(dto.getSupportSwitch());
        req.setPushStatus(dto.getPushStatus());
        req.setUId(contextInfoDTO.getTenantId());
        req.setTenantId(contextInfoDTO.getTenantId());
        req.setReceiverType(0);
        log.error("编辑message.edit：{}" , JSONObject.toJSONString(req));
        DubboResponse<Long> resp = noticeProvider.editNotic(req);
        if (!resp.isSuccess()) {
            log.error("编辑message.edit：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
        log.error("编辑message.edit：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
        return resp.getData();
    }

    public void delete(Long id, LoginContextInfoDTO contextInfoDTO) {
        DubboResponse<Void> resp = noticeProvider.deleteNotic(id);
        if (!resp.isSuccess()) {
            throw new DefaultServiceException(resp.getMsg());
        }
    }

    public PageInfo<MsgNoticeMerchantStoreVO> pageStore(MsgNoticeStoreQueryDTO req, LoginContextInfoDTO contextInfoDTO) {
        MerchantStoreQueryDTO storeQueryDTO = new MerchantStoreQueryDTO();
        storeQueryDTO.setTenantId(contextInfoDTO.getTenantId());
        storeQueryDTO.setStoreName(req.getStoreName());
        storeQueryDTO.setId(req.getId());
        storeQueryDTO.setType(req.getType());
        storeQueryDTO.setStatus(req.getStatus());
        storeQueryDTO.setProvince(req.getProvince());
        storeQueryDTO.setCity(req.getCity());
        storeQueryDTO.setArea(req.getArea());
        storeQueryDTO.setGroupId(req.getStoreGroupId());
        storeQueryDTO.setStoreNo(req.getStoreNo());
        List<Long> storeIds = null;
        if(ObjectUtil.isNotNull(req.getStoreNo()) || ObjectUtil.isNotNull(req.getStoreName()) || ObjectUtil.isNotNull(req.getType()) || ObjectUtil.isNotNull(req.getStatus()) || ObjectUtil.isNotNull(req.getProvince()) || ObjectUtil.isNotNull(req.getCity()) || ObjectUtil.isNotNull(req.getArea()) || ObjectUtil.isNotNull(req.getStoreGroupId())){
            List<MerchantStore> merchantStores = storeService.listStore(storeQueryDTO, contextInfoDTO);
            storeIds = merchantStores.stream().map(MerchantStore::getId).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(merchantStores)){
                return PageInfoHelper.createPageInfo(Collections.emptyList(), req.getPageSize());
            }
        }
        MsgNoticeReceiverQueryReq queryReq = new MsgNoticeReceiverQueryReq();
        BeanUtils.copyProperties(req, queryReq);
        queryReq.setReceiverIds(storeIds);
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(req.getPageIndex());
        pageQueryReq.setPageSize(req.getPageSize());
        log.error("查询message.pageStore：{}" , JSONObject.toJSONString(queryReq));
        DubboResponse<PageResp<MsgNoticeReceiverResp>> resp = noticeProvider.pageReceiver(queryReq, pageQueryReq);
        if (!resp.isSuccess()) {
            log.error("查询message.pageStore：req={},resp={}" , JSONObject.toJSONString(queryReq), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
        log.error("查询message.pageStore：req={},resp={}" , JSONObject.toJSONString(queryReq), JSONObject.toJSONString(resp));
        PageResp<MsgNoticeReceiverResp> data = resp.getData();
        List<MsgNoticeReceiverResp> list = data.getData();
        List<Long> receivers = list.stream().map(MsgNoticeReceiverResp::getId).collect(Collectors.toList());
        Map<Long, MerchantStoreVO> storeVOMap;
        if(CollectionUtil.isNotEmpty(receivers)){
            List<MerchantStoreVO> groups = storeService.listMerchantStoreVO(receivers,contextInfoDTO.getTenantId());
            storeVOMap = groups.stream().collect(Collectors.toMap(MerchantStoreVO::getId, Function.identity()));
        } else {
            storeVOMap = Collections.emptyMap();
        }

        PageInfo pageInfo = PageInfoHelper.createPageInfo(list, req.getPageSize());
        List<MsgNoticeMerchantStoreVO> result = list.stream().map(e -> {
            MsgNoticeMerchantStoreVO vo = new MsgNoticeMerchantStoreVO();
            MerchantStoreVO merchantStoreVO = storeVOMap.get(e.getId());
            BeanUtils.copyProperties(merchantStoreVO, vo);
            vo.setReadAmount(e.getReadAmount());
            vo.setSupportAmount(e.getSupportAmount());
            vo.setUpdateTime(e.getFirstReadTime());
            return vo;
        }).collect(Collectors.toList());
        pageInfo.setList(result);
        pageInfo.setIsLastPage(data.getIsLastPage());
        pageInfo.setTotal(data.getTotal());
        return pageInfo;

    }

    public PageInfo<MsgNoticeStoreAccountVO> pageReadOrSupportLog(ReadOrSupportLogQeryDTO dto, LoginContextInfoDTO contextInfoDTO) {
        ReadOrSupportLogQueryReq req = new ReadOrSupportLogQueryReq();
        BeanUtils.copyProperties(dto, req);
        req.setTenantId(contextInfoDTO.getTenantId());
        req.setStoreId(dto.getStoreId());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(dto.getPageIndex());
        pageQueryReq.setPageSize(dto.getPageSize());
        log.error("查询message.pageReadOrSupportLog：{}" , JSONObject.toJSONString(req));
        DubboResponse<PageResp<NoticeReadLogResultResp>> resp = noticeProvider.pageReadOrSupportLog(req, pageQueryReq);
        if (!resp.isSuccess()) {
            log.error("查询message.pageReadOrSupportLog：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
        log.error("查询message：pageReadOrSupportLog={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
        PageResp<NoticeReadLogResultResp> data = resp.getData();
        List<NoticeReadLogResultResp> list = data.getData();
        if(CollectionUtil.isEmpty(list)){
            return PageInfoHelper.createPageInfo(Collections.emptyList(), dto.getPageSize());
        }
        PageInfo pageInfo = PageInfoHelper.createPageInfo(list, dto.getPageSize());
        List<MsgNoticeStoreAccountVO> result = list.stream().map(e -> {
            MsgNoticeStoreAccountVO vo = new MsgNoticeStoreAccountVO();
            vo.setId(e.getUId());
            vo.setTenantId(e.getTenantId());
            vo.setRoleTypeDescription(StoreAccountTypeEnum.of(e.getRoleType()).getDesc());
            vo.setPhone(e.getPhone());
            vo.setAccountName(e.getUName());
            vo.setTime(e.getTime());
            return vo;
        }).collect(Collectors.toList());
        pageInfo.setList(result);
        pageInfo.setIsLastPage(data.getIsLastPage());
        pageInfo.setTotal(data.getTotal());
        return pageInfo;
    }

    /**
     * 批量发送消息
     *
     * @param tenantId
     * @param channelTypeEnum
     * @param channelCode
     * @param sender
     * @param receiverList
     * @param mesg
     * @param systemOriginEnum
     * @return
     */
    public List<MsgSendLogResp> batchSendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String channelCode, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum) {
        DubboResponse<List<MsgSendLogResp>> response = messageSendProviderV2.batchSendMessageByThridUid(tenantId, channelTypeEnum, channelCode, sender, receiverList, mesg, systemOriginEnum);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 批量发送微信公众号消息
     *
     * @param tenantId
     * @param receiverList
     * @param mesg
     * @return
     */
    public List<MsgSendLogResp> batchSendFTWechatOaMessage(Long tenantId, List<String> receiverList, MessageBodyReq mesg) {
        if (CollectionUtil.isEmpty(receiverList)) {
            return Collections.emptyList();
        }

        List<MsgSendLogResp> resp = Lists.newArrayList();
        List<List<String>> openIds = Lists.partition(receiverList, 50);
        for (List<String> receiverIds : openIds) {
            resp.addAll(batchSendMessageByThridUid(tenantId, ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, WxOfficialAccountsChannelEnum.FTGYL.channelCode, null, receiverIds, mesg, null));
        }
        return resp;
    }

}
