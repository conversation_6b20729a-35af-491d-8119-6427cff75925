package com.cosfo.manage.facade;

import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import net.summerfarm.goods.client.provider.GoodsCodeProvider;
import net.summerfarm.goods.client.req.GoodsCodeInputReq;
import net.summerfarm.goods.client.resp.GoodsCodeResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class ProductCommandFacade {

    @DubboReference
    private GoodsCodeProvider goodsCodeProvider;

    public String takeGoodsCode(Long categoryId){
        GoodsCodeInputReq req = new GoodsCodeInputReq ();
        req.setCategoryId (categoryId);
        DubboResponse<GoodsCodeResp> dubboResponse = goodsCodeProvider.takeGoodsCode (req);
        FacadeExceptionUtil.executeFaceException (dubboResponse, null);
        return dubboResponse.getData ().getSku ();
    }
}
