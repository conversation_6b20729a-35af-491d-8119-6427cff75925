package com.cosfo.manage.facade.stock;

import com.cosfo.mall.client.stock.provider.StockQueryProvider;
import com.cosfo.mall.client.stock.req.StockQueryReq;
import com.cosfo.mall.client.stock.resp.StockInfoResp;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.facade.convert.StockFacadeConvert;
import com.cosfo.manage.facade.dto.StockInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/2/29 13:56
 * @Description:
 */
@Service
@Slf4j
public class StockFacade {
    @DubboReference
    private StockQueryProvider stockQueryProvider;

    /**
     * 查询库存
     * @param stockQueryReq
     * @return
     */
    public Map<Long, StockInfoDTO> queryStock(StockQueryReq stockQueryReq) {
        DubboResponse<Map<Long, StockInfoResp>> dubboResponse = stockQueryProvider.queryStock(stockQueryReq);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        Map<Long, StockInfoResp> stockInfoRespMap = dubboResponse.getData();
        if (Objects.isNull(stockInfoRespMap)) {
            return Collections.emptyMap();
        }
        return stockInfoRespMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> StockFacadeConvert.convert2Dto(entry.getValue())
            ));
    }
}
