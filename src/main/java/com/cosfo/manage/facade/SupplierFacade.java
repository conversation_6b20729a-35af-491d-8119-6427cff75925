package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.facade.convert.Convert;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.input.SupplierQueryInput;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.SupplierProvider;
import net.summerfarm.pms.client.provider.supplier.SupplierSummaryQueryProvider;
import net.summerfarm.pms.client.provider.supplier.req.SupplierSummaryQueryReq;
import net.summerfarm.pms.client.provider.supplier.resp.SupplierSummaryResp;
import net.summerfarm.pms.client.req.SupplierReq;
import net.summerfarm.pms.client.resp.SupplierDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 供应商Facade
 * @author: George
 * @date: 2023-05-12
 **/
@Component
@Slf4j
public class SupplierFacade {

    @DubboReference
    private SupplierProvider supplierProvider;

    @DubboReference
    private SupplierSummaryQueryProvider supplierSummaryQueryProvider;


    /**
     * 查询临期供应商数量
     *
     * @param tenantId
     * @param dayLimit
     * @return
     */
    public Integer countExpireSupplierCount(Long tenantId,Integer dayLimit){
        try {
            SupplierSummaryQueryReq supplierSummaryQueryReq = new SupplierSummaryQueryReq();
            supplierSummaryQueryReq.setTenantId(tenantId);
            supplierSummaryQueryReq.setQueryExpireLimit(dayLimit);

            DubboResponse<SupplierSummaryResp> supplierSummaryRespDubboResponse = supplierSummaryQueryProvider.querySupplierSummary(supplierSummaryQueryReq);
            return supplierSummaryRespDubboResponse.getData().getExpireSupplierCount();
        } catch (Exception e) {
            log.error("查询临期供应商数量失败",e);
            return 0;
        }
    }


    /**
     * 批量查询供应商信息
     * @param supplierQueryInput
     * @return
     */
    public List<SupplierInfoDTO> batchQuerySupplier(SupplierQueryInput supplierQueryInput) {
        SupplierReq supplierReq = new SupplierReq();
        supplierReq.setPageIndex(supplierQueryInput.getPageIndex());
        supplierReq.setPageSize(supplierQueryInput.getPageSize());
        supplierReq.setTenantId(supplierQueryInput.getTenantId());
        supplierReq.setSupplierIdList((List<Integer>)ConvertUtils.convert(supplierQueryInput.getSupplierIds(), List.class));
        DubboResponse<PageInfo<SupplierDTO>> response = supplierProvider.querySupplierPage(supplierReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        PageInfo<SupplierDTO> supplierDTOPageInfo = response.getData();
        if (supplierDTOPageInfo == null || CollectionUtils.isEmpty(supplierDTOPageInfo.getList())) {
            return Collections.emptyList();
        }
        List<SupplierDTO> list = supplierDTOPageInfo.getList();
        return list.stream().map(el -> Convert.convertToSupplierInfoDTO(el)).collect(Collectors.toList());
    }

    public Map<String, Long> getNameIdMapByTenantId(Long tenantId) {
        SupplierReq supplierReq = new SupplierReq();
        supplierReq.setPageIndex(1);
        supplierReq.setPageSize(1000);
        supplierReq.setTenantId(tenantId);
        DubboResponse<PageInfo<SupplierDTO>> response = supplierProvider.querySupplierPage(supplierReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        PageInfo<SupplierDTO> supplierDTOPageInfo = response.getData();
        if (supplierDTOPageInfo == null || CollectionUtils.isEmpty(supplierDTOPageInfo.getList())) {
            return Collections.emptyMap ();
        }
        List<SupplierDTO> list = supplierDTOPageInfo.getList();

        Map<String, Long> result = new HashMap<> ();

        Map<String, Long> collect = list.stream ().collect (Collectors.toMap (
                SupplierDTO::getSupplierName,
                x -> Long.valueOf (x.getSupplierId ()),
                (existing, replacement) -> existing
        ));
        if (CollectionUtil.isNotEmpty (collect)){
            result.putAll (collect);
        }
        result.put ("杭州鲜沐科技有限公司",1L);

        return result;
    }

}
