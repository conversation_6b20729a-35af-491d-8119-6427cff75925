package com.cosfo.manage.facade;

import com.google.common.collect.Iterators;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/18
 */
@Slf4j
@Component
public class WarehouseStorageFenceQueryFacade {

    @DubboReference
    private WarehouseStorageFenceQueryProvider warehouseStorageFenceQueryProvider;

    /**
     * 查询仓库覆盖区域
     *
     * @param warehouseStorageListQueryReq
     * @return
     */
    public List<WarehouseSkuFenceResp> queryWarehouseSkuFence(List<WarehouseSkuFenceReq> warehouseStorageListQueryReq) {
        if (CollectionUtils.isEmpty(warehouseStorageListQueryReq)) {
            return Collections.emptyList();
        }
        // 此接口一次最多传入20个参数:
        List<WarehouseSkuFenceResp> warehouseSkuFenceRespList = new ArrayList<>(warehouseStorageListQueryReq.size());
        Iterators.partition(warehouseStorageListQueryReq.iterator(), 20).forEachRemaining(warehouseStorageQuerySubList -> {
            DubboResponse<List<WarehouseSkuFenceResp>> response = warehouseStorageFenceQueryProvider.queryWarehouseSkuFence(warehouseStorageQuerySubList);
            if (!response.isSuccess()) {
                throw new ProviderException("查询仓库覆盖区域信息失败, 失败原因：" + response.getMsg());
            }
            warehouseSkuFenceRespList.addAll(response.getData());
        });
        return warehouseSkuFenceRespList;
    }
    /**
     * 根据查询条件获取配送规则
     *
     * @param warehouseStorageListQueryReq
     * @return
     */
//    public List<WarehouseStorageFenceRuleResp> queryWarehouseStorageFence(WarehouseStorageFenceQueryReq warehouseStorageListQueryReq){
//        DubboResponse<List<WarehouseStorageFenceRuleResp>> response = warehouseStorageFenceQueryProvider.queryWarehouseStorageFence(warehouseStorageListQueryReq);
//        if(!response.isSuccess()){
//            throw new ProviderException("根据查询条件获取配送规则失败, 失败原因：" + response.getMsg());
//        }
//
//        return response.getData();
//    }
}
