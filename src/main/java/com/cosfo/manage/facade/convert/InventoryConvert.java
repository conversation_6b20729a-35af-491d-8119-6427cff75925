package com.cosfo.manage.facade.convert;

import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import net.summerfarm.wms.saleinventory.dto.req.PageQuerySkuQuantityChangeRecordReq;

/**
 * @Author: fansongsong
 * @Date: 2023-09-12
 * @Description:
 */
public class InventoryConvert {

    public static PageQuerySkuQuantityChangeRecordReq productStockChangeRecord2ChangeRecordReq(ProductStockChangeRecordQueryInput input) {
        if (input == null) {
            return null;
        }
        PageQuerySkuQuantityChangeRecordReq pageQuerySkuQuantityChangeRecordReq = new PageQuerySkuQuantityChangeRecordReq();
        pageQuerySkuQuantityChangeRecordReq.setPageIndex(input.getPageIndex());
        pageQuerySkuQuantityChangeRecordReq.setPageSize(input.getPageSize());
        pageQuerySkuQuantityChangeRecordReq.setSkuId(input.getSkuId());
        pageQuerySkuQuantityChangeRecordReq.setWarehouseNo(input.getWarehouseNo());
        pageQuerySkuQuantityChangeRecordReq.setStockTypeList(input.getStockTypeList());
        pageQuerySkuQuantityChangeRecordReq.setPdName(input.getPdName());
        pageQuerySkuQuantityChangeRecordReq.setChangeTypeNameList(input.getChangeTypeNameList());
        pageQuerySkuQuantityChangeRecordReq.setStartTime(input.getStartTime());
        pageQuerySkuQuantityChangeRecordReq.setEndTime(input.getEndTime());
        pageQuerySkuQuantityChangeRecordReq.setPermissionSkuIdList(input.getPermissionSkuIdList());
        pageQuerySkuQuantityChangeRecordReq.setTenantId(input.getTenantId());
        return pageQuerySkuQuantityChangeRecordReq;
    }
}
