package com.cosfo.manage.facade.convert;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.dto.*;
import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import net.summerfarm.manage.client.saas.req.ApplyAgentSkuReq;
import net.summerfarm.manage.client.saas.req.PageQuerySkuQuantityChangeRecordReq;
import net.summerfarm.manage.client.saas.resp.CategoryResp;
import net.summerfarm.manage.client.saas.resp.SummerfarmProductInfoResp;
import net.summerfarm.pms.client.resp.SupplierDTO;
import net.xianmu.authentication.client.dto.ShiroUser;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/6 11:22
 */
public class Convert {

    public static SummerfarmProductInfoDTO summerfarmProductInfoResp2Dto(SummerfarmProductInfoResp resp) {

        if (resp == null) {
            return null;
        }
        SummerfarmProductInfoDTO summerfarmProductInfoDTO = new SummerfarmProductInfoDTO();
        summerfarmProductInfoDTO.setSkuId(resp.getSkuId());
        summerfarmProductInfoDTO.setAfterSaleUnit(resp.getAfterSaleUnit());
        summerfarmProductInfoDTO.setMaxAfterSaleAmount(resp.getMaxAfterSaleAmount());
        return summerfarmProductInfoDTO;
    }

    public static PageQuerySkuQuantityChangeRecordReq productStockChangeRecord2Req(ProductStockChangeRecordQueryInput input) {

        if (input == null) {
            return null;
        }
        PageQuerySkuQuantityChangeRecordReq pageQuerySkuQuantityChangeRecordReq = new PageQuerySkuQuantityChangeRecordReq();
        pageQuerySkuQuantityChangeRecordReq.setPageIndex(input.getPageIndex());
        pageQuerySkuQuantityChangeRecordReq.setPageSize(input.getPageSize());
        pageQuerySkuQuantityChangeRecordReq.setSkuId(input.getSkuId());
        pageQuerySkuQuantityChangeRecordReq.setWarehouseNo(input.getWarehouseNo());
        pageQuerySkuQuantityChangeRecordReq.setStockTypeList(input.getStockTypeList());
        pageQuerySkuQuantityChangeRecordReq.setPdName(input.getPdName());
        pageQuerySkuQuantityChangeRecordReq.setChangeTypeNameList(input.getChangeTypeNameList());
        pageQuerySkuQuantityChangeRecordReq.setStartTime(input.getStartTime());
        pageQuerySkuQuantityChangeRecordReq.setEndTime(input.getEndTime());
        pageQuerySkuQuantityChangeRecordReq.setPermissionSkuIdList(input.getPermissionSkuIdList());
        pageQuerySkuQuantityChangeRecordReq.setTenantId(input.getTenantId());
        return pageQuerySkuQuantityChangeRecordReq;
    }

    public static CategoryDTO categoryResp2DTO(CategoryResp categoryResp) {

        if (categoryResp == null) {
            return null;
        }
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setId(categoryResp.getId());
        categoryDTO.setParentId(categoryResp.getParentId());
        categoryDTO.setCategory(categoryResp.getCategory());
        categoryDTO.setTaxRateValue(categoryResp.getTaxRateValue());
        return categoryDTO;
    }

    /**
     * 转化为LoginContextInfoDTO
     *
     * @param shiroUser
     * @return
     */
    public static LoginContextInfoDTO convertToLoginContextInfoDTO(ShiroUser shiroUser){

        if (shiroUser == null) {
            return null;
        }
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(shiroUser.getTenantId());
        loginContextInfoDTO.setPhone(shiroUser.getPhone());
        loginContextInfoDTO.setAuthUserId(shiroUser.getId());
        loginContextInfoDTO.setSystemOrigin(shiroUser.getSystemOrigin());
        return loginContextInfoDTO;
    }

    /**
     * 转化为ApplyAgentSkuReq
     *
     * @param agentSkuDTO
     * @return
     */
    public static ApplyAgentSkuReq convertToApplyAgentSkuReq(ApplyAgentSkuDTO agentSkuDTO){
        if (agentSkuDTO == null) {
            return null;
        }

        ApplyAgentSkuReq applyAgentSkuReq = new ApplyAgentSkuReq();
        applyAgentSkuReq.setSkuId(agentSkuDTO.getSkuId());
        applyAgentSkuReq.setCreateType(agentSkuDTO.getCreateType());
        applyAgentSkuReq.setOperator(agentSkuDTO.getOperator());
        return applyAgentSkuReq;
    }

    /**
     * 转换为supplierInfoDTO
     * @param supplierDTO
     * @return
     */
    public static SupplierInfoDTO convertToSupplierInfoDTO(SupplierDTO supplierDTO) {
        if (supplierDTO == null) {
            return null;
        }
        SupplierInfoDTO supplierInfoDTO = new SupplierInfoDTO();
        supplierInfoDTO.setSupplierId(supplierDTO.getSupplierId().longValue());
        supplierInfoDTO.setSupplierName(supplierDTO.getSupplierName());
        return supplierInfoDTO;
    }
}
