package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.saleinventory.SaleInventoryCommandProvider;
import net.summerfarm.wms.saleinventory.dto.req.*;
import net.summerfarm.wms.saleinventory.dto.res.*;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * WMS 库存
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/10
 */
@Slf4j
@Component
public class SaleInventoryCommandFacade {

    @DubboReference
    private SaleInventoryCommandProvider saleInventoryCommandProvider;

    /**
     * 购物车、预下单库存查询接口
     * @param preDistributionOrderOccupyReqDTO
     * @return
     */
    public PreDistributionOrderOccupyResDTO preDistributionOrderOccupy(PreDistributionOrderOccupyReqDTO preDistributionOrderOccupyReqDTO){
        DubboResponse<PreDistributionOrderOccupyResDTO> response = saleInventoryCommandProvider.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("查询可占用库存接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 下单冻结自营仓库存接口
     *
     * @param orderOccupyBySpecifyWarehouseReqDTO
     * @return
     */
    public OrderOccupyResDTO orderOccupyBySpecifyWarehouseAndSku(OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO){
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCommandProvider.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("调用自营仓库存冻结接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 补发冻结自营仓库存接口
     *
     * @param orderOccupyReqDTO
     * @return
     */
    public OrderOccupyResDTO orderOccupy(OrderOccupyReqDTO orderOccupyReqDTO){
        log.info("orderOccupy orderOccupyReqDTO={}", JSON.toJSONString(orderOccupyReqDTO));
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCommandProvider.orderOccupy(orderOccupyReqDTO);
        log.info("orderOccupy response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("orderOccupy error. response={}", JSON.toJSONString(response));
            throw new ProviderException("调用自营仓[不指定仓库]库存冻结接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 释放自营仓库存接口
     *
     * @param orderReleaseBySpecifySkuReqDTO
     * @return
     */
    public OrderReleaseResDTO orderReleaseBySpecifySku(OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO){
        DubboResponse<OrderReleaseResDTO> response = saleInventoryCommandProvider.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("调用自营仓库存释放接口失败：" + response.getMsg());
        }
        return response.getData();
    }
}
