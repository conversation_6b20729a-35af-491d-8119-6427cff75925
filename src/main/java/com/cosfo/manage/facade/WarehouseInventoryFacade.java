package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.NumberConstant;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.inventory.WarehouseInventoryProvider;
import net.summerfarm.wms.inventory.req.WarehouseSkuInventoryPageReq;
import net.summerfarm.wms.inventory.resp.WarehouseSkuInventoryPageResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description:
 */
@Slf4j
@Component
public class WarehouseInventoryFacade {

    @DubboReference
    private WarehouseInventoryProvider warehouseInventoryProvider;

    public PageInfo<WarehouseSkuInventoryPageResp> pageWarehouseSkuInventory(WarehouseSkuInventoryPageReq req) {
        log.info("warehouseInventoryProvider.pageWarehouseSkuInventory req = {}", JSON.toJSONString(req));
        DubboResponse<PageInfo<WarehouseSkuInventoryPageResp>> response = warehouseInventoryProvider.pageWarehouseSkuInventory(req);
        log.info("warehouseInventoryProvider.pageWarehouseSkuInventory response = {}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException("分页仓库库存失败, 失败原因：" + response.getMsg());
        }
        return response.getData();
    }


    public Map<String, List<WarehouseSkuInventoryPageResp>> queryWarehouseSkuInventory(List<String> skuCodeList, List<Integer> warehouseNoList) {
        if(CollectionUtils.isEmpty(skuCodeList) || CollectionUtils.isEmpty(warehouseNoList)){
            return Collections.emptyMap();
        }
        WarehouseSkuInventoryPageReq req = new WarehouseSkuInventoryPageReq();
        req.setSkuCodeList(skuCodeList);
        req.setWarehouseNo(warehouseNoList);
        req.setPageNum(NumberConstant.ONE);
        req.setPageSize(req.getWarehouseNo().size() * req.getSkuCodeList().size());
        DubboResponse<PageInfo<WarehouseSkuInventoryPageResp>> response = warehouseInventoryProvider.pageWarehouseSkuInventory(req);
        if (!response.isSuccess()) {
            throw new ProviderException("分页仓库库存失败, 失败原因：" + response.getMsg());
        }
        PageInfo<WarehouseSkuInventoryPageResp> pageRespPageInfo = response.getData();
        return pageRespPageInfo.getList().stream().collect(Collectors.groupingBy(WarehouseSkuInventoryPageResp::getSkuCode));
    }

}
