package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.order.model.dto.OrderDeliveryDTO;
import com.cosfo.manage.order.model.dto.OrderDeliveryUpdateDTO;
import com.cosfo.manage.order.model.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.req.FulfillmentDeliveryInfoReq;
import net.summerfarm.ofc.client.req.FulfillmentDeliveryItemReq;
import net.summerfarm.ofc.client.req.StartFulfillmentReq;
import net.summerfarm.ofc.client.req.StartFulfillmentWithoutWarehouseReq;
import net.summerfarm.ofc.client.req.UpdateFulfillmentDeliveryInfoReq;
import net.summerfarm.ofc.client.resp.StartFulfillmentResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-04-11
 * @Description:
 */
@Component
@Slf4j
public class FulfillmentOrderOperateFacade {

    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;

    /**
     * 批量通知接口
     * @param startFulfillmentReq
     */
    public StartFulfillmentResp startFulfillment(StartFulfillmentReq startFulfillmentReq) {
        log.info("FulfillmentOrderOperateProvider.startFulfillment startFulfillmentReq={}", JSON.toJSONString(startFulfillmentReq));
        DubboResponse<StartFulfillmentResp> response = fulfillmentOrderOperateProvider.startFulfillment(startFulfillmentReq);
        log.info("FulfillmentOrderOperateProvider.startFulfillment response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 同步无仓订单的物流信息
     */
    public void startFulfillmentWithoutWarehouse(OrderDeliveryDTO orderDeliveryDTO, String orderNo, Map<Long, Long> orderItemMap, String operator) {
        // 构建入参
        StartFulfillmentWithoutWarehouseReq startFulfillmentWithoutWarehouseReq = new StartFulfillmentWithoutWarehouseReq();
        startFulfillmentWithoutWarehouseReq.setOperator(operator);
        startFulfillmentWithoutWarehouseReq.setOrderNo(orderNo);
        FulfillmentDeliveryInfoReq startFulfillmentDeliveryInfo = new FulfillmentDeliveryInfoReq();
        startFulfillmentDeliveryInfo.setType(orderDeliveryDTO.getDeliveryType());
        startFulfillmentDeliveryInfo.setLogisticsCompany(orderDeliveryDTO.getDeliveryCompany());
        startFulfillmentDeliveryInfo.setLogisticsNo(orderDeliveryDTO.getDeliveryNo());
        startFulfillmentDeliveryInfo.setRemark(orderDeliveryDTO.getRemark());
        startFulfillmentWithoutWarehouseReq.setDeliveryInfo(startFulfillmentDeliveryInfo);

        List<FulfillmentDeliveryItemReq> deliveryItemInfoList = orderDeliveryDTO.getDeliveryDTOList().stream().map(orderItemDeliveryDTO -> {
            FulfillmentDeliveryItemReq startFulfillmentDeliveryItemInfo = new FulfillmentDeliveryItemReq();
            Long itemId = orderItemMap.get(orderItemDeliveryDTO.getOrderItemId());
            startFulfillmentDeliveryItemInfo.setItemId(itemId.toString());
            startFulfillmentDeliveryItemInfo.setQuantity(orderItemDeliveryDTO.getQuantity());
            return startFulfillmentDeliveryItemInfo;
        }).collect(Collectors.toList());
        startFulfillmentWithoutWarehouseReq.setDeliveryItemInfoList(deliveryItemInfoList);

        log.info("FulfillmentOrderOperateProvider.startFulfillmentWithoutWarehouse startFulfillmentWithoutWarehouse={}", JSON.toJSONString(startFulfillmentWithoutWarehouseReq));
        DubboResponse<Void> response = fulfillmentOrderOperateProvider.startFulfillmentWithoutWarehouse(startFulfillmentWithoutWarehouseReq);
        log.info("FulfillmentOrderOperateProvider.startFulfillmentWithoutWarehouse response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
    }

    /**
     * 更新物流信息
     */
    public void updateDeliveryInfo(OrderDeliveryUpdateDTO orderDeliveryUpdateDTO, OrderVO orderVO, String operator) {
        // 构建入参
        UpdateFulfillmentDeliveryInfoReq updateFulfillmentDeliveryInfoReq = new UpdateFulfillmentDeliveryInfoReq();
        updateFulfillmentDeliveryInfoReq.setSourceOrderNo(orderVO.getOrderNo());
        updateFulfillmentDeliveryInfoReq.setOperator(operator);
        updateFulfillmentDeliveryInfoReq.setBatchNo(orderDeliveryUpdateDTO.getBatchNo());

        FulfillmentDeliveryInfoReq newDeliveryInfo = new FulfillmentDeliveryInfoReq();
        newDeliveryInfo.setLogisticsCompany(orderDeliveryUpdateDTO.getDeliveryCompany());
        newDeliveryInfo.setLogisticsNo(orderDeliveryUpdateDTO.getDeliveryNo());
        newDeliveryInfo.setRemark(orderDeliveryUpdateDTO.getRemark());
        newDeliveryInfo.setType(orderDeliveryUpdateDTO.getDeliveryType());
        updateFulfillmentDeliveryInfoReq.setNewDeliveryInfo(newDeliveryInfo);

        log.info("FulfillmentOrderOperateProvider.updateDeliveryInfo updateFulfillmentDeliveryInfoReq={}", JSON.toJSONString(updateFulfillmentDeliveryInfoReq));
        DubboResponse<Void> response = fulfillmentOrderOperateProvider.updateDeliveryInfo(updateFulfillmentDeliveryInfoReq);
        log.info("FulfillmentOrderOperateProvider.updateDeliveryInfo response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
    }

}
