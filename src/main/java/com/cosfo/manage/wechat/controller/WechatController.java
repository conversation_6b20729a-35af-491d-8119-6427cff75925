package com.cosfo.manage.wechat.controller;


import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.wechat.model.vo.UserLiteAuthVo;
import com.cosfo.manage.wechat.service.AuthorizerService;
import com.cosfo.manage.wechat.service.WeixinService;
import com.cosfo.manage.wechat.service.WeixinTpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.cosfo.manage.common.controller.BaseController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Optional;


@RestController
@Slf4j
public class WechatController extends BaseController {

    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    protected WeixinTpService weixinTpService;
    @Resource
    AuthorizerService authorizerService;
    @Resource
    WeixinService weixinService;


    /**
     * 授权变更通知推送
     * 授权成功后会推送此事件
     * 此接口为绑定微信推送url
     */
    @PostMapping(value="/weixin/authEvent")
    public String authEvent(@RequestParam Map<String, String> params,
                            @RequestBody String ticketBody) {
        try {
            weixinTpService.processAuthEvent(null,params, ticketBody);
        }catch (Exception e){
            log.error(e.getMessage()+request.getRequestURI()+"?"+ Optional.ofNullable(request.getQueryString()).orElse(""));
        }
        return "success";
    }


    /**
     * 提供接口给前端，用户授权页
     * 步骤五、管理员授权确认之后，授权页会自动跳转进入回调 URI，
     * 并在 URL 参数中返回授权码和过期时间(redirect_url?auth_code=xxx&expires_in=600)。
     */
    @RequestMapping(value={"/tenant/preAuth"})
    public ResultDTO weiXinPreAuth() throws URISyntaxException {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        //返回授权链接
        return weixinTpService.createPreAuthUrl(contextInfoDTO.getTenantId(),request);
    }

    /**
     * 此接口为提供的回调url接口
     * 此处接收上方传来的tenant_id(拼接好的)和auth_code(回调返回的)，过期时间不考虑接收。
     * 目的：防止授权成功推送事件时去缓存取tenant_id缓存失效，这里在回调的时候再进行一次处理，绑定appid和tenant_id。
     */
    @GetMapping(value="/weixin/tenant/authcallback")
    public String authEventCallback(@RequestParam(required = false) Long tenant_id,
                                    @RequestParam String auth_code){
        try {
            weixinTpService.updateAuthorizerTenant(auth_code,tenant_id);
        }catch (Exception e){
            log.error(e.getMessage()+request.getRequestURI()+"?"+ Optional.ofNullable(request.getQueryString()).orElse(""));
        }
        return "success";
    }



    /**
     * 处理第三方开发者，消息事件
     * @param appId
     * @return
     */
    @RequestMapping(value={"/weixin/{appId}/callback"})
    public String callback(@PathVariable String appId,
                           @RequestParam Map<String, String> params,
                           @RequestBody String body){
        try {
            weixinTpService.processMessage(appId,params,body);
        }catch (Exception e){
            log.error(e.getMessage()+request.getRequestURI()+"?"+ Optional.ofNullable(request.getQueryString()).orElse(""));
        }
        return "success";
    }


    @GetMapping(value="/system/refreshToken")
    public ResultDTO refreshAccessToken(@RequestParam(value = "appId",required = false) String appId) throws Exception {
        authorizerService.refreshAuthorizerToken(Boolean.TRUE,appId);
        return ResultDTO.success("success");
    }



    /**
     * 小程序微信授权获取openId和unionId
     */
    @RequestMapping(value="/user/getOpenInfo",method = RequestMethod.POST)
    public ResultDTO wxLoginByCode(@RequestBody UserLiteAuthVo userLiteAuthVo) throws Exception {
        return  weixinService.getCode2Session(userLiteAuthVo.getAppId(),userLiteAuthVo.getCode());
    }

    /**
     * 商城租户获取已授权的小程序列表
     */
    @RequestMapping(value="/tenant/getAuthorizedList",method = RequestMethod.GET)
    public ResultDTO getAuthorizedList() {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return authorizerService.getAuthorizedList(contextInfoDTO.getTenantId());
    }

}
