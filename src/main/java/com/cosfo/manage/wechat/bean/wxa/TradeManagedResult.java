package com.cosfo.manage.wechat.bean.wxa;

import com.cosfo.manage.wechat.bean.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/8 下午7:37
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class TradeManagedResult extends BaseResult {

    /**
     * 是否已开通小程序发货信息管理服务
     */
    private Boolean is_trade_managed;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
