package com.cosfo.manage.wechat.bean;

import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.wechat.bean.sns.Jscode2sessionResult;
import com.cosfo.manage.wechat.bean.wxa.TemplateItem;
import com.cosfo.manage.wechat.bean.wxa.WxTemplateDTO;
import com.cosfo.manage.wechat.model.dto.AuditVersionDto;
import com.cosfo.manage.wechat.model.dto.DraftDto;
import com.cosfo.manage.wechat.model.dto.ExpVersionDto;
import com.cosfo.manage.wechat.model.dto.OnlineVersionDto;
import com.cosfo.manage.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.model.po.WechatTemplatePackage;
import com.cosfo.manage.wechat.model.vo.SessionKeyVo;

public class WxConverter {

    public static WxTemplateDTO toDto(TemplateItem templateItem){
        WxTemplateDTO templateDto = new WxTemplateDTO();
        templateDto.setTemplate_id(templateItem.getTemplate_id());
        templateDto.setUser_desc(templateItem.getUser_desc());
        templateDto.setUser_version(templateItem.getUser_version());
        templateDto.setCreate_time(templateItem.getCreate_time());
        templateDto.setDraft_id(templateItem.getDraft_id());
        templateDto.setSource_miniprogram(templateItem.getSource_miniprogram());
        return templateDto;
    }

    public static DraftDto toDraftDto(TemplateItem templateItem){
        DraftDto draftDto = new DraftDto();
        draftDto.setUser_desc(templateItem.getUser_desc());
        draftDto.setUser_version(templateItem.getUser_version());
        draftDto.setCreate_time(templateItem.getCreate_time());
        draftDto.setDraft_id(templateItem.getDraft_id());
        return draftDto;
    }

    public static OnlineVersionDto toOnlineDto(WechatTemplatePackage templatePackage){
        OnlineVersionDto onlineVersionDto = new OnlineVersionDto();
        onlineVersionDto.setAppid(templatePackage.getAppid());
        onlineVersionDto.setCreateTime(DateUtil.toDate(templatePackage.getCreateTime()));
        onlineVersionDto.setPkgStatus(templatePackage.getPkgStatus());
        onlineVersionDto.setPkgDesc(templatePackage.getPkgDesc());
        onlineVersionDto.setTemplateId(templatePackage.getTemplateId());
        onlineVersionDto.setVersion(templatePackage.getVersion());
        onlineVersionDto.setRemark(templatePackage.getRemark());
        return onlineVersionDto;
    }

    public static ExpVersionDto toExpDto(WechatTemplatePackage templatePackage){
        ExpVersionDto expVersionDto = new ExpVersionDto();
        expVersionDto.setAppid(templatePackage.getAppid());
        expVersionDto.setCreateTime(DateUtil.toDate(templatePackage.getCreateTime()));
        expVersionDto.setPkgStatus(templatePackage.getPkgStatus());
        expVersionDto.setPkgDesc(templatePackage.getPkgDesc());
        expVersionDto.setTemplateId(templatePackage.getTemplateId());
        expVersionDto.setVersion(templatePackage.getVersion());
        expVersionDto.setRemark(templatePackage.getRemark());
        return expVersionDto;
    }

    public static AuditVersionDto toAuditDto(WechatTemplatePackage templatePackage){
        AuditVersionDto auditVersionDto = new AuditVersionDto();
        auditVersionDto.setAppid(templatePackage.getAppid());
        auditVersionDto.setCreateTime(DateUtil.toDate(templatePackage.getCreateTime()));
        auditVersionDto.setPkgStatus(templatePackage.getPkgStatus());
        auditVersionDto.setPkgDesc(templatePackage.getPkgDesc());
        auditVersionDto.setTemplateId(templatePackage.getTemplateId());
        auditVersionDto.setVersion(templatePackage.getVersion());
        auditVersionDto.setRemark(templatePackage.getRemark());
        return auditVersionDto;
    }

    public static WechatAuthorizer toAuthorizer(WechatAuthorizerDto wechatAuthorizerDto){
        WechatAuthorizer wechatAuthorizer = new WechatAuthorizer();
        wechatAuthorizer.setAppId(wechatAuthorizerDto.getAppId());
        wechatAuthorizer.setAccessToken(wechatAuthorizerDto.getAccessToken());
        wechatAuthorizer.setAccessTokenExpiretime(wechatAuthorizerDto.getAccessTokenExpiretime());
        wechatAuthorizer.setAppType(wechatAuthorizerDto.getAppType());
        return wechatAuthorizer;
    }

    public static SessionKeyVo toSessionKeyVo(Jscode2sessionResult sessionResult){
        SessionKeyVo sessionKeyVo = new SessionKeyVo();
        sessionKeyVo.setOpenId(sessionResult.getOpenid());
        sessionKeyVo.setUnionId(sessionResult.getUnionid());
        sessionKeyVo.setSessionKey(sessionResult.getSession_key());
        return sessionKeyVo;
    }





}
