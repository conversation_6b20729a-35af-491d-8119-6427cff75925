package com.cosfo.manage.wechat.model.vo;

import lombok.Data;

import javax.persistence.criteria.CriteriaBuilder;

@Data
public class CommitCodePreVo {

    /**
     * 授权方appId
     */
    private String appId;
    /**
     * 第三方平台接口调用令牌authorizer_access_token
     */
    private String accessToken;
    /**
     * 代码库中的代码模板 ID可通过获取代码模板列表接口获取template_id
     * 注意，如果该模板id为标准模板库的模板id，则ext_json可支持的参数为：{"extAppid":" ", "ext": {}, "window": {}}
     */
    private Integer templateId;
    /**
     * 为了方便第三方平台的开发者引入 extAppid 的开发调试工作，引入ext.json配置文件概念，
     * 该参数则是用于控制ext.json配置文件的内容。关于该参数的补充说明请查看下方的"ext_json补充说明"。
     */
    private String extJson;
    /**
     * 代码版本号，开发者可自定义（长度不要超过 64 个字符）
     */
    private String userVersion;
    /**
     * 代码描述，开发者可自定义
     */
    private String userDesc;
}
