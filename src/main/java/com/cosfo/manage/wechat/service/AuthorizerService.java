package com.cosfo.manage.wechat.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.wechat.bean.wxa.ModifyDomain;
import com.cosfo.manage.wechat.bean.wxa.ModifyDomainResult;
import com.cosfo.manage.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.model.po.WechatLiteConfig;

import java.util.List;

public interface AuthorizerService {

    /**
     * 获取三方平台授权信息
     * @return
     * @
     * manage
     */
    WechatAuthorizerDto getAuthorizer();

    /**
     * 处理托管事件--更新第三方平台ticket
     * @param ticket
     * @
     */
    void updateTpTicket(String appId,String ticket) ;

    /**
     * 更新tp的accessToken
     * manage
     * @param tpAuthorizer
     */
    void updateTpToken(boolean force,WechatAuthorizerDto tpAuthorizer) throws Exception;

    /**
     * 处理托管事件--托管
     */
    WechatAuthorizer authorized(WechatAuthorizerDto wechatAuthorizerDto, String appId, String authCode, String preAuthCode) ;


    /**
     * 刷新令牌
     * manage
     */
    void refreshAuthorizerToken(boolean force,String appId) throws Exception;

    /**
     * 刷新令牌
     * manage
     * @param force
     * @param appId
     */
    void refreshToken(boolean force,String appId) throws Exception;

    /**
     * 获取刷新码
     * manage
     * @param appId
     */
    WechatAuthorizer getAuthInfo(String appId,String authCode) throws Exception;

    /**
     * 处理托管事件--取消拖管
     * @param appId
     * @
     */
    void unAuthorized(String appId) ;

    /**
     * 获取授权方accessToken
     * manage
     */
    WechatAuthorizer getAccessTokenByAppId(String appId);

    /**
     * 获取授权方信息列表
     * manage
     */
    List<WechatAuthorizer> getAuthorizers(List<String> appIds);

    /**
     * 获取授权方账号基本信息
     */
    WechatLiteConfig updateAuthorizerInfo(WechatAuthorizerDto wechatAuthorizerDto, String appId) throws Exception;

    /**
     * manage
     * @param tenantId
     * @return
     */
    ResultDTO getAuthorizedList(Long tenantId);
    /**
     * 设置小程序服务器域名
     * manage
     */
    void setModifyDomain(String access_token,String action, String download_domain, String request_domain, String socket_domain, String upload_domain) throws Exception;
//    /**
//     * 设置小程序业务域名
//     */
//    void setModifyWebviewDomain(String access_token,String action, String web_view_domain) throws Exception;



}
