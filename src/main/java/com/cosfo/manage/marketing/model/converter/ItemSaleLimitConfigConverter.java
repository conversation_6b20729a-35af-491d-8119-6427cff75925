package com.cosfo.manage.marketing.model.converter;

import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.model.po.ItemSaleLimitConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ItemSaleLimitConfigConverter {

    ItemSaleLimitConfigConverter INSTANCE = Mappers.getMapper(ItemSaleLimitConfigConverter.class);

    ItemSaleLimitConfigDTO toDTO(ItemSaleLimitConfig itemSaleLimitConfig);

    ItemSaleLimitConfig toPO(ItemSaleLimitConfigDTO itemSaleLimitConfigDTO);
}
