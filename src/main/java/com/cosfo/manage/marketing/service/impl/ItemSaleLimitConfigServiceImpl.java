package com.cosfo.manage.marketing.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.manage.marketing.model.converter.ItemSaleLimitConfigConverter;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.model.po.ItemSaleLimitConfig;
import com.cosfo.manage.marketing.repository.ItemSaleLimitConfigRepository;
import com.cosfo.manage.marketing.service.ItemSaleLimitConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemSaleLimitConfigServiceImpl implements ItemSaleLimitConfigService {

    @Resource
    private ItemSaleLimitConfigRepository itemSaleLimitConfigRepository;

    @Override
    public Map<Long, ItemSaleLimitConfigDTO> queryItemSaleLimitConfigMap(Long tenantId, Collection<Long> itemIdList) {
        List<ItemSaleLimitConfig> itemSaleLimitConfigs = itemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(tenantId, itemIdList);
        if (!CollectionUtils.isEmpty(itemSaleLimitConfigs)) {
            return itemSaleLimitConfigs.stream().collect(Collectors.toMap(ItemSaleLimitConfig::getMarketItemId, ItemSaleLimitConfigConverter.INSTANCE::toDTO));
        }
        return Collections.emptyMap();
    }

    @Override
    public ItemSaleLimitConfigDTO queryItemSaleLimitConfig(Long tenantId, Long itemId) {
        ItemSaleLimitConfigDTO itemSaleLimitConfig = queryItemSaleLimitConfigMap(tenantId, Collections.singletonList(itemId)).get(itemId);
        if (itemSaleLimitConfig == null) {
            return ItemSaleLimitConfigDTO.DEFAULT();
        }
        return itemSaleLimitConfig;
    }

    @Override
    public boolean saveItemSaleLimitConfig(ItemSaleLimitConfigDTO itemSaleLimitConfigDTO) {
        ItemSaleLimitConfig itemSaleLimitConfig = ItemSaleLimitConfigConverter.INSTANCE.toPO(itemSaleLimitConfigDTO);
        LambdaUpdateWrapper<ItemSaleLimitConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ItemSaleLimitConfig::getTenantId, itemSaleLimitConfig.getTenantId());
        updateWrapper.eq(ItemSaleLimitConfig::getMarketItemId, itemSaleLimitConfig.getMarketItemId());
        return itemSaleLimitConfigRepository.saveOrUpdate(itemSaleLimitConfig, updateWrapper);
    }
}
