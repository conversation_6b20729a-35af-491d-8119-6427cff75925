package com.cosfo.manage.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.order.model.po.OrderItemFeeTransaction;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单项费用明细流水 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface OrderItemFeeTransactionMapper extends BaseMapper<OrderItemFeeTransaction> {

    /**
     * 计算订单总费用
     *
     * @param orderIds
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    BigDecimal calTotalFeeBy(@Param("ids") List<Long> orderIds, @Param("feeType") Integer feeType, @Param("transactionType") Integer transactionType, @Param("tenantId") Long tenantId);

    /**
     * 批量查询订单明细费用项
     *
     * @param orderIds
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    List<OrderItemFeeTransaction> listOrderItemFee(@Param("ids") List<Long> orderIds, @Param("feeType") Integer feeType, @Param("transactionType") Integer transactionType, @Param("tenantId") Long tenantId);

    /**
     * 批量查询售后费用明细项
     * @param afterSaleIds
     * @param feeType
     * @param tenantId
     * @return
     */
    List<OrderItemFeeTransaction> listOrderItemFeeByAfterSaleIds(@Param("ids") List<Long> afterSaleIds, @Param("feeType") Integer feeType, @Param("tenantId") Long tenantId);

}
