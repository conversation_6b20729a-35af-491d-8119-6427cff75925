package com.cosfo.manage.order.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * order_item
 * <AUTHOR>
@Data
public class OrderItem implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * sku编码
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 可申请售后过期时间
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 已配数量(无仓订单有效)
     */
    private Integer deliveryQuantity;

    private static final long serialVersionUID = 1L;
}