package com.cosfo.manage.order.model.vo.aftersale;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/10
 */
@Data
public class OrderAfterSaleRuleDetailVO {
    /**
     * 配送仓库类型 0自营 1三方
     */
    private Integer deliveryType;
    /**
     * 商品分组Id
     */
    private List<Long> classificationIds;
    /**
     * 订单状态类型 5司机送达/门店收货
     */
    private Integer orderStatusType;
    /**
     * 可申请售后时间
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;
}
