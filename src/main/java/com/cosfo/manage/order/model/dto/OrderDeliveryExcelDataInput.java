package com.cosfo.manage.order.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderDeliveryExcelDataInput {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("供应商编号")
    private String supplierTenantId;

    @ExcelProperty("发货数量")
    private String deliveryAmount;

    @ExcelProperty("配送方式")
    private String deliveryType;

    @ExcelProperty("物流公司")
    private String deliveryCompany;

    @ExcelProperty("配送单号")
    private String deliveryNo;

    @ExcelProperty("备注")
    private String remark;

    @ExcelIgnore
    private String errorMessage;
}
