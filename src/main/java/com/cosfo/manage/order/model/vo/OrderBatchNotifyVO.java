package com.cosfo.manage.order.model.vo;

import lombok.Data;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-13
 * @Description:
 */
@Data
public class OrderBatchNotifyVO {
    /**
     * 总条数
     */
    private Integer totalNum;
    /**
     * 成功条数
     */
    private Integer successNum;
    /**
     * 失败条数
     */
    private Integer failNum;

    /**
     * 失败信息列表
     */
    private List<NotifyErrorVO> notifyErrorList;

    @Data
    public static class NotifyErrorVO {

        /**
         * 错误描述
         */
        private String message;
        /**
         * 订单列表
         */
        private List<String> orderNoList;

    }
}
