package com.cosfo.manage.order.model.vo.aftersale;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/27
 */
@Data
public class OrderAfterSaleRuleVO {
    /**
     * primary key
     */
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 类型 0配送仓库 1商品分组
     */
    private Integer type;
    /**
     * 是否默认0默认 1非默认
     */
    private Integer defaultFlag;
    /**
     * 规则
     */
    private String rule;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
}
