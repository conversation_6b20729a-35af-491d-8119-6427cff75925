package com.cosfo.manage.order.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/10
 */
@Data
public class OrderAfterSaleRuleDetailVO {
    /**
     * 配送仓库类型 0无仓 1三方 2自营
     */
    private Integer deliveryType;
    /**
     * 商品分组Id
     */
    private List<Long> classificationIds;
    /**
     * 订单状态类型 5司机送达/门店收货
     */
    private Integer orderStatusType;
    /**
     * 可申请售后时间
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 审批标识 0自动同意 1需要审核
     */
    private Integer auditFlag;

    /**
     * 快递履约-可申请售后时间
     */
    private Integer expressOrderApplyEndTime;
}
