package com.cosfo.manage.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-04-11
 * @Description:
 */
@Data
public class FulfillmentDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递单号
     */
    private String logisticsNo;

    /**
     * 快递公司
     */
    private String logisticsCompany;

    /**
     * 商品id
     */
    private String itemId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 配送类型 0:其它;1:快递
     */
    private Integer deliveryType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片
     */
    private String pics;
}
