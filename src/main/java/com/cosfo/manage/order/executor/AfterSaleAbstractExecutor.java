//package com.cosfo.manage.order.executor;
//
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTO;
//import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
//import com.cosfo.ordercenter.client.resp.OrderDTO;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 15:29
// */
//public abstract class AfterSaleAbstractExecutor {
//
//    /**
//     * 审核通过
//     * @param orderAfterSaleAuditDTO
//     * @param requestContextInfoDTO
//     * @return
//     */
//    public abstract ResultDTO reviewSuccess(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO, OrderAfterSale afterSale, OrderDTO order);
//
//}
