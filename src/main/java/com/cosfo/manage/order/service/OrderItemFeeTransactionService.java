package com.cosfo.manage.order.service;

import com.cosfo.manage.common.context.OrderItemFeeEnum;
import com.cosfo.manage.order.model.po.OrderItemFeeTransaction;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单项费用明细流水 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface OrderItemFeeTransactionService {

    /**
     * 计算订单项总费用
     *
     * @param orderIds
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    BigDecimal calTotalFeeBy(List<Long> orderIds, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId);

    /**
     * 查询订单明细项费用
     * @param orderItems
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    Map<Long, BigDecimal> calOrderItemFeeMap(List<Long> orderItems, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId);


    /**
     * 根据售后单查询费用明细
     * @param afterSaleIds
     * @param feeType
     * @param tenantId
     * @return
     */
    Map<Long, BigDecimal> calAfterSaleItemFeeMap(List<Long> afterSaleIds, OrderItemFeeEnum.FeeType feeType, Long tenantId);

}
