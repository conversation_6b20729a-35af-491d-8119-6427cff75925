package com.cosfo.manage.order.service.impl;

import com.cosfo.manage.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper;
import com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.manage.order.service.OrderAgentSkuFeeRuleSnapshotService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/27
 */
@Service
public class OrderAgentSkuFeeRuleSnapshotServiceImpl implements OrderAgentSkuFeeRuleSnapshotService {
    @Resource
    private OrderAgentSkuFeeRuleSnapshotMapper orderAgentSkuFeeRuleSnapshotMapper;

    @Override
    public OrderAgentSkuFeeRuleSnapshot queryTenantProfitSharingDetail(Long orderId, Long tenantId) {
        return orderAgentSkuFeeRuleSnapshotMapper.selectByOrderIdAndTenantId(orderId, tenantId);
    }

    @Override
    public String queryOrderHitAgentRule(Long orderId, Long tenantId) {
        return orderAgentSkuFeeRuleSnapshotMapper.queryHitRule(orderId, tenantId);
    }

    @Override
    public Map<Long, String> batchQueryOrderHitAgentRule(List<Long> orderIds, Long tenantId) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new HashMap<>();
        }
        List<OrderAgentSkuFeeRuleSnapshot> ruleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.batchQueryOrderHitAgentRule(orderIds, tenantId);
        if (CollectionUtils.isEmpty(ruleSnapshots)) {
            return new HashMap<>();
        }

        return ruleSnapshots.stream().collect(Collectors.toMap(OrderAgentSkuFeeRuleSnapshot::getOrderId, snapshot -> snapshot.getHitRule() == null ? "" : snapshot.getHitRule()));
    }
}
