package com.cosfo.manage.order.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.order.model.dto.OrderPresaleBatchDeliveryTimeDTO;
import com.cosfo.manage.order.model.dto.OrderPresaleDeliveryTimeDTO;
import com.cosfo.manage.order.service.OrderPresaleService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 预售订单管理
 */
@RestController
@RequestMapping("/order/presale")
public class OrderPresaleController extends BaseController {

    @Resource
    private OrderPresaleService orderPresaleService;

    /**
     * 设置预售订单配送时间
     */
    @RequestMapping(value = "/upsert/delivery-time")
    public CommonResult<Void> setDeliveryTime(@Valid @RequestBody OrderPresaleDeliveryTimeDTO orderPresaleDeliveryTimeDTO) {
        orderPresaleService.setDeliveryTime(orderPresaleDeliveryTimeDTO);
        return CommonResult.ok();
    }

    /**
     * 批量设置预售订单配送时间
     */
    @RequestMapping(value = "/batch/upsert/delivery-time")
    public CommonResult<Long> batchSetDeliveryTime(@Valid @RequestBody OrderPresaleBatchDeliveryTimeDTO orderPresaleBatchDeliveryTimeDTO) {
        return CommonResult.ok(orderPresaleService.batchSetDeliveryTime(orderPresaleBatchDeliveryTimeDTO, getMerchantInfoDTO()));
    }
}
