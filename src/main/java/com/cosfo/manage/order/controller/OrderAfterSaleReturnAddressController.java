package com.cosfo.manage.order.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.order.model.vo.ReturnAddressAddInput;
import com.cosfo.manage.order.model.vo.ReturnAddressUpdateInput;
import com.cosfo.manage.order.model.vo.ReturnAddressVO;
import com.cosfo.manage.tenant.dao.TenantReturnAddressDao;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 租户售后退回地址管理
 * @author: xiaowk
 * @date: 2023/5/24 下午12:53
 */
@RestController
@RequestMapping("/after/sale/return-address")
public class OrderAfterSaleReturnAddressController extends BaseController {

    @Resource
    private TenantReturnAddressDao tenantReturnAddressDao;
    /**
     * 退回地址列表
     */
    @RequestMapping(value = "/query/list", method = RequestMethod.POST)
    public CommonResult<List<ReturnAddressVO>> list() {
        return CommonResult.ok(tenantReturnAddressDao.queryByTenantId(getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 新增退货地址
     * @param returnAddressAddInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add", method = RequestMethod.POST)
    public CommonResult add(@Valid @RequestBody ReturnAddressAddInput returnAddressAddInput) {
        tenantReturnAddressDao.insert(returnAddressAddInput, getMerchantInfoDTO().getTenantId());
        return CommonResult.ok();
    }

    /**
     * 删除退货地址
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete", method = RequestMethod.POST)
    public CommonResult delete(Long id) {
        tenantReturnAddressDao.delete(id);
        return CommonResult.ok();
    }

    /**
     * 更新退货地址
     * @param returnAddressUpdateInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update", method = RequestMethod.POST)
    public CommonResult update(@Valid @RequestBody ReturnAddressUpdateInput returnAddressUpdateInput) {
        tenantReturnAddressDao.update(returnAddressUpdateInput);
        return CommonResult.ok();
    }

}
