package com.cosfo.manage.order.controller.rolestrategy;

import com.cosfo.manage.tenant.service.AuthRoleService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRoleRuleContext {

    @Resource
    private List<OrderRoleRuleStrategy> strategyList;
    @Resource
    private AuthRoleService authRoleService;

    public OrderRoleRuleStrategy handle(Long tenantId, Long authUserId) {
        Boolean isSupplier = authRoleService.isSupplierRole(tenantId, authUserId);
        String role = isSupplier ? "supplier" : "default";
        for (OrderRoleRuleStrategy orderRoleRuleStrategy : strategyList) {
            if (orderRoleRuleStrategy.role().equals(role)) {
                return orderRoleRuleStrategy;
            }
        }
        throw new BizException("角色展示策略加载异常");
    }

}
