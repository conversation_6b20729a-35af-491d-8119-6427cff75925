package com.cosfo.manage.order.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleRuleUpdateDTO;
import com.cosfo.manage.order.model.vo.OrderAfterSaleRuleResultVO;
import com.cosfo.manage.order.service.OrderAfterSaleService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 售后规则管理
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/27
 */
@RestController
@RequestMapping("/after/sale/rule")
public class OrderAfterSaleRuleController extends BaseController {
    @Resource
    private OrderAfterSaleService service;

    /**
     * 查询售后规则
     *
     * @return
     */
    @PostMapping("/query/rule")
    public ResultDTO<OrderAfterSaleRuleResultVO> queryRule() {
        return ResultDTO.success(service.queryRule(getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 修改售后规则
     *
     * @param afterSaleRuleDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:mall-after-sale-rule:update", expireError = true)
    @PostMapping("/upsert/rule")
    public ResultDTO<Boolean> updateRule(@RequestBody @Valid OrderAfterSaleRuleUpdateDTO afterSaleRuleDTO){
        afterSaleRuleDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return ResultDTO.success(service.updateRule(afterSaleRuleDTO, getMerchantInfoDTO()));
    }
}
