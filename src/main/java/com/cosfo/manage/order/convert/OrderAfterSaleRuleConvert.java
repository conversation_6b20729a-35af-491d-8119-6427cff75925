package com.cosfo.manage.order.convert;

import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleRuleVO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleRuleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/8/15 下午3:54
 */
@Mapper
public interface OrderAfterSaleRuleConvert {

    OrderAfterSaleRuleConvert INSTANCE = Mappers.getMapper(OrderAfterSaleRuleConvert.class);

    OrderAfterSaleRuleVO dto2VO(OrderAfterSaleRuleDTO dto);

    List<OrderAfterSaleRuleVO> dtos2VOS(List<OrderAfterSaleRuleDTO> dtos);
}
