package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class InventerBillSaveByPosOrder4Tester extends XianMuJavaProcessorV2 {


    @Resource
    private PosOrderDomainService posOrderDomainService;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String id = context.getInstanceParameters ();
        if (StringUtils.isEmpty (id)) {
            return new ProcessResult(true);
        }
        posOrderDomainService.insertInventoryBillFromPosOrderNo (Long.valueOf(id));
        return new ProcessResult(true);
    }
}
