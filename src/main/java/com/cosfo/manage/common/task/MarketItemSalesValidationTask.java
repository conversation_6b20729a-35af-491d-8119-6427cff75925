package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.repository.PaymentRepository;
import com.cosfo.manage.market.repository.MarketItemOrderSummaryRepository;
import com.cosfo.manage.market.service.MarketItemOrderSummaryService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Component
@Slf4j
public class MarketItemSalesValidationTask extends XianMuJavaProcessorV2 {

    @Resource
    private MarketItemOrderSummaryRepository marketItemOrderSummaryRepository;
    @Resource
    private MarketItemOrderSummaryService marketItemOrderSummaryService;
    @Resource
    private PaymentRepository paymentRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        LocalDate date;
        String params = context.getInstanceParameters();
        if (StringUtils.isEmpty(params)) {
            params = context.getJobParameters();
        }
        if (StringUtils.isEmpty(params)) {
            date = LocalDate.now().minusDays(1); // 默认校验昨天的数据
        } else {
            date = LocalDate.parse(params);
        }
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        log.info("验证商品销量任务开始, startDateTime: {}, endDateTime: {}", startOfDay, endOfDay);
        if (!(validateSalesPrice(date, startOfDay, endOfDay)
                && validateSalesQuantity(date, startOfDay, endOfDay))) {
            log.info("销量榜金额和件数校验不通过, 开始校正销量榜");
            correctSalesRanking(startOfDay, endOfDay);
        }
        if (!(validateSalesPrice(date, startOfDay, endOfDay)
                && validateSalesQuantity(date, startOfDay, endOfDay))) {
            log.error("销量榜金额校正后仍不通过,请检查", new ProviderException());
        }
        log.info("验证商品销量任务结束------");

        return new ProcessResult(true);
    }

    private boolean validateSalesPrice(LocalDate date, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        // payment表的总销售额
        BigDecimal totalPriceByPayment = marketItemOrderSummaryRepository.sumTotalOrderPriceByPayments(startDateTime, endDateTime);
        // market_item_order_summary表的总销售额
        BigDecimal totalPriceInSummary = marketItemOrderSummaryRepository.sumTotalOrderPricePerDay(date);

        if (totalPriceByPayment.compareTo(totalPriceInSummary) != 0) {
            log.info("销量榜金额校验不通过, totalPriceByPayment: {}, totalPriceInSummary: {}",
                    totalPriceByPayment, totalPriceInSummary);
            return false;
        }
        log.info("销量榜金额校验通过, totalPriceByPayment: {}, totalPriceInSummary: {}",
                totalPriceByPayment, totalPriceInSummary);
        return true;
    }

    private boolean validateSalesQuantity(LocalDate date, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        // payment表的总销售件数
        Integer totalQuantityByPayment = marketItemOrderSummaryRepository
                .sumTotalOrderQuantityByPayments(startDateTime, endDateTime);
        // market_item_order_summary表的总销售件数
        Integer totalQuantityInSummary = marketItemOrderSummaryRepository.sumTotalOrderQuantityPerDay(date);

        if (totalQuantityByPayment.compareTo(totalQuantityInSummary) != 0) {
            log.info("销量榜件数校验不通过, totalQuantityByPayment: {}, totalQuantityInSummary: {}",
                    totalQuantityByPayment, totalQuantityInSummary);
            return false;
        }
        log.info("销量榜件数校验通过, 件数: {}", totalQuantityByPayment);
        return true;
    }


    private void correctSalesRanking(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        List<Payment> payments = paymentRepository.selectSuccessPaymentByTime(startDateTime, endDateTime);
        payments.forEach(payment -> marketItemOrderSummaryService.generateMarketItemOrderSummaryByPayment(payment));

        log.info("结束校正商品销量榜-------");
    }

}
