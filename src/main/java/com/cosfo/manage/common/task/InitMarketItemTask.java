package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyQueryDTO;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化商品
 */
@Component
@Slf4j
public class InitMarketItemTask extends XianMuJavaProcessorV2 {
    @Autowired
    private MarketService marketService;

    @Resource
    private ProductPricingSupplyService productPricingSupplyService;

    @Resource
    private MarketItemService marketItemService;

    @Resource
    private MarketFacade marketFacade;

    private Integer onSale= OnSaleTypeEnum.ON_SALE.getCode ();
    private Integer itemSaleMode = 0;
//    private static Map<Long,String> skuIdCNameMap = getSkuIdCNameMap();
//    private static Map<String,Long> cNameCIdMap = getCNameCIdMap ();
    private static Map<Long,String> skuIdCNameMap = Collections.emptyMap ();
    private static Map<String,Long> cNameCIdMap = Collections.emptyMap ();
//    private static Map<Long,String> skuIdCNameMap = getSkuIdCNameMap1();
//    private static Map<String,Long> cNameCIdMap = getCNameCIdMap1 ();
//
//    private static Map<String, Long> getCNameCIdMap1() {
//        cNameCIdMap = new HashMap<> ();
//        cNameCIdMap.put ("预付子分组+预付分组",881L);
//        return cNameCIdMap;
//    }
//
//    private static Map<Long, String> getSkuIdCNameMap1() {
//        skuIdCNameMap = new HashMap<> ();
//        skuIdCNameMap.put (24089L,"预付子分组+预付分组");
//        skuIdCNameMap.put (16031L,"预付子分组+预付分组");
//        skuIdCNameMap.put (16776L,"预付子分组+预付分组");
//        skuIdCNameMap.put (16277L,"预付子分组+预付分组");
//        return skuIdCNameMap;
//    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        }

        Map<String, String> map = Collections.emptyMap ();
        if (!StringUtils.isEmpty (context.getInstanceParameters())) {
            map = JSON.parseObject (context.getInstanceParameters (), Map.class);
        }
        if (!map.containsKey("tenant")) {
            return new ProcessResult(true);
        }
        /** goodsType
            1, "报价货品"
            2, "自营货品"
            0, 全部
         **/
        if (!map.containsKey("goodsType")) {
            return new ProcessResult(true);
        }

        Long tenantId = Long.valueOf(map.get("tenant"));
        Integer goodsType = Integer.valueOf(map.get("goodsType"));
        List<String> failedSkuIds = new LinkedList<>();
        Map<Long,Long> spuMarketMap = new HashMap<> ();

        switch (goodsType){
            case 0:
                initQuotationGoods(tenantId,spuMarketMap,failedSkuIds);
                initSelfGoods(tenantId,spuMarketMap,failedSkuIds);
                break;
            case 1:
                initQuotationGoods(tenantId,spuMarketMap,failedSkuIds);
                break;
            case 2:
                initSelfGoods(tenantId,spuMarketMap,failedSkuIds);
                break;
        }
        if (CollectionUtil.isEmpty (failedSkuIds)) {
            return new ProcessResult(true);
        } else {
            return new ProcessResult(InstanceStatus.PARTIAL_FAILED, String.join(",", failedSkuIds));
        }
    }

    private void initSelfGoods(Long tenantId, Map<Long,Long> spuMarketMap ,List<String> failedSkuIds) {
        //todo
    }

    private void initQuotationGoods(Long tenantId,Map<Long,Long> spuMarketMap, List<String> failedSkuIds) {
        LoginContextInfoDTO contextInfoDTO = new LoginContextInfoDTO ();
        contextInfoDTO.setTenantId (tenantId);
        ProductPricingSupplyQueryDTO supplyQueryDTO = new ProductPricingSupplyQueryDTO ();
        supplyQueryDTO.setPageSize (100);
        //        分页查询报价品
        boolean hasNext = true;
        int pageIndex = 1;
        while(hasNext){
            supplyQueryDTO.setPageNum (pageIndex);

            PageInfo<ProductPricingSupplyDTO> page = productPricingSupplyService.listDistributionMarket (supplyQueryDTO, contextInfoDTO);
            if(CollectionUtil.isNotEmpty (page.getList ())){
                List<Long> skuIdList = page.getList ().stream ().map (ProductPricingSupplyDTO::getSupplySkuId).collect(Collectors.toList());
                Map<Long, MarketItemInfoResp> marketItemMap = marketFacade.queryQuotationMarketItemMap(skuIdList);
                for (ProductPricingSupplyDTO productPricingSupplyDTO : page.getList ()) {
                    try{
                        Long marketId = spuMarketMap.get (productPricingSupplyDTO.getSpuId ());
                        if(ObjectUtil.isEmpty (marketId)) {
                            MarketAddResDTO add = marketService.add (buildMarketSpuInput (productPricingSupplyDTO,marketItemMap.get(productPricingSupplyDTO.getSupplySkuId())), tenantId);
                            marketId = add.getMarketId ();
                            spuMarketMap.put (productPricingSupplyDTO.getSpuId (), marketId);
                        }else{
                            marketItemService.save(buildMarketItemInput(productPricingSupplyDTO,marketId,marketItemMap.get(productPricingSupplyDTO.getSupplySkuId())), tenantId);
                        }
                    }catch (Exception e){
                        log.warn ("保存商品失败，skuId={}",productPricingSupplyDTO.getSupplySkuId (),e);
                        failedSkuIds.add(String.valueOf(productPricingSupplyDTO.getSupplySkuId ()));
                    }
                }
            }
            hasNext = page.isHasNextPage ();
            pageIndex = pageIndex + 1;
        }

    }
    private MarketSpuInput buildMarketSpuInput(ProductPricingSupplyDTO productPricingSupplyDTO,MarketItemInfoResp marketItemInfoResp) {
        MarketSpuInput spuInput = new MarketSpuInput ();
//        Long classificationId = getClassificationId (productPricingSupplyDTO.getSupplySkuId ());
        Long classificationId = 1660L;
        if(ObjectUtil.isEmpty (classificationId)){
            throw new BizException (productPricingSupplyDTO.getSupplySkuId ()+"没有对应的分类");
        }
        spuInput.setMainPicture (ObjectUtil.isNotEmpty (marketItemInfoResp.getMainPicture ())?marketItemInfoResp.getMainPicture ():null);
        spuInput.setClassificationId(classificationId);
        spuInput.setTitle(productPricingSupplyDTO.getTitle ());
        spuInput.setMarketItemInput (buildMarketItemInput(productPricingSupplyDTO,null, marketItemInfoResp));
        return spuInput;
    }

    private Long getClassificationId(Long supplySkuId) {
        String name = skuIdCNameMap.get (supplySkuId);
        if(StringUtils.isNotEmpty (name)){
           return cNameCIdMap.get (name);
        }
        return null;
    }

    private MarketItemInput buildMarketItemInput(ProductPricingSupplyDTO productPricingSupplyDTO,Long marketId,MarketItemInfoResp marketItemInfoResp) {

        MarketItemInput marketItemInput =new MarketItemInput();
        marketItemInput.setGoodsType(1);
        marketItemInput.setMiniOrderQuantity(1);
        marketItemInput.setSupplierId("-1");
        marketItemInput.setSupplierName("杭州鲜沐科技有限公司");

        marketItemInput.setDefaultPrice(buildMarketAreaItemMappingInput());
        marketItemInput.setMarketItemUnfairPriceStrategyDTO(buildMarketItemUnfairPriceStrategyDTO());
        marketItemInput.setSkuId(productPricingSupplyDTO.getSupplySkuId ());
        marketItemInput.setSpecification(productPricingSupplyDTO.getSpecification ());
        if (Objects.nonNull(marketItemInfoResp)) {
            marketItemInput.setMaxAfterSaleAmount(Objects.isNull(marketItemInfoResp.getMaxAfterSaleAmount()) ? NumberConstants.ONE :marketItemInfoResp.getMaxAfterSaleAmount());
            marketItemInput.setAfterSaleUnit(com.cosfo.manage.common.util.StringUtils.isBlank(marketItemInfoResp.getAfterSaleUnit()) ? StringConstants.DEFAULT_AFTER_SALE_UNIT : marketItemInfoResp.getAfterSaleUnit());
        } else {
            marketItemInput.setMaxAfterSaleAmount(NumberConstants.ONE);
            marketItemInput.setAfterSaleUnit(StringConstants.DEFAULT_AFTER_SALE_UNIT);
        }

        marketItemInput.setMarketId (marketId);
        marketItemInput.setItemSaleMode(itemSaleMode);
        marketItemInput.setOnSale(onSale);

        return marketItemInput;
    }

    private MarketItemUnfairPriceStrategyDTO buildMarketItemUnfairPriceStrategyDTO() {
        MarketItemUnfairPriceStrategyDTO dto = new MarketItemUnfairPriceStrategyDTO ();
        dto.setDefaultFlag(1);
        dto.setStrategyType(1);
        return dto;
    }

    private MarketAreaItemMappingInput buildMarketAreaItemMappingInput() {
        MarketAreaItemMappingInput mappingInput = new MarketAreaItemMappingInput ();
        mappingInput.setPriceType(1);
        mappingInput.setType(1);
        mappingInput.setMappingNumber(BigDecimal.valueOf (6));
        return mappingInput;
    }
    //货品id，分类名称
    private static Map<Long,String> getSkuIdCNameMap(){
        skuIdCNameMap = new HashMap<> ();
        skuIdCNameMap.put (112548L,"配料+原料");
        skuIdCNameMap.put (112547L,"糖类+食材");
        skuIdCNameMap.put (112546L,"糖类+食材");
        skuIdCNameMap.put (112545L,"果汁原料+原料");
        skuIdCNameMap.put (112544L,"糖类+食材");
        skuIdCNameMap.put (112543L,"糖类+食材");
        skuIdCNameMap.put (112542L,"糖类+食材");
        skuIdCNameMap.put (112541L,"糖类+食材");
        skuIdCNameMap.put (112540L,"糖类+食材");
        skuIdCNameMap.put (112539L,"糖类+食材");
        skuIdCNameMap.put (112538L,"其他耗材+包材");
        skuIdCNameMap.put (112537L,"其他耗材+包材");
        skuIdCNameMap.put (112536L,"耗材+其他");
        skuIdCNameMap.put (112535L,"耗材+其他");
        skuIdCNameMap.put (112534L,"耗材+其他");
        skuIdCNameMap.put (112533L,"果汁原料+原料");
        skuIdCNameMap.put (112532L,"果汁原料+原料");
        skuIdCNameMap.put (112531L,"水果制品+食材");
        skuIdCNameMap.put (112530L,"配料+原料");
        skuIdCNameMap.put (112529L,"吸管+包材");
        skuIdCNameMap.put (112528L,"吸管+包材");
        skuIdCNameMap.put (112527L,"吸管+包材");
        skuIdCNameMap.put (112526L,"坚果+原料");
        skuIdCNameMap.put (112525L,"炼乳+乳制品");
        skuIdCNameMap.put (112524L,"水果制品+食材");
        skuIdCNameMap.put (112523L,"水果制品+食材");
        skuIdCNameMap.put (112522L,"饮料冲调粉+食材");
        skuIdCNameMap.put (112521L,"饮料冲调粉+食材");
        skuIdCNameMap.put (112520L,"饮料冲调粉+食材");
        skuIdCNameMap.put (112519L,"杯+包材");
        skuIdCNameMap.put (112518L,"杯+包材");
        skuIdCNameMap.put (112517L,"杯+包材");
        skuIdCNameMap.put (112516L,"耗材+其他");
        skuIdCNameMap.put (112515L,"包装袋+包材");
        skuIdCNameMap.put (112960L,"包装袋+包材");
        skuIdCNameMap.put (112514L,"耗材+其他");
        skuIdCNameMap.put (112513L,"包装袋+包材");
        skuIdCNameMap.put (112512L,"耗材+其他");
        skuIdCNameMap.put (112511L,"液体乳+乳制品");
        skuIdCNameMap.put (112510L,"液体乳+乳制品");
        skuIdCNameMap.put (112509L,"稀奶油+乳制品");
        skuIdCNameMap.put (112508L,"糖类+食材");
        skuIdCNameMap.put (112507L,"配料+原料");
        skuIdCNameMap.put (112506L,"配料+原料");
        skuIdCNameMap.put (112505L,"水果制品+食材");
        skuIdCNameMap.put (112504L,"果汁原料+原料");
        skuIdCNameMap.put (112503L,"液体乳+乳制品");
        skuIdCNameMap.put (112502L,"饮料冲调粉+食材");
        skuIdCNameMap.put (112501L,"茶制品+食材");
        skuIdCNameMap.put (112500L,"咖啡豆+原料");
        skuIdCNameMap.put (112499L,"咖啡豆+原料");
        skuIdCNameMap.put (112498L,"茶制品+食材");
        skuIdCNameMap.put (112497L,"茶制品+食材");
        skuIdCNameMap.put (112496L,"茶制品+食材");
        skuIdCNameMap.put (112495L,"茶制品+食材");
        skuIdCNameMap.put (112492L,"茶制品+食材");
        skuIdCNameMap.put (112491L,"配料+原料");
        skuIdCNameMap.put (112490L,"配料+原料");
        skuIdCNameMap.put (112489L,"柑果类+新鲜水果");
        skuIdCNameMap.put (112488L,"配料+原料");
        skuIdCNameMap.put (112487L,"果汁原料+原料");
        skuIdCNameMap.put (112486L,"果汁原料+原料");
        skuIdCNameMap.put (112485L,"果汁原料+原料");
        skuIdCNameMap.put (112484L,"水果制品+食材");
        skuIdCNameMap.put (112483L,"水果制品+食材");
        skuIdCNameMap.put (112482L,"饮料冲调粉+食材");
        skuIdCNameMap.put (112481L,"其他耗材+其他");
        skuIdCNameMap.put (112480L,"其他耗材+其他");
        skuIdCNameMap.put (112479L,"耗材+其他");
        skuIdCNameMap.put (112478L,"厨具+包材");
        skuIdCNameMap.put (112477L,"盖+包材");
        skuIdCNameMap.put (112476L,"杯+包材");
        skuIdCNameMap.put (112475L,"耗材+其他");
        skuIdCNameMap.put (112474L,"耗材+其他");
        skuIdCNameMap.put (112473L,"其他耗材+包材");
        skuIdCNameMap.put (112472L,"盖+包材");
        skuIdCNameMap.put (112471L,"盖+包材");
        skuIdCNameMap.put (112470L,"盖+包材");
        skuIdCNameMap.put (112469L,"杯+包材");
        skuIdCNameMap.put (112468L,"耗材+其他");
        skuIdCNameMap.put (112467L,"盖+包材");
        skuIdCNameMap.put (112466L,"杯+包材");
        skuIdCNameMap.put (112465L,"茶制品+食材");
        skuIdCNameMap.put (112464L,"茶制品+食材");
        skuIdCNameMap.put (102282L,"浆果类+新鲜水果");
        skuIdCNameMap.put (101698L,"浆果类+新鲜水果");
        skuIdCNameMap.put (102059L,"浆果类+新鲜水果");
        skuIdCNameMap.put (91L,"奶酪+乳制品");
        skuIdCNameMap.put (8648L,"水果制品+食材");
        skuIdCNameMap.put (93L,"糖类+食材");
        skuIdCNameMap.put (95L,"配料+原料");
        skuIdCNameMap.put (2308L,"植脂奶油+乳制品");
        skuIdCNameMap.put (8610L,"稀奶油+乳制品");
        skuIdCNameMap.put (178L,"炼乳+乳制品");
        skuIdCNameMap.put (51L,"稀奶油+乳制品");
        skuIdCNameMap.put (49L,"稀奶油+乳制品");
        skuIdCNameMap.put (1064L,"面粉+原料");
        skuIdCNameMap.put (1065L,"面粉+原料");
        skuIdCNameMap.put (2390L,"面粉+原料");
        skuIdCNameMap.put (52L,"稀奶油+乳制品");
        skuIdCNameMap.put (1745L,"浆果类+新鲜水果");
        skuIdCNameMap.put (1745L,"浆果类+新鲜水果");
        skuIdCNameMap.put (110128L,"浆果类+新鲜水果");
        skuIdCNameMap.put (6083L,"浆果类+新鲜水果");
        skuIdCNameMap.put (110128L,"浆果类+新鲜水果");
        skuIdCNameMap.put (6083L,"浆果类+新鲜水果");
        return skuIdCNameMap;
    }

    //分类名称 ， 分类id
    private static Map<String,Long> getCNameCIdMap(){
//        select CONCAT('cNameCIdMap.put ("', a.name,'+',  b.name,'",',a.id,'L);') From `market_classification` a left join market_classification b on a.parent_id=b.id WHERE a.`tenant_id` = 63 and b.id is not null
        cNameCIdMap = new HashMap<> ();
        cNameCIdMap.put ("浆果类+新鲜水果",1132L);
        cNameCIdMap.put ("包装袋+包材",1318L);
        cNameCIdMap.put ("茶制品+食材",1325L);
        cNameCIdMap.put ("耗材+其他",1329L);
        cNameCIdMap.put ("炼乳+乳制品",1332L);
        cNameCIdMap.put ("果汁原料+原料",1338L);
        cNameCIdMap.put ("柑果类+新鲜水果",1316L);
        cNameCIdMap.put ("杯+包材",1319L);
        cNameCIdMap.put ("水果制品+食材",1326L);
        cNameCIdMap.put ("其他耗材+其他",1330L);
        cNameCIdMap.put ("奶酪+乳制品",1333L);
        cNameCIdMap.put ("坚果+原料",1339L);
        cNameCIdMap.put ("厨具+包材",1320L);
        cNameCIdMap.put ("糖类+食材",1327L);
        cNameCIdMap.put ("稀奶油+乳制品",1334L);
        cNameCIdMap.put ("咖啡豆+原料",1340L);
        cNameCIdMap.put ("盖+包材",1321L);
        cNameCIdMap.put ("液体乳+乳制品",1335L);
        cNameCIdMap.put ("面粉+原料",1341L);
        cNameCIdMap.put ("其他耗材+包材",1322L);
        cNameCIdMap.put ("植脂奶油+乳制品",1336L);
        cNameCIdMap.put ("配料+原料",1342L);
        cNameCIdMap.put ("吸管+包材",1323L);
        return cNameCIdMap;
    }
}
