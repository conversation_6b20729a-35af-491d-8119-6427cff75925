package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Getter;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @description: 霸王茶姬相关配置
 * @author: zach
 * @date: 2025-04-15
 **/
@Configuration
public class ChageeNacosConfig {

    @NacosValue(value = "${open.api.chagee.tenantIds:245143}", autoRefreshed = true)
    private List<Long> chageeTenantIdList;

    @Getter
    @NacosValue(value = "${open.api.chagee.appKey:05d1b467ece1ccfb821639ebfb480a6a}", autoRefreshed = true)
    private String appKey = "05d1b467ece1ccfb821639ebfb480a6a";

    @Getter
    @NacosValue(value = "${open.api.chagee.appSecret:qLibAeV11Z3KZYzoiqLgCyv5o7XH5mOXeo8dAho6GDE}", autoRefreshed = true)
    private String appSecret;


    /**
     * openApi飞书告警群url
     */
    @Getter
    @NacosValue(value = "${open.api.chagee.store.warn.url:https://open.feishu.cn/open-apis/bot/v2/hook/c8ae7b9a-72d8-449c-9297-f1b04032f9a0}", autoRefreshed = true)
    public String openApiChageeStoreWarnUrl;


    public List<Long> getChageeTenantIdList() {
        return this.chageeTenantIdList;
    }

    public boolean judgeIsTenantOfChagee(Long tenantId) {
        return !CollectionUtils.isEmpty(chageeTenantIdList) && chageeTenantIdList.contains(tenantId);
    }
}
