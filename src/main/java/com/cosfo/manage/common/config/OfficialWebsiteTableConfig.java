package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: fansongsong
 * @Date: 2023-12-18
 * @Description:
 */
@Slf4j
@Configuration
@Data
public class OfficialWebsiteTableConfig implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * 预约回电表格appToken
     */
    @NacosValue(value = "${official.website.table.appToken:PUheb2udmaRsU4syqjOcRh4rn7d}", autoRefreshed = true)
    public String appToken;

    /**
     * 预约回电表格tableId
     */
    @NacosValue(value = "${official.website.table.tableId:tblUe5Pptpw7XTSg}", autoRefreshed = true)
    public String tableId;
    /**
     * 预约回电表格fields
     */
    @NacosValue(value = "${official.website.table.fields:{\"fields\":{\"日期\":%d,\"文本\":\"%s\",\"备注\":\"%s\"}}}", autoRefreshed = true)
    public String fields;

//    /**
//     * 正式token-仅测试环境生效
//     */
//    @NacosValue(value = "${official.website.table.devTestToken:}", autoRefreshed = true)
//    public String devTestToken;

//        // 因文档是正式应用创建,因此测试环境使用配置项token验证功能
//        String activeProfiles = Arrays.toString(environment.getActiveProfiles());
//        String devTestToken = officialWebsiteTableConfig.getDevTestToken();
//        if (activeProfiles.contains("dev") && StringUtils.isNotEmpty(devTestToken)) {
//            feiShuToken = devTestToken;
//        }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}:{}", applicationName, this.getClass(), JSON.toJSONString(this));
    }
}
