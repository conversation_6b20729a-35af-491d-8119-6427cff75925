package com.cosfo.manage.common.config.qiniu;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.common.constant.QiNiuConstants;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2024/1/18 10:35
 * @Description:
 */
@Component
@Data
@Slf4j
public class QiNiuNacosConfig {

    @NacosValue(value = "${qiniu.allow.filetype:image/jpeg;image/png;application/pdf}", autoRefreshed = true)
    private String allowFileTyp;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("QiNiuNacosConfig Nacos配置已更新:{}", newConfig);
    }

    /**
     * 创建七牛云上传文件的token
     *
     * @param fileName
     * @param expires
     * @return
     */
    public Map<String, String> createToken(String fileName, long expires) {
        Map<String, String> result = new HashMap();
        Auth auth = Auth.create(QiNiuConstants.ACCESS_KEY, QiNiuConstants.SECRET_KEY);
        // 校验文件是否为真实的图片文件
        StringMap putPolicy = new StringMap();
        putPolicy.put("insertOnly", 1);
        putPolicy.put("mimeLimit", allowFileTyp);
        String token = auth.uploadToken(QiNiuConstants.DEFAULT_BUCKET, fileName, expires, putPolicy);
        result.put("token", token);
        result.put("key", fileName);
        return result;
    }
}
