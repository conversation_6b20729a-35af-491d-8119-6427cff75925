//package com.cosfo.manage.common.factory;
//
//import com.cosfo.manage.order.executor.*;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 14:44
// */
//@Component
//public class OrderAfterSaleFactory {
//
//    @Resource
//    private RefundAfterSaleExecutor refundAfterSaleExecutor;
//
//    @Resource
//    private ReturnRefundAfterSaleExecutor returnRefundAfterSaleExecutor;
//
//    @Resource
//    private ExchangeAfterSaleExecutor exchangeAfterSaleExecutor;
//
//    @Resource
//    private ResendAfterSaleExecutor resendAfterSaleExecutor;
//
//    private Map<Integer, AfterSaleAbstractExecutor> afterSaleAbstractExecutorMap;
//
//    @PostConstruct
//    private void initAfterSaleStrategyMap() {
//        afterSaleAbstractExecutorMap = new HashMap<>(6);
//        afterSaleAbstractExecutorMap.put(1, refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(2, refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(3, returnRefundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(4, returnRefundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(5, exchangeAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(6, resendAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(7, refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(8, returnRefundAfterSaleExecutor);
//    }
//
//    public AfterSaleAbstractExecutor getAfterSaleExecutor(Integer type) {
//        return afterSaleAbstractExecutorMap.get(type);
//    }
//}
