package com.cosfo.manage.common.listener;

import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.constant.MqTopicConstant;
import com.cosfo.manage.common.listener.UpdateTenantStockWarnConfigListener.UpdateTenantStockWarnMessageDTO;
import com.cosfo.manage.report.service.ProductStockForewarningReportService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 更新库存预警配置的mq消息监听类
 *
 * @author: xiaowk
 * @date: 2023/11/4 下午8:02
 */
@Slf4j
@Component
@MqListener(topic = MqTopicConstant.SAAS_ADD_TENANT_INFO,
        consumerGroup = "GID_saas_manage_tenant_stock_warn",
        tag = MqTagConstant.UPDATE_TENANT_STOCK_WARN_TAG
)
public class UpdateTenantStockWarnConfigListener extends AbstractMqListener<UpdateTenantStockWarnMessageDTO> {

    @Resource
    private ProductStockForewarningReportService productStockForewarningReportService;

    @Override
    public void process(UpdateTenantStockWarnMessageDTO msgDTO) {

        if (msgDTO == null || msgDTO.getTenantId() == null) {
            log.warn("rocketmq 收到更新库存预警配置, 消息内容为空");
            return;
        }

        productStockForewarningReportService.refreshSaleAmountPerDay(msgDTO.getTenantId());

    }

    @Data
    public static class UpdateTenantStockWarnMessageDTO {
        /**
         * 租户ID
         */
        private Long tenantId;

    }
}
