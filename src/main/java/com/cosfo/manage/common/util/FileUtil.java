package com.cosfo.manage.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * 文件util
 *
 * <AUTHOR>
 * @date 2020/08/29
 */
@Slf4j
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    private static final int BUFFER_SIZE = 8192;

    private FileUtil() {
    }

    /**
     * 获得文件缓存地址
     *
     * @param suffixName 文件后缀 例如.jpg
     * @return 文件缓存地址
     */
    public static String tempFilePath(String suffixName) {
        return System.getProperty("user.dir") + File.separator + tempFileName(suffixName);
    }

    /**
     * 获得文件临时名称
     *
     * @param suffixName 文件后缀 例如.jpg
     * @return 文件名称
     */
    public static String tempFileName(String suffixName) {
        return System.currentTimeMillis() + suffixName;
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    public static void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            logger.error("删除文件失败", e);
        }
    }

    /**
     * 获得文件后缀
     *
     * @param fileUrl 文件url 例如 http://a3.att.hudong.com/14/75/01300000164186121366756803686.jpg
     * @return 文件后缀 例如 .jpg
     */
    public static String getFileSuffix(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return null;
        }

        int index = fileUrl.lastIndexOf(".");
        if (index == -1) {
            return null;
        }

        return fileUrl.substring(index);
    }

    /**
     * File转MultipartFile
     *
     * @param file
     * @return
     */
    public static MultipartFile fileToMultipartFile(File file) {
        final DiskFileItem item = new DiskFileItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true, file.getName(), 100000000, file.getParentFile());
        try {
            OutputStream os = item.getOutputStream();
            os.write(FileUtils.readFileToByteArray(file));
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }
        return new CommonsMultipartFile(item);
    }



}
