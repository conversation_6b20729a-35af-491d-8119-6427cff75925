package com.cosfo.manage.common.util.binlog.observer;

import java.util.Map;

public interface BinlogObserver {

    default void onInsert(Map<String, String> newData) {
        // default implementation (do nothing)
    }

    default void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        // default implementation (do nothing)
    }

    default void onDelete(Map<String, String> oldData) {
        // default implementation (do nothing)
    }
}
