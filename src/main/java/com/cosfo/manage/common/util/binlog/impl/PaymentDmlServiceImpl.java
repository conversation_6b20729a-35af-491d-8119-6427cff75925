package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.common.context.PaymentEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.market.service.MarketItemOrderSummaryService;
import com.cosfo.manage.merchant.service.MerchantStoreTradeSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component("paymentDmlServiceImpl")
public class PaymentDmlServiceImpl extends TradeDmlServiceImpl {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private MarketItemOrderSummaryService marketItemOrderSummaryService;
    @Resource
    private MerchantStoreTradeSummaryService merchantStoreTradeSummaryService;

    @Override
    protected String getTableName() {
        return DBTableName.COSFO_TABLE_PAYMENT;
    }

    @Override
    protected void generateTradeSummaryForBusiness(Long businessId) {
        log.info("开始处理支付单：[{}]门店汇总数据", businessId);
        merchantStoreTradeSummaryService.generateStoreDimensionPaymentSummary(businessId);
        Payment payment = paymentMapper.selectByPrimaryKey(businessId);
        if (payment == null) {
            log.error("找不到对应的支付单, paymentId: {}", businessId);
            return;
        }
        marketItemOrderSummaryService.generateMarketItemOrderSummaryByPayment(payment);
    }

    @Override
    protected boolean shouldGenerateTradeSummary(Integer paymentStatus) {
        return Objects.equals(paymentStatus, PaymentEnum.Status.SUCCESS.getCode());
    }

    @Override
    protected String getChangedFiled() {
        return "status";
    }

}
