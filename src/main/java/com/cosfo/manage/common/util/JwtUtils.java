//package com.cosfo.manage.common.util;
//
//import io.jsonwebtoken.Jwts;
//import io.jsonwebtoken.SignatureAlgorithm;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.util.PathMatcher;
//
//import javax.servlet.http.HttpServletRequest;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @create 2020/11/15 14:12
// */
//public class JwtUtils {
//    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);
//    public static final long EXPIRATION_TIME = 3 * 60 * 60 * 1000;// 令牌环有效期
//
//    public static final String SECRET = "abc123456def";//令牌环密钥
//    public static final String TOKEN_PREFIX = "cosfo-manage_";//令牌环头标识
//    public static final String HEADER_STRING = "token";//配置令牌环在http heads中的键值
//    public static final String TENANTID = "tenantId";//自定义字段-租户字段
//    public static final String PHONE = "phone";
//    public static final String ACCESS_TOKEN = "ACCESS_TOKEN";
//    public static final String[] protectUrlPattern = {"/login", "/ok", "/weixin/authEvent",
//            "/weixin/tenant/authcallback", "/weixin/{appId}/callback", "/system/*", "/template/*", "/template/*/*",
//            "/tenantAgreement/listAll", "/merchant/store/export-template", "/productSpu/upsert/deal-agent-sku", "/storeBill/deal/history-bill"}; //哪些请求不需要进行安全校验
//    private static final PathMatcher pathmatcher = new AntPathMatcher();
//
//    /**
//     * 商城令牌环头标识
//     */
//    public static final String MALL_TOKEN_PREFIX = "cosfo-mall_";
//
//    //生成令牌环
//    public static String generateToken(Long tenantId, String phone, String accessToken) {
//        HashMap<String, Object> map = new HashMap<>();
//        map.put(TENANTID, tenantId);
//        map.put(PHONE, phone);
//        map.put(ACCESS_TOKEN,accessToken);
//        String jwt = Jwts.builder()
//                .setClaims(map)
//                .signWith(SignatureAlgorithm.HS512, SECRET)
//                .compact();
//        return TOKEN_PREFIX + " " + jwt;
//    }
//
//    //生成令牌环
//    public static String generateToken(String tenantId, String phone, long exprationtime) {
//        HashMap<String, Object> map = new HashMap<>();
//        map.put(TENANTID, tenantId);
//        map.put(PHONE, phone);
//        String jwt = Jwts.builder()
//                .setClaims(map)
//                .setExpiration(new Date(System.currentTimeMillis() + exprationtime))
//                .signWith(SignatureAlgorithm.HS512, SECRET)
//                .compact();
//        return TOKEN_PREFIX + " " + jwt;
//    }
//
//    //令牌环校验
//    public static Map<String, Object> validateTokenAndGetClaims(String token) {
//        Map<String, Object> body = Jwts.parser()
//                .setSigningKey(SECRET)
//                .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
//                .getBody();
//        return body;
//    }
//
//    //是否是要过滤接口
//    public static boolean isProtectedUrl(HttpServletRequest request) {
//        for (int i = 0; i < protectUrlPattern.length; i++) {
//            if (pathmatcher.match(protectUrlPattern[i], request.getServletPath())) {
//                return false;
//            }
//        }
//        return true;
//    }
//}
