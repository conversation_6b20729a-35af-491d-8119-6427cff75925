package com.cosfo.manage.common.util.transfer;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 14:25
 */
//public class ProductCategoryTransferUtil {
//
//    private ProductCategoryTransferUtil() {
//    }
//
//    public static ProductCategoryTreeDTO toTreeDTO(ProductCategory productCategory) {
//        ProductCategoryTreeDTO treeDTO = ProductCategoryTreeDTO.builder().id(productCategory.getId()).name(productCategory.getName()).parentId(productCategory.getParentId()).build();
//        return treeDTO;
//    }
//
//    public static List<ProductCategoryTreeDTO> toTreeDTO(List<ProductCategory> productCategories) {
//        return productCategories.stream().map(ProductCategoryTransferUtil::toTreeDTO).collect(Collectors.toList());
//    }
//}
