package com.cosfo.manage.common.util.binlog.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.common.util.binlog.DtsModelHandler;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/11/2 上午12:15
 */
@Slf4j
@Component
public class ProductPricingSupplyDmlService implements DbTableDmlService {


    @Resource
    private ProductStockForewarningReportRepository productStockForewarningReportRepository;


    @Override
    public String getTableDmlName() {
        return DBTableName.COSFO_PRODUCT_PRICING_SUPPLY;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {

        // 根据报价单变动更新库存预警表，只处理删除，新增在报价城市表监听处理
        if (Objects.equals(BinlogEventEnum.INSERT.getEvent(), dtsModelBo.getType())) {

        } else if (Objects.equals(BinlogEventEnum.DELETE.getEvent(), dtsModelBo.getType())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelBo);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                Long skuId = Long.valueOf(oldMap.get("supply_sku_id"));
                Long tenantId = Long.valueOf(oldMap.get("tenant_id"));
                handleStockWarning(skuId, tenantId);
            }

        }

    }


    private void handleStockWarning(Long skuId, Long tenantId) {
        productStockForewarningReportRepository.deleteByTenantIdAndSkuId(tenantId, skuId);
    }

}
