package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.util.binlog.observer.BinlogObserver;
import com.cosfo.manage.common.util.binlog.observer.productstock.ProductStockObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ProductStockDmlServiceImpl extends DbTableDmlServiceImpl {

    @Resource
    private List<ProductStockObserver> productStockObservers;

    @Override
    public String getTableDmlName() {
        return DBTableName.COSFO_TABLE_PRODUCT_STOCK_FOREWARNING_REPORT;
    }

    @Override
    protected List<? extends BinlogObserver> getObservers() {
        return productStockObservers;
    }
}
