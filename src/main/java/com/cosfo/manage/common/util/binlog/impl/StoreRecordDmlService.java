package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductPricingSupplyMapper;
import com.cosfo.manage.report.model.dto.ProductStockChangeDTO;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import com.cosfo.manage.report.service.ProductStockForewarningReportService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> fss
 * create at:  2023/05/18
 */
@Slf4j
@Component
public class StoreRecordDmlService implements DbTableDmlService {

    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductAgentStockReportService productAgentStockReportService;
    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;
    @Resource
    private ProductStockForewarningReportService productStockForewarningReportService;
    @Resource
    private ProductFacade productFacade;

    @Override
    public String getTableDmlName() {
        return DBTableName.QUANTITY_CHANGE_RECORD;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.INSERT.getEvent(), dtsModelBo.getType())) {
            return;
        }
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

//        try {
//            dtsModelBo.consumerData(map -> {
//                // store_record主键
//                String storeRecordId = map.get("id");
//                if (StringUtils.isEmpty(storeRecordId)) {
//                    log.info("监听quantity_change_record变动传入id为空，过滤消息");
//                    return;
//                }
//
//                int newOnlineQuantity = Integer.parseInt(map.get("new_online_quantity"));
//                int oldOnlineQuantity = Integer.parseInt(map.get("old_online_quantity"));
//                // sku编码、仓库编号
//                String sku = map.get("sku");
//                int areaNo = Integer.parseInt(map.get("area_no"));
//                if (newOnlineQuantity == oldOnlineQuantity) {
//                    log.info("监听binlog数据忽略,新老在线库存未发生变更 sku={},warehouseNo={},storeRecordId={},msgKey={}", sku, areaNo, storeRecordId, dtsModelBo.getMsgKey());
//                }
//                // 不存在相应鲜沐报价单，return
//                ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByAgentTenantInfo(XianmuSupplyTenant.TENANT_ID, sku);
//                if (Objects.isNull(productAgentSkuMapping)) {
//                    log.info("监听binlog数据忽略,不存在相应鲜沐报价单 sku={},warehouseNo={},storeRecordId={},msgKey={}", sku, areaNo, storeRecordId, dtsModelBo.getMsgKey());
//                    return;
//                }
//                log.info("监听库存变更消息,开始进行库存同步 sku={},warehouseNo={},storeRecordId={},msgKey={}", sku, areaNo, storeRecordId, dtsModelBo.getMsgKey());
//                productAgentStockReportService.stockChangeSync(productAgentSkuMapping, areaNo);
//
//                log.info("监听binlog插入完成 sku={},warehouseNo={},storeRecordId={},msgKey={}", sku, areaNo, storeRecordId, dtsModelBo.getMsgKey());
//            });
//        } catch (Exception e) {
//            log.error("旧库存预警执行错误", e);
//        }

        processProductStockWarning(dtsModelBo);
    }

    private void processProductStockWarning(DtsModelBO dtsModelBo) {
        dtsModelBo.consumerData(map -> {
            // store_record主键
            String storeRecordId = map.get("id");
            if (StringUtils.isEmpty(storeRecordId)) {
                log.info("监听quantity_change_record变动传入id为空，过滤消息");
                return;
            }

            int newOnlineQuantity = Integer.parseInt(map.get("new_online_quantity"));
            int oldOnlineQuantity = Integer.parseInt(map.get("old_online_quantity"));
            // sku编码、仓库编号
            String sku = map.get("sku");
            int warehouseNo = Integer.parseInt(map.get("area_no"));
            if (newOnlineQuantity == oldOnlineQuantity) {
                log.info("监听binlog数据忽略,新老在线库存未发生变更 sku={},warehouseNo={},storeRecordId={},msgKey={}", sku, warehouseNo, storeRecordId, dtsModelBo.getMsgKey());
            }

            handleStockWarning(sku, warehouseNo, newOnlineQuantity);

        });
    }

    public void handleStockWarning(String sku, int warehouseNo, int newOnlineQuantity) {

        List<ProductsMappingResp> productAgentSkuMappingList = productFacade.queryBySkuCodes(Collections.singletonList(sku));
        if (CollectionUtils.isEmpty(productAgentSkuMappingList)) {
            return;
        }

        ProductsMappingResp productAgentSkuMapping = productAgentSkuMappingList.get(0);
        if (productAgentSkuMapping == null) {
            return;
        }

        ProductStockChangeDTO productStockChangeDTO = new ProductStockChangeDTO();
        productStockChangeDTO.setTenantId(productAgentSkuMapping.getTenantId());
        productStockChangeDTO.setSkuId(productAgentSkuMapping.getSkuId());
        productStockChangeDTO.setSkuCode(productAgentSkuMapping.getSku());
        productStockChangeDTO.setWarehouseNo(warehouseNo);
        productStockChangeDTO.setNewOnlineQuantity(newOnlineQuantity);


        Long tenantId = productAgentSkuMapping.getTenantId();
        // 报价货品
        if (tenantId == 1) {
            // 更新所有存在报价单的租户的相应sku货品的实时库存
            productStockChangeDTO.setGoodsType(GoodsTypeEnum.QUOTATION_TYPE.getCode());

            productStockForewarningReportService.updateStockWarnBySupplySku(productStockChangeDTO);

        } else {
            // 自营货品
            productStockChangeDTO.setGoodsType(GoodsTypeEnum.SELF_GOOD_TYPE.getCode());

            productStockForewarningReportService.updateStockWarn(productStockChangeDTO);
        }


    }
}
