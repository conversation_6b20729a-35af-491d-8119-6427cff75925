package com.cosfo.manage.common.util;


import com.cosfo.common.util.TimeUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Package: com.manageSystem.contexts
 * @Description: 系统常用参数
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
public class Global {

    /**
     * 普通订单业务编号
     */
    public static final String NORMAL_ORDER_CODE = "OR";

    /**
     * 售后订单业务编号
     */
    public static final String NORMAL_ORDER_AFTER_SALE_CODE = "AS";

    /**
     * 账单文件
     */
    public static final String BILL = "FB";

    /**
     * 汇付支付流水号前缀
     */
    public static final String HUIFU_PAY_CODE = "HP";

    /**
     * 汇付用户信息查询流水号前缀
     */
    public static final String HUIFU_USER_CODE = "HPU";

    /**
     * 汇付流水号时间格式
     */
    public static final String HUIFU_FORMAT = "yyyyMMddHHmmss";

    public static final String HUIFU_PAY_SETTLE = "HP_SETTLE";

    /**
     * 生成订单号
     *
     * @param orderCode
     * @return
     */
    public static String createOrderNo(String orderCode) {
        return orderCode + System.currentTimeMillis() + StringUtils.orderRandomNum();
    }


    /**
     * 生成格式一个编号
     * 生成格式：前缀+时间戳+N位随机数
     *
     * @param prefix 前缀
     * @param digits N位随机数
     * @return
     */
    public static String generateTimeCode(String prefix, Integer digits) {
        return prefix + System.currentTimeMillis() + StringUtils.generateRandomNumberStr(digits);
    }

    /**
     * 生成一批编号
     * 生成格式：前缀+时间戳+N位随机数
     *
     * @param prefix     前缀
     * @param digits     N位随机数
     * @param batchCount 生成随机数数量
     * @return
     */
    public static List<String> generateBatchTimeCode(String prefix, Integer digits, Integer batchCount) {
        List<String> numberStrList = StringUtils.generateBatchRandomNumberStr(batchCount, digits);
        long timeMillis = System.currentTimeMillis();
        return numberStrList.stream().map(num -> prefix + timeMillis + num).collect(Collectors.toList());

    }


    /**
     * 生成汇付流水号
     *
     * @param huifuCode
     * @return
     */
    public static String createHuiFuNo(String huifuCode) {
        return huifuCode + TimeUtils.changeDate2String(new Date(), HUIFU_FORMAT) + StringUtils.orderRandomNum();
    }

    public static String subSpecification(String specification) {
        if (StringUtils.isBlank(specification)) {
            return specification;
        }
        if (specification.startsWith("0_") || specification.startsWith("1_")) {
            // 如果是，则去掉开头的两个字符
            return specification.substring(2);
        }
        return specification;
    }
}
