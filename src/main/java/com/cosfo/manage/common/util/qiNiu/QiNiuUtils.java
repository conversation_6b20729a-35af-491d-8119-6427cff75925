package com.cosfo.manage.common.util.qiNiu;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.config.qiniu.QiNiuConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.util.FileUtil;
import com.cosfo.manage.common.util.StringUtils;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import lombok.Synchronized;
import com.qiniu.storage.model.BatchStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.Random;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/27
 */
@Component
@Slf4j
public class QiNiuUtils {

    @Autowired
    private QiNiuConfig config;
    private static QiNiuConfig qiNiuConfig;

    private static Random random = new Random();

    //初始化静态参数
    //通过@PostConstruct实现初始化bean之前进行的操作
    @PostConstruct
    public void init() {
        qiNiuConfig = config;
    }

    // 上传图片
    public static String uploadFile(MultipartFile file, String fileName) throws IOException {
        try {
            // 调用put方法上传
            Response res = qiNiuConfig.getUploadManager().put(file.getBytes(), fileName, qiNiuConfig.getUpToken());
            // 打印返回的信息
            if (res.isOK() && res.isJson()) {
                // 返回这张存储照片的地址
                return JSONObject.parseObject(res.bodyString()).get("key").toString();

            } else {
                log.error("七牛云上传异常:" + res.bodyString());
                return null;
            }
        } catch (QiniuException e) {
            // 请求失败时打印的异常的信息
            log.error("七牛云上传异常:" + e.getMessage());
            return null;
        }
    }

    // 复制图片
    public static void copyFileBatch(String[] keyList) {
        try {
            //单次批量请求的文件数量不得超过1000
            BucketManager.BatchOperations batchOperations = new BucketManager.BatchOperations();
            for (String key : keyList) {
                batchOperations.addCopyOp(qiNiuConfig.getXM_BUCKETNAME (), key, qiNiuConfig.getBucketname (), key);
            }
            Response response = qiNiuConfig.getBucketManager ().batch(batchOperations);
            BatchStatus[] batchStatusList = response.jsonToObject(BatchStatus[].class);
            for (int i = 0; i < keyList.length; i++) {
                BatchStatus status = batchStatusList[i];
                String key = keyList[i];
                if (status.code == 200) {
                    log.info (key +" copy success");
                } else {
                    log.error (key + "copy错误，错误信息=" +status.data.error);
                }
            }
        } catch (QiniuException ex) {
            log.error ("七牛云copy异常:" + ex.response,ex);
        }
    }


    /**
     * 上传文件
     *
     * @param localFilePath
     * @param fileName
     * @return
     * @throws IOException
     */
    public static String uploadFile(String localFilePath, String fileName) throws IOException {
        try {
            Response response = qiNiuConfig.getUploadManager().put(localFilePath, Constants.FILE_DIR + fileName, qiNiuConfig.getUpToken());
            // 打印返回的信息
            if (response.isOK() && response.isJson()) {
                // 返回这张存储照片的地址
                return JSONObject.parseObject(response.bodyString()).get("key").toString();
            }
        } catch (Exception e) {
            log.error("上传文件至七牛云失败, fileName={}, errmsg={}", fileName, e.getMessage());
        }
        return null;
    }

    /**
     * 删除文件
     * @param keyList
     * @throws IOException
     */
    public static void deleteFile(String[] keyList) {
        try {
            //单次批量请求的文件数量不得超过1000
            if (keyList.length > NumberConstants.ONE_THOUSAND) {
                throw new DefaultServiceException("单次批量删除请求的文件数量不得超过1000");
            }
            String bucketName = qiNiuConfig.getBucketname();
            BucketManager bucketManager = qiNiuConfig.getBucketManager();
            BucketManager.BatchOperations batchOperations = new BucketManager.BatchOperations();
            batchOperations.addDeleteOp(bucketName, keyList);
            Response response = bucketManager.batch(batchOperations);
            if (!response.isOK()) {
                String info = response.getInfo();
                log.error("文件删除失败{}", info);
                throw new DefaultServiceException("文件删除失败");
            }
        } catch (Exception e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }
    }

    /**
     * qiniu云文件名默认 = 【18位字母+数字】+ 【原文件名后缀】
     * @param fileName
     * @return
     */
    @Synchronized
    public static String generateUploadName(String fileName){
        String fileSuffix = FileUtil.getFileSuffix(fileName);
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 18; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString() + fileSuffix;
    }

}
