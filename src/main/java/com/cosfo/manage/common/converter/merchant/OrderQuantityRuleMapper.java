package com.cosfo.manage.common.converter.merchant;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDTO;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleJsonDTO;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderQuantityRuleMapper {

    OrderQuantityRuleMapper INSTANCE = Mappers.getMapper(OrderQuantityRuleMapper.class);

    /**
     * ruleList to dtoList
     *
     * @param ruleList
     * @return
     */
    List<OrderQuantityRuleDTO> toRuleDTOList(List<MerchantOrderQuantityRule> ruleList);

    /**
     * ruleList to voList
     *
     * @param ruleList
     * @return
     */
    List<OrderQuantityRuleVO> toRuleVOList(List<MerchantOrderQuantityRule> ruleList);

    /**
     * role to dto
     *
     * @param rule
     * @return
     */
    OrderQuantityRuleDTO ruleToDTO(MerchantOrderQuantityRule rule);

    /**
     * rule to vo
     *
     * @param rule
     * @return
     */
    OrderQuantityRuleVO ruleToVo(MerchantOrderQuantityRule rule);

    /**
     * dto to vo
     *
     * @param dto
     * @return
     */
    OrderQuantityRuleVO toVO(OrderQuantityRuleDTO dto);

    /**
     * dtoList to voList
     *
     * @param dtoList
     * @return
     */
    List<OrderQuantityRuleVO> toVOList(List<OrderQuantityRuleDTO> dtoList);

    /**
     * dto to rule
     *
     * @param ruleDTO
     * @return
     */
    MerchantOrderQuantityRule dtoToRule(OrderQuantityRuleDTO ruleDTO, @Context Long tenantId);

    /**
     * dto list to rule list
     *
     * @param ruleDTOList
     * @return
     */
    List<MerchantOrderQuantityRule> dtoToRuleList(List<OrderQuantityRuleDTO> ruleDTOList, @Context Long tenantId);

    /**
     * 处理 json
     *
     * @param ruleDTO
     */
    @AfterMapping
    default void handleRuleJson(@MappingTarget OrderQuantityRuleDTO ruleDTO) {
        String rule = ruleDTO.getRule();
        OrderQuantityRuleJsonDTO json = JSON.parseObject(rule, OrderQuantityRuleJsonDTO.class);
        ruleDTO.setOperator(json.getOp());
        ruleDTO.setRuleDetailList(json.getRule());
    }

    /**
     * 处理vo json
     *
     * @param ruleVO
     */
    @AfterMapping
    default void handleRuleJson(@MappingTarget OrderQuantityRuleVO ruleVO) {
        String rule = ruleVO.getRule();
        OrderQuantityRuleJsonDTO json = JSON.parseObject(rule, OrderQuantityRuleJsonDTO.class);
        ruleVO.setOperator(json.getOp());
        ruleVO.setRuleDetailList(json.getRule());
    }

    @AfterMapping
    default void handleTenant(@MappingTarget MerchantOrderQuantityRule rule, @Context Long tenantId) {
        if (rule != null) {
            rule.setTenantId(tenantId);
        }

    }


    /**
     * 处理 rule
     *
     * @param ruleDTO
     */
    @BeforeMapping
    default void beforeMappingTest(OrderQuantityRuleDTO ruleDTO) {
        if (ruleDTO == null) {
            return;
        }
        OrderQuantityRuleJsonDTO jsonDTO = new OrderQuantityRuleJsonDTO();
        jsonDTO.setOp(ruleDTO.getOperator());
        jsonDTO.setRule(ruleDTO.getRuleDetailList());
        ruleDTO.setRule(JSON.toJSONString(jsonDTO));
    }
}
