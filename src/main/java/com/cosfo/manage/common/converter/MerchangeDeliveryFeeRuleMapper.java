package com.cosfo.manage.common.converter;

import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantDeliveryFeeSubRuleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-04-15
 * @Description:
 */
@Mapper
public interface MerchangeDeliveryFeeRuleMapper {

    MerchangeDeliveryFeeRuleMapper INSTANCE = Mappers.getMapper(MerchangeDeliveryFeeRuleMapper.class);

    /**
     * sub转rule
     * @param dto
     * @return
     */
    MerchantDeliveryFeeRuleDTO subDtoToRuleDto(MerchantDeliveryFeeSubRuleDTO dto);

    /**
     * rule转sub
     * @param dto
     * @return
     */
    MerchantDeliveryFeeSubRuleDTO ruleDtoToSubDto(MerchantDeliveryFeeRuleDTO dto);

}
