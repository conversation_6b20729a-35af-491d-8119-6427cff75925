package com.cosfo.manage.common.converter;

import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import net.summerfarm.wms.saleinventory.dto.req.QueryAreaStoreQuantityReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-11-20
 * @Description:
 */
@Mapper
public interface QueryAreaStoreQuantityReqMapper {

    QueryAreaStoreQuantityReqMapper INSTANCE = Mappers.getMapper(QueryAreaStoreQuantityReqMapper.class);

    QueryAreaStoreQuantityReq inputToReq(SummerfarmAgentSkuWarehouseDataInput input);

}
