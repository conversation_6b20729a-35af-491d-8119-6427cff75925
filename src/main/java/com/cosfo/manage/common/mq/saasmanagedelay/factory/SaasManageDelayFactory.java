package com.cosfo.manage.common.mq.saasmanagedelay.factory;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2024/2/23 09:36
 * @Description:
 */
@Component
public class SaasManageDelayFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 获取实现
     */
    private static Collection<SaasManageDelayService> creatorList;

    /**
     * 通过消息体类型,获取实现类对象
     *
     * @param msgType 消息体类型
     * @return 实现类对象
     */
    public SaasManageDelayService creator(String msgType) {
        if (CollectionUtils.isEmpty(creatorList)) {
            creatorList = applicationContext
                .getBeansOfType(SaasManageDelayService.class)
                .values();
        }
        return creatorList.stream().filter(x -> Objects.equals(x.getTagName(), msgType))
            .findFirst()
            .orElse(null);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        creatorList = applicationContext.getBeansOfType(SaasManageDelayService.class).values();
    }
}
