package com.cosfo.manage.common.easy.excel.helper;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * excel 级联下拉框 handler
 */
public class ChainDropDownWriteHandler {

    public static void generateDataValidation(Workbook workbook, XSSFSheet sheet, Map<Integer, ChainDropDown> map) {
//        DataValidationHelper helper = sheet.getDataValidationHelper();
        XSSFDataValidationHelper helper = new XSSFDataValidationHelper(sheet);

        for (Map.Entry<Integer, ChainDropDown> e : map.entrySet()) {
            // k 为存在下拉数据集的单元格下标 v为下拉数据集
            Integer k = e.getKey();
            ChainDropDown v = e.getValue();
            CellRangeAddressList rangeList = new CellRangeAddressList(1, 65536, k, k);
            String hideSheetName = v.getTypeName();
            Sheet hideSheet = getSheet(workbook, hideSheetName);
            workbook.setSheetHidden(workbook.getSheetIndex(hideSheet), true);
            if (v.isRootFlag()) {
                Row firstRow = hideSheet.createRow(v.getRowIndex());
                List<String> values = v.getDataMap().get(ChainDropDown.ROOT_KEY);
                for (int i = 0; i < values.size(); i++) {
                    Cell rowCell = firstRow.createCell(i);
                    rowCell.setCellValue(values.get(i));
                }

                String listFormula = "%s!$A$1:$%s$1";
                listFormula = String.format(listFormula, hideSheetName, getColNum(values.size()));
                DataValidationConstraint constraint = helper.createFormulaListConstraint(listFormula);
                // 设置下拉约束
                DataValidation validation = helper.createValidation(constraint, rangeList);
                // 阻止输入非下拉选项的值
                validation.setErrorStyle(DataValidation.ErrorStyle.WARNING);
                validation.setShowErrorBox(true);
                validation.setSuppressDropDownArrow(true);
                validation.createErrorBox("提示", "此值与单元格定义格式不一致");
                sheet.addValidationData(validation);

            } else {
                Integer rowIndex = v.getRowIndex();
                Map<String, List<String>> dataMap = v.getDataMap();

                for (Map.Entry<String, List<String>> entry : dataMap.entrySet()) {
                    String parentValue = entry.getKey();
                    List<String> childValues = entry.getValue();
                    Row row = hideSheet.createRow(rowIndex++);
                    row.createCell(0).setCellValue(parentValue);
                    for (int j = 0; j < childValues.size(); j++) {
                        Cell cell = row.createCell(j + 1);
                        cell.setCellValue(childValues.get(j));
                    }

                }

                for (int i = 2; i <= 2000; i++) {
                    // 从第二行开始，第一行是标题
                    int beginRow = i;

                    // 设置offset公式
                    String column = getColNum(k - 1);
                    int row = beginRow;
                    String listFormula = getListFormula(column, row, hideSheetName);

                    DataValidationConstraint formulaListConstraint = helper.createFormulaListConstraint(listFormula);
                    rangeList = new CellRangeAddressList(i - 1, i - 1, k, k);
                    // 设置下拉约束
                    DataValidation validation = helper.createValidation(formulaListConstraint, rangeList);
                    validation.setEmptyCellAllowed(true);
                    validation.setSuppressDropDownArrow(true);
                    validation.setShowErrorBox(true);
                    // 设置输入信息提示信息
                    validation.createPromptBox("下拉选择提示", "请使用下拉方式选择合适的值！");
                    sheet.addValidationData(validation);
                }
            }

        }
    }

    public static String getListFormula(String column, int row, String sheetName) {
        String listFormula = "OFFSET(" + sheetName + "!$A$1, MATCH($%s$%s, " + sheetName + "!$A$2:$A$1000, 0), 1, 1, COUNTA(OFFSET(" + sheetName + "!$A$1, MATCH($%s$%s, " + sheetName
                + "!$A$2:$A$1000, 0), 1, 1, 200)))";
        return String.format(listFormula, column, row, column, row);
    }

    public static Sheet getSheet(Workbook workbook, String sheetName) {
        Sheet sheet = workbook.getSheet(sheetName);
        if (!ObjectUtils.isEmpty(sheet)) {
            return sheet;
        }
        return workbook.createSheet(sheetName);
    }

    /**
     *  计算formula
     * @param offset 偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowNum 第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     *
     */
    public static String getRange(int offset, int rowNum, int colCount) {
        String start = getColNum(offset);
        String end = getColNum(colCount);
        String format = "$%s$%s:$%s$%s";
        return String.format(format, start, rowNum, end, rowNum);
    }


    /**
     * 获取Excel列的号码A-Z - AA-ZZ - AAA-ZZZ 。。。。
     * @param num
     * @return
     */
    public static String getColNum(int num) {
        int MAX_NUM = 26;
        char initChar = 'A';
        if (num == 0) {
            return initChar + "";
        } else if (num > 0 && num < MAX_NUM) {
            int result = num % MAX_NUM;
            return (char) (initChar + result) + "";
        } else if (num >= MAX_NUM) {
            int result = num / MAX_NUM;
            int mod = num % MAX_NUM;
            String starNum = getColNum(result - 1);
            String endNum = getColNum(mod);
            return starNum + endNum;
        }
        return "";
    }


}
