package com.cosfo.manage.common.sms.model;

import com.cosfo.manage.common.context.SenderPlatformEnum;
import com.cosfo.manage.common.sms.SmsSender;
import com.cosfo.manage.common.sms.impl.ChuangLanSmsSender;
import com.cosfo.manage.common.sms.impl.LocalSmsSender;
import com.cosfo.manage.system.mapper.SystemParametersMapper;
import com.cosfo.manage.system.model.po.SystemParameters;
import lombok.Setter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/21
 */
@Component
@Setter
public class SmsSenderFactory {

    public static final String SMS_PLATFORM = "SmsPlatform";

    @Resource
    private ChuangLanSmsSender chuangLanSMSSender;
    @Resource
    private LocalSmsSender localSMSSender;

    @Resource
    private SystemParametersMapper systemParametersMapper;

    /**
     * 拿到具体的Sender
     * @return
     */
    public SmsSender getSmsSender() {
        // 查询在系统参数表配置的SMS平台
        SystemParameters systemParameters = systemParametersMapper.selectByKey(SMS_PLATFORM);
        String smsPlatform = systemParameters.getParamValue();
        SenderPlatformEnum[] values = SenderPlatformEnum.values();
        SenderPlatformEnum type = SenderPlatformEnum.values()[Integer.valueOf(smsPlatform)];
        switch (type) {
            case LOCAL_SMS:
                return localSMSSender;
            default:
                return chuangLanSMSSender;
        }
    }

}
