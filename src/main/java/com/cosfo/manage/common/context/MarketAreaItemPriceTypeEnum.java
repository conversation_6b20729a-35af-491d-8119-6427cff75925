package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 商城商品售价策略
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/30
 */
@Getter
@AllArgsConstructor
public enum MarketAreaItemPriceTypeEnum {
    /**
     * 错误类型
     */
    ERROR(-1, "定价类型错误"),
    /**
     * 所有门店展示并统一定价
     */
    ALL_STORE_UNIFIED_PRICE(0,"所有门店展示并统一定价"),
    /**
     * 所有门店展示但差异化定价
     */
    ALL_STORE_DIFFERENCES_PRICE(1,"所有门店展示但差异化定价"),
    /**
     *
     */
    PART_STORE_DIFFERENCES_PRICE(2,"所有门店展示但差异化定价");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    public static MarketAreaItemPriceTypeEnum getPriceTypeEnum(Integer code) {
        for (MarketAreaItemPriceTypeEnum marketAreaItemPriceTypeEnum : MarketAreaItemPriceTypeEnum.values()) {
            if (marketAreaItemPriceTypeEnum.code.equals(code)) {
                return marketAreaItemPriceTypeEnum;
            }
        }

        return MarketAreaItemPriceTypeEnum.ERROR;
    }
}
