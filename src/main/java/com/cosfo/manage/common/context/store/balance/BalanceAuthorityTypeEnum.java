package com.cosfo.manage.common.context.store.balance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/19 9:45
 */
@Getter
@AllArgsConstructor
public enum BalanceAuthorityTypeEnum {

    /**
     * 关闭余额权限
     */
    CLOSE_BALANCE_AUTH(0, "关闭"),

    /**
     * 开通余额权限
     */
    OPEN_BALANCE_AUTH(1, "开启");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static BalanceAuthorityTypeEnum getByType(Integer type){
        for(BalanceAuthorityTypeEnum balanceAuthorityEnum: BalanceAuthorityTypeEnum.values()){
            if(balanceAuthorityEnum.getType().equals(type)){
                return balanceAuthorityEnum;
            }
        }
        return null;
    }

    public static String getDesc(Integer type) {
        for (BalanceAuthorityTypeEnum value : BalanceAuthorityTypeEnum.values()) {
            if (Objects.equals(value.getType (), type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
