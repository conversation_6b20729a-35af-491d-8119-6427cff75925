package com.cosfo.manage.common.context;

import com.cosfo.manage.order.model.po.Order;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单枚举类
 *
 * <AUTHOR>
 * @date 2022/5/15
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    /**
     * 下单中
     */
    CREATING_ORDER(1,"下单中"),
    /**
     * 待支付
     */
    NO_PAYMENT(2,"待支付"),
    /**
     * 已支付
     */
    WAIT_DELIVERY(3,"已支付"),
    /**
     * 待收货
     */
    DELIVERING(4,"待收货"),
    /**
     * 已完成
     */
    FINISHED(5,"已完成"),
    /**
     * 已取消
     */
    CANCELED(6,"已取消"),
    /**
     * 已退款
     */
    REFUNDED(7,"已退款"),
    /**
     * 关单中
     */
    CLOSING(8,"关单中"),
    /**
     * 已关闭
     */
    CLOSED(9, "已关闭"),

    /**
     * 3 -> 10  -> 4 等待出库
     */
    WAITING_DELIVERY(10,"待配送"),

    /**
     *  无仓订单部分配送时，会更新为当前状态
     *  3 -> 10 -> 11  -> 4 部分配送
     */
    SEGMENT_WAITING_DELIVERY(11,"部分配送"),
    /**
     * 出库中 自营仓订单 3 -> 10 -> 12 ->  4
     */
    OUT_OF_STORAGE(12,"出库中"),

    /**
     * 待审核 支付成功后，如果需要审核更新为13，不需要审核更新为10
     */
    WAIT_AUDIT(13,"待审核");
    ;

    /**
     * 订单状态编码
     */
    private Integer code;
    /**
     * 订单状态描述
     */
    private String desc;

    public static String getDesc(Integer code) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.code.equals(code)) {
                return orderStatusEnum.desc;
            }
        }

        return null;
    }

    /**
     * 能否发起售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyAfterSale(Integer status){
        if(WAIT_DELIVERY.getCode().equals(status) || DELIVERING.getCode().equals(status) || FINISHED.getCode().equals(status) || WAITING_DELIVERY.getCode().equals(status)
                || SEGMENT_WAITING_DELIVERY.getCode().equals(status) || OUT_OF_STORAGE.getCode().equals(status)){
            return true;
        }

        return false;
    }

    /**
     * 能否发起配送前售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyNotSendAfterSale(Integer status){
        if(WAIT_DELIVERY.getCode().equals(status) ||  WAITING_DELIVERY.getCode().equals(status)){
            return true;
        }

        return false;
    }

    /**
     * 能否发起配送后售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyDeliveredAfterSale(Integer status){
        if(DELIVERING.getCode().equals(status) || FINISHED.getCode().equals(status)
                || SEGMENT_WAITING_DELIVERY.getCode().equals(status) || OUT_OF_STORAGE.getCode().equals(status)){
            return true;
        }

        return false;
    }

    public static void transferOrderStatus(OrderVO orderVO){
        if (Objects.isNull(orderVO)) {
            return;
        }

        orderVO.setStatus(transferOrderStatus(orderVO.getStatus()));
    }

    public static void transferOrderStatus(Order order){
        if (Objects.isNull(order)) {
            return;
        }

        order.setStatus(transferOrderStatus(order.getStatus()));
    }

    public static void transferOrderStatus(OrderDTO order){
        if (Objects.isNull(order)) {
            return;
        }

        order.setStatus(transferOrderStatus(order.getStatus()));
    }

    public static void transferOrderStatus(OrderResp order){
        if (Objects.isNull(order)) {
            return;
        }

        order.setStatus(transferOrderStatus(order.getStatus()));
    }

    public static Integer transferOrderStatus(Integer statusCode){
        if (Objects.isNull(statusCode)) {
            return null;
        }
        // 部分配送、等待出库都是等待出库
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(statusCode) || OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode().equals(statusCode)) {
            return OrderStatusEnum.WAITING_DELIVERY.getCode();
        }
        // 出库中 自营仓订单也是待收货
        if (OrderStatusEnum.OUT_OF_STORAGE.getCode().equals(statusCode)) {
            return OrderStatusEnum.DELIVERING.getCode();
        }

        return statusCode;
    }

    /**
     * 是否是待配送状态
     * @param status
     * @return
     */
    public static boolean isWaitStatusOrder(Integer status) {
        if (Objects.isNull(status)) {
            return false;
        }
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(status) || OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode().equals(status)) {
            return true;
        }
        return false;
    }
}
