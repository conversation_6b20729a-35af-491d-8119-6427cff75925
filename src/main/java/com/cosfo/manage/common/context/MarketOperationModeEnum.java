package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description
 * <AUTHOR>
 * @date 2022/10/6 11:44
 */
@Getter
@AllArgsConstructor
public enum MarketOperationModeEnum {

    /**
     * 自营-品牌方配送
     */
    SELF_RUN_SELF_DELIVERY(1, "自营-品牌方配送"),

    /**自营-三方配送
     *
     */
    SELF_RUN_Third_DELIVERY(2, "自营-三方配送"),

    /**
     * "经销-三方配送"
     */
    THIRD_RUN_Third_DELIVERY(3, "经销-三方配送");


    /**
     * type
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    /**
     * get desc
     * @param warehouseType 0 自营仓 1 第三方仓
     * @param deliveryType 0 品牌方配送 1三方配送
     * @return
     */
    public static String getDesc(Integer warehouseType, Integer deliveryType) {
        if (Objects.equals(warehouseType, WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            return MarketOperationModeEnum.THIRD_RUN_Third_DELIVERY.getDesc();
        }
        if (Objects.equals(deliveryType, DeliveryTypeEnum.BRAND_DELIVERY.getCode())) {
            return MarketOperationModeEnum.SELF_RUN_SELF_DELIVERY.getDesc();
        }
        return SELF_RUN_Third_DELIVERY.getDesc();
    }


}
