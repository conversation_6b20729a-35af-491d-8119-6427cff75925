package com.cosfo.manage.common.context;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum  InfoType {

    COMPONENT_VERIFY_TICKET("component_verify_ticket","三方平台票据"),
    UN_AUTHORIZED("unauthorized","取消授权"),
    AUTHORIZED("authorized","授权成功"),
    UPDATE_AUTHORIZED("updateauthorized","更新授权");

    /**
     * 状态类型编码
     */
    private String str;
    /**
     * 状态类型描述
     */
    private String desc;
}
