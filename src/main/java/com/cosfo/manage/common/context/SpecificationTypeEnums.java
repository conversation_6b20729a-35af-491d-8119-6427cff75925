package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/3
 */
@Getter
@AllArgsConstructor
public enum SpecificationTypeEnums {
    /**
     * 容量*数量
     */
    CAPACITY(0,"容量*数量"),
    /**
     * 区间
     */
    INTERVAL(1,"区间");

    private Integer type;
    private String desc;

    public static Integer getType(String desc) {
        for (SpecificationTypeEnums specificationTypeEnums : SpecificationTypeEnums.values()) {
            if (specificationTypeEnums.desc.equals(desc)) {
                return specificationTypeEnums.getType();
            }
        }

        return null;
    }

    public static String getDescByType(Integer type) {
        for (SpecificationTypeEnums specificationTypeEnums : SpecificationTypeEnums.values()) {
            if (specificationTypeEnums.type.equals(type)) {
                return specificationTypeEnums.getDesc();
            }
        }

        return "";
    }
}
