package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/11
 */
@Getter
@AllArgsConstructor
public enum BillStatusEnum {
    /**
     *  待审核
     */
    UNAUDITED(1, "待审核"),
    /**
     * 审核成功
     */
    AUDITED_SUCCESS(2, "审核成功"),
    /**
     * 其他
     */
    OTHER(3,"线下核对"),
    ;
    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}
