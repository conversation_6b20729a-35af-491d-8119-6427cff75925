package com.cosfo.manage.common.context;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

@Getter
@AllArgsConstructor
public enum OnsaleTypeExcelEnum {
    /**
     * 下架
     */
    SOLD_OUT(0,"下架"),
    /**
     * 上架
     */
    ON_SALE(1,"上架");



    private Integer code;
    private String desc;

    public static OnsaleTypeExcelEnum getByDesc(String desc) {
        for (OnsaleTypeExcelEnum type : OnsaleTypeExcelEnum.values()) {
            if (type.desc.equals(desc)) {
                return type;
            }
        }

        return null;
    }
}
