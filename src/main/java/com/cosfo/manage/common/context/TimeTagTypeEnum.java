package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description: 时间标签类型
 * @author: <PERSON>
 * @date: 2023-06-08
 **/
@Getter
@AllArgsConstructor
public enum TimeTagTypeEnum {

    /**
     * 周
     */
    WEEK(1, "周"),

    /**
     * 月
     */
    MONTH(2, "月"),

    /**
     * 季度
     */
    QUARTER(3, "季度");

    /**
     * type
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 状态描述
     * @param type
     * @return
     */
    public static String getDesc(Integer type) {
        for (TimeTagTypeEnum timeTagTypeEnum : TimeTagTypeEnum.values()) {
            if (Objects.equals(type, timeTagTypeEnum.getType())) {
                return timeTagTypeEnum.getDesc();
            }
        }
        return null;
    }
}
