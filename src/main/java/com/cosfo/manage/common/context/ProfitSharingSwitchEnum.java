package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 分账开关
 *
 * @author: fss
 * @创建时间: 2023/5/25
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingSwitchEnum {

    /**
     * 开启
     */
    OPEN(1,"开启"),

    /**
     * 关闭
      */
    SHUTDOWN(0,"关闭");

    /**
     * 状态类型编码
     */
    private Integer code;

    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * get desc
     * @param type
     * @return
     */
    public static String getDesc(Integer type) {
        for (ProfitSharingSwitchEnum value : ProfitSharingSwitchEnum.values()) {
            if (Objects.equals(value.getCode(), type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
