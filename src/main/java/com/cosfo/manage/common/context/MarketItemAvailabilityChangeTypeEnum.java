package com.cosfo.manage.common.context;

import com.cofso.item.client.enums.OnSaleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MarketItemAvailabilityChangeTypeEnum {

    ON_SALE(1, "上架"),

    OFF_SALE(2, "下架"),

    NORMAL(3, "正常"),

    SOLD_OUT(4, "售罄"),

    ;

    private final Integer code;
    private final String desc;

    public static MarketItemAvailabilityChangeTypeEnum getOnSaleStatus(OnSaleTypeEnum itemOnSaleType) {
        switch (itemOnSaleType) {
            case ON_SALE:
                return ON_SALE;
            case SOLD_OUT:
                return OFF_SALE;
            default:
                return null;
        }

    }
}
