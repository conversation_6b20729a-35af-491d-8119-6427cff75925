package com.cosfo.manage.common.context;

import com.cosfo.common.util.TimeUtils;

import java.util.Date;

/**
 * 描述: Excel枚举类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public enum ExcelTypeEnum {
    /**
     * 订单
     */
    ORDER(1, "order.xlsx", "订单表"),


    /**
     * 售后单
     */
    AFTERSALEORDERS(2,"afterSale.xlsx","售后订单列表"),

    /**
     * 应付账单列表
     */
    HANDLE_BILL(3,"handle_bill.xlsx", "应付账单"),
    /**
     * 应收列表
     */
    STORE_BILL(4,"store_bill.xlsx","门店账单"),

    /**
     * 错误门店信息
     */
    ERROR_MERCHANT_STORE(5, "error_store.xlsx", "门店错误信息"),

    /**
     * 门店信息
     */
    MERCHANT_STORE(6, "store.xlsx", "门店信息"),

    /**
     * 商品信息
     */
    PRODUCT(7, "market.xlsx", "商品信息导出信息"),

    /**
     * 商品错误信息
     */
    ERROR_PRODUCT(8, "error_product.xlsx", "商品错误信息"),

    /**
     * 商品销售详情
     */
    PRODUCT_DETAIL_SALE(9, "product_detail_sales.xlsx", "商品销售数据导出信息"),

    /**
     * 门店采购详情
     */
    MERCHANT_STORE_DETAIL_PURCHASE(10, "merchant_store_detail_purchase.xlsx", "门店采购数据导出信息"),

    /**
     * 代仓商品库存数据
     */
    AGENT_SKU_WAREHOUSE_DATA(11, "agent_sku_warehouse_data.xlsx","实时库存导出信息"),

    /**
     * 导入门店分组
     */
    MERCHANT_STORE_GROUP_INPUT(12,"merchant_store_group_input.xlsx", "导入门店分组"),

    /**
     * 导入门店分组错误表
     */
    MERCHANT_STORE_GROUP_INPUT_ERROR(13,"merchant_store_group_input_error.xlsx","导入门店分组错误表"),

    /**
     * 导出门店分组
     */
    MERCHANT_STORE_GROUP_EXPORT(14, "merchant_store_group_export.xlsx", "门店分组批量导出信息"),
    /**
     * 导出结算信息
     */
    SETTLE_INFO(15,"settle_info.xlsx","结算明细导出信息"),
    /**
     * 分账明细导出
     */
    PROFIT_EXPORT(16,"profit_export.xlsx","分账明细导出"),
    /**
     * 地域导入错误信息
     */
    ERROR_AREA(17,"error_administrative_division.xlsx","地域导入错误信息"),
    /**
     * 交易流水导出
     */
    EXCHANGE_INFO(18,"exchange_info.xlsx","交易流水导出"),

    /**
     * 出入库记录
     */
    WAREHOUSE_RECORD_EXPORT(20,"warehouse_record.xlsx", "出入库记录导出信息"),

    /**
     * 配送记录
     */
    DISTRIBUTION_RECORD_EXPORT(21,"distribution_record.xlsx","配送记录导出信息"),

    /**
     * 采购明细
     */
    PURCHASE_DETAIL_REPORT(22, "purchase_detail_report.xlsx", "采购明细导出信息"),

    /**
     * 采购退货明细导出
     */
    PURCHASE_BACK_DETAIL_REPORT(23, "purchases_back_detail_report.xlsx", "采购退货明细导出信息"),

    /**
     * 货损明细报表
     */
    DAMAGE_DETAIL_REPORT(24, "damage_detail_report.xlsx", "货损明细导出信息"),

    /**
     * 损售比明细报表
     */
    DAMAGE_SALE_RATIO_REPORT(25, "damage_sale_ratio_report.xlsx","损售比导出信息"),

    /**
     * 导入自营货品
     */
    IMPORT_AGENT_PRODUCT(26,"import_agent_product_error.xlsx", "导入自营货品"),
    /**
     * 导出门店采购明细
     */
    MERCHANT_STORE_PURCHASE(27,"merchant_store_purchase.xlsx", "门店订货明细"),
    PREPAYMENT_RECORD_EXPORT(30,"prepayment_record_export.xlsx", "预付概况导出信息"),
    PREPAYMENT_ACCOUNT_EXPORT(28,"prepayment_account_export.xlsx", "余额构成导出信息"),
    PREPAYMENT_TRANSACTION_EXPORT(29,"prepayment_transaction_export.xlsx", "收支明细导出信息"),

    /**
     * 门店余额变动明细导出
     */
    STORE_BALANCE_CHANGE_RECORD_EXPORT(30,"store_balance_change_record.xlsx", "门店余额明细"),
    PRODUCT_STOCK_WARN_RECORD_EXPORT(31,"product_stock_warn_record.xlsx", "库存预警导出信息"),

    ITEM_STATEMENT(31, "item_statement.xlsx", "采购应付对账单-商品"),
    ORDER_STATEMENT(32, "order_statement.xlsx", "采购应付对账单-订单"),
    STATEMENT(33, "statement.xlsx", "采购应付对账单"),

    PURCHASE_BACK(34, "purchase_back.xlsx", "采购退货单导出"),
    /**
     * 供应商订单
     */
    SUPPLIER_ORDERS(35, "supplier_orders.xlsx", "订单表"),

    /**
     * 供应商售后订单
     */
    SUPPLIER_AFTER_SALE_ORDERS(36,"supplier_afterSale.xlsx","供应商售后订单"),

    /**
     * 门店订货异常分析导出
     */
    MERCHANT_STORE_ITEM_DIMENSION_AUDIT(37,"merchant_store_item_dimension_audit.xlsx","门店商品维度订货异常分析"),

    /**
     *门店订货异常分析
     */
    MERCHANT_STORE_DIMENSION_AUDIT(38,"merchant_store_dimension_audit.xlsx","门店维度订货异常分析"),

    /**
     * 门店订货占比分析
     */
    MERCHANT_STORE_ORDER_PROPORTION(39,"merchant_store_order_proportion.xlsx","门店订货占比分析"),

    PRODUCT_SKU_SELF(40,"product_sku_self.xlsx","自营货品导出"),

    /**
     * 代理费用账单
     */
    AGENT_WAREHOUSE_BILL(41, "agent_warehouse_bill.xlsx", "代仓费用账单"),

    /**
     * 普通供应商采购应付对账单-订单
     */
    ORDER_STATEMENT_NORMAL_SUPPLIER(42, "order_statement_normal_supplier.xlsx", "普通供应商采购应付对账单-订单"),

    /**
     * 普通供应商采购应付对账单
     */
    STATEMENT_NORMAL_SUPPLIER(43, "statement_normal_supplier.xlsx", "普通供应商采购应付对账单"),
    /**
     * 导入商品价格策略
     */
    IMPORT_ITEM_PRICE_STRATEGY(44,"import_item_price_strategy_error.xlsx", "导入商品价格策略"),
    /**
     * 导入商品上下架策略
     */
    IMPORT_ITEM_ONSALE_STRATEGY(45,"import_item_onsale_strategy_error.xlsx", "导入商品上下架"),
    /**
     * 导出报价货品
     */
    PRODUCT_SKU_SUPPLY(46,"product_sku_supply.xlsx", "导出报价货品"),

    /**
     * 导出商品销量榜
     */
    MARKET_ITEM_SALES_RANKING(47,"market_item_sales_ranking_export.xlsx", "商品销量榜"),

    PRODUCT_STOCK_FOREWARNING_REPORT(48,"product_stock_forewarning_report.xlsx", "导出库存预警报表"),

    /**
     * 导出滞销商品
     */
    MARKET_ITEM_NO_SALE(49,"market_item_no_sale_report.xlsx", "滞销商品"),

    /**
     * 导出售罄商品时长
     */
    MARKET_ITEM_ON_SALE_SOLD_OUT(50,"market_item_on_sale_sold_out_report.xlsx", "售罄商品时长"),

    /**
     * 门店采购活跃率
     */
    MERCHANT_STORE_PURCHASE_ACTIVITY(51,"merchant_store_purchase_activity_export.xlsx", "门店采购活跃率"),

    /**
     * 履约健康度-发货及时性
     */
    MERCHANT_STORE_ORDER_FULFILLMENT_RATE(52,"merchant_store_order_fulfillment_rate_export.xlsx", "履约健康度-发货及时性"),



    ERROR_POS_STORE(53, "error_pos_store.xlsx", "三方POS门店导入错误信息"),
    ERROR_POS_ITEM(54, "error_pos_item.xlsx", "三方POS商品导入错误信息"),
    ERROR_POS_BOM(55, "error_pos_bom.xlsx", "成本卡导入错误信息"),
    /**
     * 门店滞叫分析表导出
     */
    MERCHANT_STORE_ORDER_HYSTERESIS_ANALYSIS(56,"merchant_store_order_hysteresis_analysis.xlsx", "导出门店滞叫分析表"),

    /**
     * 库存周转天数
     */
    STOCK_TURNOVER_DAYS(57,"stock_turnover_days.xlsx", "库存周转天数"),

    /**
     * 滞销货品
     */
    GOODS_NO_SALE_SUMMARY(58,"goods_no_sale_summary.xlsx", "滞销货品"),

    /**
     * 临期货品
     */
    GOODS_NEAR_DEADLINE_SUMMARY(59,"goods_near_deadline_summary.xlsx", "临期货品"),

    /**
     * 过期货品
     */
    GOODS_EXPIRATION_SUMMARY(59,"goods_expiration_summary.xlsx", "过期货品"),

    /**
     * 供应商到仓准时率
     */
    SUPPLIER_TO_WAREHOUSE_ON_TIME_RATE(60,"supplier_to_warehouse_on_time_rate.xlsx", "供应商到仓准时率"),

    /**
     * 供应商到仓准确率
     */
    SUPPLIER_TO_WAREHOUSE_ACCURACY(61,"supplier_to_warehouse_accuracy.xlsx", "供应商到仓准确率"),

    POST_ORDER_AUDIT(62,"pos_order_audit.xlsx","门店订单稽核报表导出"),

    ORDER_JUEPEI(63,"order_juepei.xlsx","绝配订单导出"),

    /**
     * 导入无货商品供应价
     */
    IMPORT_NO_GOODS_SUPPLY_PRICE(64,"import_no_goods_supply_price_error.xlsx", "导入无货商品供应价"),

    PURCHASE_SUMMARY_REPORT_SKU(65,"purchase_summary_sku_report.xlsx", "采购汇总表货品维度"),
    PURCHASE_SUMMARY_REPORT_SUPPLIER(66,"purchase_summary_supplier_report.xlsx", "采购汇总表供应商维度"),

    SPECIAL_RULE_BILL(67, "special_rule_bill.xlsx", "特殊规则账单"),
    
    IMPORT_ORDER_DELIVERY(68,"import_no_goods_order_delivery_error.xlsx", "导入订单批量发货"),

    IMPORT_ORDER_DELIVERY_SUPPLIER(69,"import_no_goods_order_delivery_supplier_error.xlsx", "供应商导入订单批量发货"),

    /**
     * 半成品
     */
    SEMI_FINISHED_PRODUCTS(6, "semi_finished_products.xlsx", "半成品"),


    ORDER_ITEM_STATEMENT_ANALYSIS(71, "order_item_statement_analysis.xlsx", "订单对账表"),

    IMPORT_AGENT_ORDER_ERROR(72, "import_agent_order_error.xlsx", "导入代下单错误"),

    IMPORT_AGENT_ORDER_OUTER_ERROR(73, "import_agent_order_outer_error.xlsx", "三方导单错误"),
    ;

    private Integer order;
    private String name;
    private String desc;

    ExcelTypeEnum(Integer order, String name, String desc) {
        this.order = order;
        this.name = name;
        this.desc = desc;
    }

    public Integer getOrder() {
        return this.order;
    }

    public String getName() {
        return this.name;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getNameByOrder(Integer order) {
        for (ExcelTypeEnum excelTypeEnum : ExcelTypeEnum.values()) {
            if (excelTypeEnum.order.equals(order)) {
                return excelTypeEnum.name;
            }
        }

        return null;
    }

    /**
     * 选择语句excel
     *
     * @param orderSummaryFlag             订单汇总flag
     * @param itemSummaryFlag              商品汇总flag
     * @param supplierDirectAssignBillFlag 供应商直配汇总flag
     * @return {@link ExcelTypeEnum}
     */
    public static ExcelTypeEnum chooseStatementExcel(boolean orderSummaryFlag, boolean itemSummaryFlag, boolean supplierDirectAssignBillFlag, boolean xmSupplierFlag) {
        if (!supplierDirectAssignBillFlag) {
            return AGENT_WAREHOUSE_BILL;
        }
        if (orderSummaryFlag && itemSummaryFlag && xmSupplierFlag) {
            return STATEMENT;
        }
        if (orderSummaryFlag && itemSummaryFlag && !xmSupplierFlag) {
            return STATEMENT_NORMAL_SUPPLIER;
        }
        if (orderSummaryFlag && xmSupplierFlag) {
            return ORDER_STATEMENT;
        }
        if (orderSummaryFlag && !xmSupplierFlag) {
            return ORDER_STATEMENT_NORMAL_SUPPLIER;
        }
        if (!orderSummaryFlag && itemSummaryFlag) {
            return ITEM_STATEMENT;
        }
        return ORDER_STATEMENT;
    }

    public String getFileName(){
        return this.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx";
    }
}
