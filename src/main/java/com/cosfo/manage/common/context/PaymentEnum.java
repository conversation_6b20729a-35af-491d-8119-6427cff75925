package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/23  11:46
 */
public class PaymentEnum {
    @Getter
    @AllArgsConstructor
    public enum Status {
        /**
         * 0、待支付
         */
        WAITING(0, "待支付", "I"),
        /**
         * 1、支付成功
         */
        SUCCESS(1, "支付成功", "S"),
        /**
         * 2、支付失败
         */
        FAIL(2, "支付失败", "F"),
        /**
         * 3、取消支付
         */
        CANCELED(3, "取消支付","N"),
        /**
         * 4、处理中(锁定)
         */
        DEALING(4, "处理中(锁定)","P");
        private Integer code;
        private String desc;
        private String huifuStat;

        /**
         * 根据汇付返回码确定状态
         *
         * @param huifuStat
         * @return
         */
        public static Status getStatusByHuifuStat(String huifuStat){
            for(Status status: Status.values()){
                if(status.getHuifuStat().equals(huifuStat)){
                    return status;
                }
            }

            return null;
        }
    }
}
