package com.cosfo.manage.common.context;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/4/11 16:22
 * @Description: 货源类型
 */
@Getter
@AllArgsConstructor
public enum GoodsTypeEnum {

    /**
     * 供应商直发货品
     * 前端展示用，0和1前端展示合并成供应商直发
     */
    SUPPLIER_GOOD(-1, "供应商直发货品"),
    /**
     * 无货商品
     */
    NO_GOOD_TYPE(0, "无货商品"),
    /**
     * 报价商品
     */
    QUOTATION_TYPE(1, "报价货品"),
    /**
     * 自营货品
     */
    SELF_GOOD_TYPE(2, "自营货品");

    private Integer code;
    private String desc;

    public static GoodsTypeEnum getTypeByCode(Integer code) {
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static String getDescByCode(Integer code) {
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }

        return null;
    }

    public static String getShowDescByCode(Integer code) {
        if (Lists.newArrayList(NO_GOOD_TYPE.code, QUOTATION_TYPE.code).contains(code)) {
            return SUPPLIER_GOOD.desc;
        }
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
    public static String getShowDescByCodeV2(Integer code) {
        if (Lists.newArrayList(NO_GOOD_TYPE.code, QUOTATION_TYPE.code).contains(code)) {
            return "非总部备货";
        }
        if (Lists.newArrayList(SELF_GOOD_TYPE.code).contains(code)) {
            return "总部备货";
        }
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
    public static final Set<Integer> NO_WAREHOUSE_CODES = new HashSet<>(Lists.newArrayList(NO_GOOD_TYPE.getCode(), SELF_GOOD_TYPE.getCode()));

    public static final Set<Integer> THIRD_DELIVERY_CODES = new HashSet<>(Lists.newArrayList(SELF_GOOD_TYPE.getCode(), QUOTATION_TYPE.getCode()));

    /**
     * 供应商直发货品展示
     *
     * @param goodsTypeEnum
     * @return
     */
    public static GoodsTypeEnum supplierGoodsMapping(GoodsTypeEnum goodsTypeEnum) {
        if (Lists.newArrayList(NO_GOOD_TYPE, QUOTATION_TYPE).contains(goodsTypeEnum)) {
            return SUPPLIER_GOOD;
        }
        return goodsTypeEnum;
    }

    public static Integer getThreePartiesCode() {
        return QUOTATION_TYPE.getCode();
    }

    public static Integer getBrandDeliveryCode() {
        return NO_GOOD_TYPE.getCode();
    }


}
