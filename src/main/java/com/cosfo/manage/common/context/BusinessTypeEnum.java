package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/5
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    /**
     * 订单
     */
    ORDER(0,"订单"),
    /**
     * 售后单
     */
    ORDER_AFTER_SALE(1,"售后单");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 状态描述
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (Objects.equals(code, businessTypeEnum.getCode())) {
                return businessTypeEnum.getDesc();
            }
        }
        return null;
    }
}
