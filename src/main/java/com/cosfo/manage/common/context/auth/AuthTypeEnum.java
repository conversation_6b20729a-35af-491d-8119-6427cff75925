package com.cosfo.manage.common.context.auth;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuthTypeEnum {

    /**
     * 账期权限
     */
    MANAGER_BILL("cosfo_manage:customer-manage:bill:update", "账期权限"),

    /**
     * 专享余额权限
     */
    MANAGER_BALANCE_SWITCH("cosfo_manage:customer-manage:balance-switch:update", "专享余额权限"),

    /**
     * 调整余额权限
     */
    MANAGER_BALANCE("cosfo_manage:customer-manage:balance:update", "調整余额权限"),
    ;

    private String url;

    private String desc;

}
