package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2022/12/27 11:24
 * 分账状态0待分账1成功2失败3取消
 */
@Getter
@AllArgsConstructor
public enum BillProfitEnum {
    /**
     * 待分账
     */
    DEALING(0,"待分账"),
    /**
     * 成功
     */
    SUCCESS(1,"成功"),
    /**
     * 失败
     */
    FAIL(2,"失败"),
    /**
     * 取消
     */
    CANCEL(3,"取消");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 状态描述
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (BillProfitEnum billProfitEnum : BillProfitEnum.values()) {
            if (billProfitEnum.getCode().equals(code)) {
                return billProfitEnum.getDesc();
            }
        }
        return null;
    }
}
