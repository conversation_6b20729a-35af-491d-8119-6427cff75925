package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/17
 */
@Getter
@AllArgsConstructor
public enum DeliveryTypeEnum {

    /**
     * 品牌方配送
     */

    BRAND_DELIVERY(0, "品牌方配送"),

    /**
     * 三方配送
     */
    THIRD_DELIVERY(1,"三方配送"),

    NULL_ERROR(-1, "非法数据");
    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;
    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DeliveryTypeEnum getByCode(Integer code){
        for (DeliveryTypeEnum accountType: DeliveryTypeEnum.values()) {
            if (accountType.getCode().equals(code)) {
                return accountType;
            }
        }
        return DeliveryTypeEnum.NULL_ERROR;
    }
}
