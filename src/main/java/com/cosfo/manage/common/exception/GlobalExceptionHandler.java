package com.cosfo.manage.common.exception;

import com.cosfo.manage.common.exception.code.OpenApiErrorCode;
import com.cosfo.manage.common.result.CompatibleResultDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.common.expection.TenantPermissionException;
import net.xianmu.authentication.enums.TenantPermissionExceptionEnum;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.dubbo.rpc.RpcException;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authz.UnauthorizedException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @date 2022/5/6  14:31
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public CommonResult exceptionHandler(Exception e) {
        // 处理业务异常
        CommonResult<Object> fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        if (e instanceof ClientAbortException) {
            log.info("【警告】message=[{}]", e.getMessage(), e);
        } else if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException) e;
            log.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, exception.getMessage());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            log.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException) e;
            log.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, exception.getMessage());
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException) e;
            log.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR,  exception.getMessage());
        } else if (e instanceof RpcException) {
            RpcException exception = (RpcException) e;
            log.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "服务处理异常");
        }else if(e instanceof UnauthorizedException){
            log.warn("接口请求权限异常, 异常信息:{}", e.getMessage(), e);
            fail = CommonResult.fail(ResultStatusEnum.FORBIDDEN, ResultStatusEnum.FORBIDDEN.getMsg(), ResultStatusEnum.FORBIDDEN.getStatus().toString());
        } else if (e instanceof DefaultServiceException) {
            log.error("DefaultServiceException：{}", e.getMessage(), e);
            if (((DefaultServiceException) e).getCode() != null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
            }
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
        } else if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException exception = (MethodArgumentNotValidException) e;
            log.warn("Exception：{}", exception.getMessage(), exception);
            fail = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, exception.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        } else if (e instanceof TenantPermissionException ) {
            TenantPermissionException exception = (TenantPermissionException) e;
            log.warn("Exception：{}", exception.getMessage(), exception);
            if (("BIZ-" + TenantPermissionExceptionEnum.EXPIRE.getName()).equals(exception.getErrorCode().getCode())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"当前功能已过期，请联系客户经理");
            }
            fail = CommonResult.fail(ResultStatusEnum.FORBIDDEN, exception.getMessage(), String.valueOf(TenantPermissionExceptionEnum.EXPIRE.getStatus()));

        } else {
            log.error("DefaultServiceException：{}", e.getMessage(), e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }

        return fail;
    }

    @ExceptionHandler(value = AccountException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public CommonResult authExceptionHandle(AccountException e) {
        log.warn("登录信息已过期, {}", e.getMessage(), e);
        return CommonResult.fail(ResultStatusEnum.UNAUTHORIZED, e.getMessage());
    }

    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ConsumerException) {
            ConsumerException exception = (ConsumerException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException)throwable;
            log.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            if(exception.getErrorCode() != null && (exception.getErrorCode() instanceof OpenApiErrorCode)){
                return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getErrorCode().getStatus(), exception.getMessage());
            }
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        }  else if (throwable instanceof DefaultServiceException) {
            DefaultServiceException exception = (DefaultServiceException)throwable;
            log.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getCode().toString(), exception.getMessage());
        }else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException)throwable;
            log.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof OpenApiProviderException) {
            OpenApiProviderException exception = (OpenApiProviderException) throwable;
            log.error("openApi提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getErrorCode().getStatus(), exception.getMessage());
        } else {
            log.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return DubboResponse.getError(providerErrorCode.getCode(), throwable.getMessage());
        }
    }

}
