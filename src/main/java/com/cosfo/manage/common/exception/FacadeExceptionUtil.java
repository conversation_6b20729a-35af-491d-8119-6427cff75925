package com.cosfo.manage.common.exception;

import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.BizErrorCode;
import net.xianmu.common.exception.error.code.ParamsErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.rpc.RpcException;

import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/5/15 15:27
 * @Description:
 */
public class FacadeExceptionUtil {

    /**
     * PRC 接口中需要抛出明确异常的异常类
     *
     * @param dubboResponse
     * @param defaultErrMsg
     */
    public static <T> void executeFaceException(DubboResponse<T> dubboResponse, String defaultErrMsg) {
        if (Objects.isNull(dubboResponse)){
            throw new RpcException("RPC 返回异常！");
        }
        if (dubboResponse.getCode().startsWith(ParamsErrorCode.PARAMS_PREFIX)) {
            throw new ParamsException(dubboResponse.getMsg());
        }
        if (dubboResponse.getCode().startsWith(BizErrorCode.BIZ_PREFIX)) {
            throw new BizException(dubboResponse.getMsg());
        }
        if (!dubboResponse.isSuccess()) {
            throw new ProviderException(Objects.nonNull(defaultErrMsg) ? defaultErrMsg : dubboResponse.getMsg());
        }
    }

}
