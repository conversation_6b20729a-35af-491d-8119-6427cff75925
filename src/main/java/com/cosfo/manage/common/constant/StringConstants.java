package com.cosfo.manage.common.constant;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 11:30
 */
public class StringConstants {

    /**
     * 逗号分隔符
     */
    public static final String SEPARATING_SYMBOL = ",";

    /**
     * 逗号分隔符
     */
    public static final String CHINESE_SEPARATING_SYMBOL = "，";

    /**
     * 小数点
     */
    public static final String DECIMAL_POINT  = ".";

    /**
     * 下划线分隔符
     */
    public static final String SEPARATING_LINE = "_";

    /**
     * *号
     */
    public static final String STARS = "*";

    /**
     * 分隔符
     */
    public static final String SEPARATING_IN_LINE = "-";

    /**
     * 分隔符
     */
    public static final String DOUBLE_SEPARATING_IN_LINE = "- -";

    /**
     * 空字符串
     */
    public static final String EMPTY = "";

    /**
     * 左斜线
     */
    public static final String LEFT_SLASH = "/";

    /**
     * 百分号
     */
    public static final String PERCENT = "%";

    /**
     * 0
     */
    public static final String ZERO = "0";

    /**
     * 0%
     */
    public static final String ZERO_PERCENT = "0%";

    /**
     * 截单时间
     */
    public static final String CUT_OFF_TIME = " 20:00:00";

    /**
     * 代仓商品库存数据
     */
    public static final String AGENT_SKU_WAREHOUSE_DATA_EXPORT = "全部";

    /**
     * 默认的售后单位
     */
    public static final String DEFAULT_AFTER_SALE_UNIT = "件";
    /**
     * 默认的体积
     */
    public static final String DEFAULT_VOLUME = "1.00*1.00*1.00";

    /**
     * 特殊字符@
     */
    public static final String CHARACTER = "@";

    /**
     * 密码匹配表达式
     */
    public static final String password_regex = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$";
}
