package com.cosfo.manage.flpos.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.flpos.sign.SignUtil;
import com.cosfo.manage.flpos.vo.FLPosResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlPosFacade {
    public static String url = "https://openapi.51hchc.com/";

    @NacosValue(value = "${flpos.mchId:1000000091}", autoRefreshed = true)
    private String mchId;

    @NacosValue(value = "${flpos.signKey:90598bfdaf2c4a0ebf5896e83aedaf50}", autoRefreshed = true)
    private String signKey;

    public String sendPost(String uri, Map<String, String> param) {
        param.put ("mch_id", mchId);
        param.put ("nonce_str", StringUtils.generateRandomNumberStr (6));
        String sign = SignUtil.getSign (param, signKey);
        param.put ("sign", sign);

        String ret = null;
        URL urlEntity = null;
        HttpURLConnection connection = null;
        try {
            //将对象转成 json 字符串，可以按需使用自己的 JSON 工具进行转换。
            ObjectMapper mapper = new ObjectMapper ();
            String data = mapper.writeValueAsString (param); //请求路径，注意替换
            log.info ("flpos接口调用，uri={},request={}",uri,data);
            urlEntity = new URL (url + uri);
            connection = (HttpURLConnection) urlEntity.openConnection ();
            //post 请求，参数要放在 http 正文内，因此需要设为 true, 默认情况下是 false;
            connection.setDoOutput(true);
            //设置是否从 httpUrlConnection 读入，默认情况下是 true;
            connection.setDoInput(true);
            // Post 请求不能使用缓存
            connection.setUseCaches (false);
            connection.setRequestMethod ("POST");
            connection.setRequestProperty ("Content-Type", "application/json;charset=UTF-8");
            connection.setRequestProperty ("accept", "application/json");
            connection.setRequestProperty ("Connection", "Keep-Alive");// 维持长连接
            connection.connect();
            try (OutputStreamWriter outputStreamWriter = new OutputStreamWriter (connection.getOutputStream (), "UTF-8");
                BufferedWriter writer = new BufferedWriter (outputStreamWriter)) {
                writer.write (data);
                writer.flush ();
            }
            int resultCode = connection.getResponseCode ();
            if (HttpURLConnection.HTTP_OK == resultCode) {
                StringBuilder sb = new StringBuilder ();
                try (BufferedReader responseReader = new BufferedReader (new InputStreamReader (connection.getInputStream (), "UTF-8"))) {
                    while ((ret = responseReader.readLine ()) != null) {
                        sb.append (ret).append ("\n");
                    }
                }
                ret = sb.toString ();
                log.info ("flpos接口调用成功，uri={},request={},response={}",uri,data,ret);
            } else {
                log.error ("flpos接口调用失败，uri={},request={},resultCode={}",uri,data,resultCode);
            }
        } catch(IOException e){
            log.error ("flpos接口调用失败，uri={},param={}",uri,param,e);
        } finally{
            if (connection != null) {
                connection.disconnect ();
            }
        }
        if(StringUtil.isNotBlank (ret)) {
            FLPosResponse flPosResponse = JSON.parseObject (ret, FLPosResponse.class);
            if ("SUCCESS".equals (flPosResponse.getReturn_code ()) && "SUCCESS".equals (flPosResponse.getResult_code ())) {
                return JSON.toJSONString (flPosResponse.getResult_data ());
            }else{
                log.error ("flpos接口调用业务失败，uri={},param={},ret={}",uri,param,ret);
            }
        }
        return null;
    }

}
