package com.cosfo.manage.flpos.sign;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class SignUtil {
 
    private SignUtil() {}
    public static String getSign(Map<String, String> param, String signKey) {
        try {
            String sortedParamStr = getSortedParamStr(param, signKey);
            return sign(sortedParamStr, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }
    private static String getSortedParamStr(Map<String, String> param, String signKey) throws UnsupportedEncodingException {
        //按着 key 的 ASCII 码从小到大排序(字典序)
        TreeMap<String, String> sortMap = new TreeMap<> (param);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortMap.entrySet()) {
            if ("sign".equals(entry.getKey()) || "key".equals(entry.getKey()) || entry.getValue() == null) {
                continue;
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        //最后拼接 singKey
        sb.append("key").append("=").append(signKey);
        return sb.toString ();
    }
    private static String sign(String str, String charsetName) {
        if (str == null || str.length() == 0) {
                return null;
        }
        StringBuilder sb = new StringBuilder();
        try {
            byte[] data = charsetName != null ? str.getBytes(charsetName) : str.getBytes();
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(data);
            byte bytes[] = md5.digest();
            for (int i = 0; i < bytes.length; i++) {
                String s = Integer.toHexString(bytes[i] & 0xff);
                if (s.length() == 1) {
                    sb.append('0');
                }
                sb.append(s);
            }
            return sb.toString().toUpperCase();
        } catch (Exception e) {
            log.error("flpos-create sign was failed: " + e);
            return null;
        }
    }
}