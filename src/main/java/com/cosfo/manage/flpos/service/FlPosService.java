package com.cosfo.manage.flpos.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.flpos.facade.FlPosAPI;
import com.cosfo.manage.flpos.facade.FlPosFacade;
import com.cosfo.manage.flpos.vo.FLPosResponse;
import com.cosfo.manage.flpos.vo.FlPosOrderInfoVO;
import com.cosfo.manage.flpos.vo.FlPosOrderDetailVO;
import com.cosfo.manage.flpos.vo.PageResult;
import com.cosfo.manage.keruyun.vo.KryOrderListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlPosService {
    @Autowired
    private FlPosFacade flPosFacade;
    public PageResult<FlPosOrderInfoVO> orderList(String storeId, String startTime, String endTime, Integer pageNo) {
        Map<String, String> param = new HashMap ();
        param.put ("store_id",storeId);
        param.put ("start_time",startTime);
        param.put ("end_time",endTime);
        param.put ("page_no", pageNo.toString ());
        param.put ("page_size", "100");
        String o = flPosFacade.sendPost (FlPosAPI.ORDER_LIST.getUri (), param);
        if(o!=null){
            PageResult pageResult = JSON.parseObject (o, PageResult.class);
            pageResult.setData (JSON.parseArray (JSON.toJSONString (pageResult.getData ()), FlPosOrderInfoVO.class));;
            return pageResult;
        }
        return null;
    }

    public FlPosOrderDetailVO orderDetail(String bill) {
        Map<String, String> param = new HashMap ();
        param.put ("bill",bill);
        String o = flPosFacade.sendPost (FlPosAPI.ORDER_DETAIL.getUri (),param);
        if(o!=null){
            FlPosOrderDetailVO dataVO = JSON.parseObject (o,FlPosOrderDetailVO.class);
            dataVO.setDetailInfo (JSON.toJSONString (dataVO));
            return dataVO;
        }
        return null;
    }
}
