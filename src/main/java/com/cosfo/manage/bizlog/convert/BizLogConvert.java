package com.cosfo.manage.bizlog.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cosfo.manage.bizlog.model.dto.BizLogQueryDTO;
import com.cosfo.manage.bizlog.model.vo.BizLogListVO;
import com.cosfo.manage.common.constant.BizLogConstants;
import com.cosfo.manage.good.model.vo.AgentSkuBizLogVO;
import com.cosfo.manage.good.model.vo.ProductSpuDetailVO;
import com.cosfo.manage.order.model.vo.OrderBizLogVO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.bizlog.QueryBizLogReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/12/26 20:53
 * @Description:
 */
@Slf4j
public class BizLogConvert {

    public static QueryBizLogReq convert2QueryReq(BizLogQueryDTO queryDTO) {
        if (queryDTO == null) {
            return null;
        }
        QueryBizLogReq queryBizLogReq = new QueryBizLogReq();
        queryBizLogReq.setTenantId(queryDTO.getTenantId());
        queryBizLogReq.setBizDomain(queryDTO.getBizDomain());
        queryBizLogReq.setEntityType(queryDTO.getEntityType());
        queryBizLogReq.setBizKey(queryDTO.getBizKey());
        queryBizLogReq.setBizStartTime(queryDTO.getBizStartTime());
        queryBizLogReq.setBizEndTime(queryDTO.getBizEndTime());
        queryBizLogReq.setOperationUserName(queryDTO.getOperationUserName());
        queryBizLogReq.setOperationPhone(queryDTO.getOperationPhone());
        queryBizLogReq.setPageIndex(queryDTO.getPageIndex());
        queryBizLogReq.setPageSize(queryDTO.getPageSize());
        queryBizLogReq.setSortList(queryDTO.getSortList());
        return queryBizLogReq;
    }


    public static PageInfo<BizLogListVO> convert2BizLogPage(PageInfo<BizLogListResp> pageInfoResp) {
        if (pageInfoResp == null) {
            return null;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(pageInfoResp.getPageNum());
        pageInfo.setPageSize(pageInfoResp.getPageSize());
        pageInfo.setSize(pageInfoResp.getSize());
        pageInfo.setStartRow(pageInfoResp.getStartRow());
        pageInfo.setEndRow(pageInfoResp.getEndRow());
        pageInfo.setPages(pageInfoResp.getPages());
        pageInfo.setPrePage(pageInfoResp.getPrePage());
        pageInfo.setNextPage(pageInfoResp.getNextPage());
        pageInfo.setIsFirstPage(pageInfoResp.isIsFirstPage());
        pageInfo.setIsLastPage(pageInfoResp.isIsLastPage());
        pageInfo.setHasPreviousPage(pageInfoResp.isHasPreviousPage());
        pageInfo.setHasNextPage(pageInfoResp.isHasNextPage());
        pageInfo.setNavigatePages(pageInfoResp.getNavigatePages());
        pageInfo.setNavigatepageNums(pageInfoResp.getNavigatepageNums());
        pageInfo.setNavigateFirstPage(pageInfoResp.getNavigateFirstPage());
        pageInfo.setNavigateLastPage(pageInfoResp.getNavigateLastPage());
        pageInfo.setTotal(pageInfoResp.getTotal());
        pageInfo.setList(convert2BizLogList(pageInfoResp.getList()));
        return pageInfo;
    }

    public static List<BizLogListVO> convert2BizLogList(List<BizLogListResp> respList) {
        if (respList == null) {
            return Collections.emptyList();
        }
        List<BizLogListVO> bizLogListVOList = new ArrayList<>();
        for (BizLogListResp bizLogListResp : respList) {
            bizLogListVOList.add(toBizLogListVO(bizLogListResp));
        }
        return bizLogListVOList;
    }

    public static BizLogListVO toBizLogListVO(BizLogListResp bizLogListResp) {
        if (bizLogListResp == null) {
            return null;
        }
        BizLogListVO bizLogListVO = new BizLogListVO();
        bizLogListVO.setLogId(bizLogListResp.getLogId());
        bizLogListVO.setTenantId(bizLogListResp.getOperatorTenantId());
        bizLogListVO.setTenantName(bizLogListResp.getOperatorTenantName());
        bizLogListVO.setOperatorAuthUserId(bizLogListResp.getOperatorAuthUserId());
        bizLogListVO.setOperatorUserName(bizLogListResp.getOperatorUserName());
        bizLogListVO.setOperatorPhone(bizLogListResp.getOperatorPhone());
        bizLogListVO.setBizDomainName(bizLogListResp.getBizDomainName());
        bizLogListVO.setBizDomain(bizLogListResp.getBizDomain());
        bizLogListVO.setEntityType(bizLogListResp.getEntityType());
        bizLogListVO.setBizKeyTip(bizLogListResp.getBizKeyTip());
        bizLogListVO.setOperationName(bizLogListResp.getOperationName());
        bizLogListVO.setBizKey(bizLogListResp.getBizKey());
        bizLogListVO.setLogContent(bizLogListResp.getLogContent());
        bizLogListVO.setBizCreateTime(bizLogListResp.getBizCreateTime());
        return bizLogListVO;
    }

    public static AgentSkuBizLogVO toAgentSkuBizLogVO(BizLogListVO bizLogListVO) {
        if (bizLogListVO == null) {
            return null;
        }
        AgentSkuBizLogVO agentSkuBizLogVO = new AgentSkuBizLogVO();
        agentSkuBizLogVO.setLogId(bizLogListVO.getLogId());
        agentSkuBizLogVO.setTenantId(bizLogListVO.getTenantId());
        agentSkuBizLogVO.setTenantName(bizLogListVO.getTenantName());
        agentSkuBizLogVO.setOperatorAuthUserId(bizLogListVO.getOperatorAuthUserId());
        agentSkuBizLogVO.setOperatorUserName(bizLogListVO.getOperatorUserName());
        agentSkuBizLogVO.setOperatorPhone(bizLogListVO.getOperatorPhone());
        agentSkuBizLogVO.setOperationName(bizLogListVO.getOperationName());
        agentSkuBizLogVO.setBizTime(bizLogListVO.getBizCreateTime());
        if (Objects.isNull(agentSkuBizLogVO.getOperatorUserName()) &&
            (BizLogConstants.SKU_AGENT_SUCCESS.equals(agentSkuBizLogVO.getOperationName()) || BizLogConstants.SKU_AGENT_REFUSE.equals(agentSkuBizLogVO.getOperationName()))){
            agentSkuBizLogVO.setOperatorUserName("代仓服务商-杭州鲜沐科技有限公司");
        }
        String logContent = bizLogListVO.getLogContent();
        try{
            if(agentSkuBizLogVO.getOperationName().equals(BizLogConstants.SKU_AGENT_REFUSE)){
                Map<String, String> logMap = JSON.parseObject(logContent, new TypeReference<HashMap<String, String>>() {});
                agentSkuBizLogVO.setRefuseReason(logMap.get("refuseReason"));
                agentSkuBizLogVO.setSpuDetailVO(JSONObject.parseObject(logMap.get("skuInfo"), ProductSpuDetailVO.class));
            }else {
                agentSkuBizLogVO.setSpuDetailVO(JSONObject.parseObject(bizLogListVO.getLogContent(), ProductSpuDetailVO.class));
            }
        }catch (Exception e){
            log.error("转换代仓申请日志信息出错！",e);
        }

        return agentSkuBizLogVO;
    }

    public static List<AgentSkuBizLogVO> convert2AgentLogList(List<BizLogListVO> logListVOS) {

        if (logListVOS == null) {
            return Collections.emptyList();
        }
        List<AgentSkuBizLogVO> agentSkuBizLogVOList = new ArrayList<>();
        for (BizLogListVO bizLogListVO : logListVOS) {
            agentSkuBizLogVOList.add(toAgentSkuBizLogVO(bizLogListVO));
        }
        return agentSkuBizLogVOList;
    }


    public static OrderBizLogVO convert2OrderBizLogVO(BizLogListVO bizLogListVO) {
        if (bizLogListVO == null) {
            return null;
        }
        OrderBizLogVO orderBizLogVO = new OrderBizLogVO();
        orderBizLogVO.setLogId(bizLogListVO.getLogId());
        orderBizLogVO.setTenantId(bizLogListVO.getTenantId());
        orderBizLogVO.setTenantName(bizLogListVO.getTenantName());
        orderBizLogVO.setOperatorAuthUserId(bizLogListVO.getOperatorAuthUserId());
        orderBizLogVO.setOperatorUserName(bizLogListVO.getOperatorUserName());
        orderBizLogVO.setOperatorPhone(bizLogListVO.getOperatorPhone());
        orderBizLogVO.setOperationName(bizLogListVO.getOperationName());
        orderBizLogVO.setBizCreateTime(bizLogListVO.getBizCreateTime());
        orderBizLogVO.setLogContent(bizLogListVO.getLogContent());
        return orderBizLogVO;
    }
}
