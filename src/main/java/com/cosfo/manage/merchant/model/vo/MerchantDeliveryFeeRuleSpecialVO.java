package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MerchantDeliveryFeeRuleSpecialVO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、无仓 1、三方仓 2、自营仓
     */
    private Integer type;

    /**
     * 规则类型 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;

    /**
     * 定价方式：0,固定 1实时加价 2实时上浮
     */
    private Integer priceType;


    /**
     * 基于仓运费报价：实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;

    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;

    /**
     * 免运费规则，0金额，1数量
     */
    private Integer freeDeliveryType;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 命中商品id列表
     */
    private List<Long> hitItemIds;

    /**
     * 选中所有商品
     */
    private Boolean allItemHit;

    /**
     * 仓库编号,0:无实义等价于空
     */
    private List<Integer> warehouseNoList;

    /**
     * 仓库名称列表
     */
    private List<String> warehouseNameList;

    /**
     * 命中配送区域
     */
    private List<List<String>> hitAreas;

    /**
     * 阶梯价
     */
    private List<MerchantDeliveryStepFeeVO> stepFeeList;
}
