package com.cosfo.manage.merchant.model.vo.balance;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/4/24 11:18
 * @PackageName:com.cosfo.manage.merchant.model.vo.balance
 * @ClassName: MerchantBalanceVO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MerchantStoreBalanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店编码
     */
    private String storeNo;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenBalance;

    /**
     * 账户类型 0=现金余额，1=非现金账户
     * @see com.cosfo.manage.common.context.MerchantStoreBalanceEnums.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 资金账户名称
     */
    private String fundAccountName;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
