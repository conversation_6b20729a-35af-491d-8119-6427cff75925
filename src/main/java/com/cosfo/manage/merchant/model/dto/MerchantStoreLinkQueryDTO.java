package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2023/03/10 10:51
 */
@Data
public class MerchantStoreLinkQueryDTO {

    /**
     * 主键、自增  当前group_id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店id列表
     */
    private List<Long> storeIdList;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 详情地址
     */
    private String address;

}
