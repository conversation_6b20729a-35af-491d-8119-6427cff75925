package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 17:25
 * 取现配置列表
 */
@Data
public class QryCashConfigDTO implements Serializable {

    /**
     *业务类型
     */
    private String cash_type;
    /**
     *开关状态
     */
    private String switch_state;
    /**
     *提现手续费（固定/元）
     */
    private String fix_amt;
    /**
     *费率（百分比/%）
     */
    private String fee_rate;
    /**
     *手续费外扣标记
     */
    private String out_cash_flag;
    /**
     *手续费承担方
     */
    private String out_fee_huifuid;
    /**
     *取现手续费外扣时的账户类型
     */
    private String out_cash_acct_type;
}
