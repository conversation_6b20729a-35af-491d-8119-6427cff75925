package com.cosfo.manage.merchant.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 门店商品订货分析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Getter
@Setter
public class MerchantStoreItemOrderAnalysisExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    private String timeTag;

    /**
     * 1、周 2、月 3、季度
     */
    private Integer type;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 平均订货周期
     */
    private BigDecimal averageOrderPeriod;

    /**
     * 上周期平均订货周期
     */
    private BigDecimal averageOrderPeriodLastPeriod;

    /**
     * 平均订货周期较上周期
     */
    private String averageOrderPeriodUpperPeriod;

    /**
     * 订货数量
     */
    private Integer orderAmount;

    /**
     * 上周期订货数量
     */
    private Integer orderAmountLastPeriod;

    /**
     * 订货数量较上周期
     */
    private String orderAmountUpperPeriod;

    /**
     * 订货金额
     */
    private BigDecimal orderPrice;

    /**
     * 上周期订货金额
     */
    private BigDecimal orderPriceLastPeriod;

    /**
     * 订货金额较上周期
     */
    private String orderPriceUpperPeriod;

    /**
     * 最后订货日期 yyyy-MM-dd
     */
    private String lastOrderTime;

    /**
     * 最后订货数量
     */
    private Integer lastOrderAmount;

    /**
     * 最后订货金额
     */
    private BigDecimal lastOrderPrice;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private String storeType;

    /**
     * 门店分组
     */
    private String storeGroup;

}
