package com.cosfo.manage.merchant.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * merchant_store_detail_purchase
 * <AUTHOR>
@Data
public class MerchantStoreDetailPurchase implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签
     */
    private String timeTag;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Byte type;

    /**
     * 采购商品数
     */
    private Integer purchaseNum;

    /**
     * 采购金额
     */
    private BigDecimal purchasePrice;

    /**
     * 退款商品数
     */
    private Integer refundNum;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 店长手机号
     */
    private String phone;

    /**
     * 账期开关 1开启 0关闭
     */
    private Integer billSwitch;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 门店id
     */
    private Long merchantStoreId;

    /**
     * 门店ID
     */
    private String storeCode;

    private static final long serialVersionUID = 1L;
}
