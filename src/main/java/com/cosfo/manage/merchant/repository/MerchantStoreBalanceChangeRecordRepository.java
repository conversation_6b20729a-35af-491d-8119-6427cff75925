package com.cosfo.manage.merchant.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;

import java.util.List;

/**
 * <p>
 * 门店余额变动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface MerchantStoreBalanceChangeRecordRepository extends IService<MerchantStoreBalanceChangeRecord> {

    /**
     * 根据associatedOrderNos[订单号或者售后单号]查询
     * @param associatedOrderNos
     * @return
     */
    List<MerchantStoreBalanceChangeRecord> queryByAssociatedOrderNos(List<String> associatedOrderNos);
}
