package com.cosfo.manage.merchant.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.merchant.model.dto.balance.BalanceCompositionDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;
import com.cosfo.manage.merchant.model.vo.balance.BalanceCompositionVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 门店余额表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface MerchantStoreBalanceMapper extends BaseMapper<MerchantStoreBalance> {

    MerchantStoreBalance selectByStoreIdForUpdate(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);

    int updateBalanceByStoreId(@Param("tenantId")Long tenantId,@Param("storeId") Long storeId,@Param("changeBalance") BigDecimal changeBalance,
                               @Param("accountType") Integer accountType, @Param("fundAccountId") Long fundAccountId);

    MerchantStoreBalance selectByStoreId(@Param("tenantId") Long tenantId,@Param("storeId") Long storeId, @Param("accountType") Integer accountType,
                                         @Param("fundAccountId") Long fundAccountId);

    List<MerchantStoreBalance> selectByStoreIds(@Param("tenantId") Long tenantId,@Param("storeIds") List<Long> storeIds, @Param("accountType") Integer accountType);

    int batchInsert(@Param("balanceList") List<MerchantStoreBalance> newMerchantStoreBalanceList);

    List<BalanceCompositionVO> balanceComposition(BalanceCompositionDTO balanceCompositionDTO);

    List<MerchantStoreBalance> queryBalanceTotal(BalanceCompositionDTO balanceCompositionDTO);
}
