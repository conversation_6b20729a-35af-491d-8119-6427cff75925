package com.cosfo.manage.good.convert;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.good.model.dto.WarehousingRecordInput;
import com.cosfo.manage.good.model.vo.WarehousingRecordVO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import net.summerfarm.wms.saleinventory.dto.req.StockChangeRecordQueryReq;
import net.summerfarm.wms.saleinventory.dto.res.StockChangeRecordResp;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/12
 */
public class WarehouseRecordConvert {

    /**
     * 转换为WarehousingRecordVO
     *
     * @param stockChangeRecordResp
     * @return
     */
    public static WarehousingRecordVO convertToWarehousingRecordVO(StockChangeRecordResp stockChangeRecordResp, ProductSkuDTO productSkuDTO){
        if(Objects.isNull(stockChangeRecordResp)){
            return null;
        }

        WarehousingRecordVO warehousingRecordVO = new WarehousingRecordVO();
        warehousingRecordVO.setBatch(stockChangeRecordResp.getBatch());
        warehousingRecordVO.setGoodsName(stockChangeRecordResp.getGoodsName());
        warehousingRecordVO.setWarehouseNo(stockChangeRecordResp.getWarehouseNo());
        warehousingRecordVO.setWarehouseName(stockChangeRecordResp.getWarehouseName());
        warehousingRecordVO.setQuantity(stockChangeRecordResp.getQuantity());
        warehousingRecordVO.setStorageArea(stockChangeRecordResp.getStorageArea());
        if(ObjectUtil.isNotNull (productSkuDTO)){
            warehousingRecordVO.setSpecification (productSkuDTO.getSpecification ());
            warehousingRecordVO.setSpecificationUnit (productSkuDTO.getSpecificationUnit ());
            warehousingRecordVO.setSkuId (productSkuDTO.getId ());
            if (!StringUtils.isEmpty (productSkuDTO.getStorageTemperature ())) {
                warehousingRecordVO.setStorageStorage (productSkuDTO.getStorageTemperature ());
                warehousingRecordVO.setStorageStorageDesc (productSkuDTO.getStorageTemperature () + Constants.CENTIGRADE);
            }
            warehousingRecordVO.setCustomSkuCode (productSkuDTO.getCustomSkuCode ());
        }
        warehousingRecordVO.setProductionDate(stockChangeRecordResp.getProductionDate());
        warehousingRecordVO.setQualityDate(stockChangeRecordResp.getQualityDate());
        warehousingRecordVO.setChangeType(stockChangeRecordResp.getChangeType());
        warehousingRecordVO.setRecordTime(stockChangeRecordResp.getRecordTime());
        return warehousingRecordVO;
    }

    public static StockChangeRecordQueryReq convertToStockChangeRecordQueryReq(WarehousingRecordInput warehousingRecordInput){
        if (warehousingRecordInput == null) {
            return new StockChangeRecordQueryReq();
        }
        StockChangeRecordQueryReq stockChangeRecordQueryReq = new StockChangeRecordQueryReq();
        stockChangeRecordQueryReq.setPageNum(warehousingRecordInput.getPageIndex());
        stockChangeRecordQueryReq.setPageSize(warehousingRecordInput.getPageSize());
        stockChangeRecordQueryReq.setSkuId(warehousingRecordInput.getSkuId());
        if(Objects.nonNull(warehousingRecordInput.getWarehouseNo())) {
            stockChangeRecordQueryReq.setWarehouseNo(warehousingRecordInput.getWarehouseNo().intValue());
        }

        stockChangeRecordQueryReq.setOutStoreType(warehousingRecordInput.getOutOfStockType());
        stockChangeRecordQueryReq.setInStoreType(warehousingRecordInput.getWarehousingType());
        stockChangeRecordQueryReq.setGoodsName(warehousingRecordInput.getTitle());
        if(Objects.nonNull(warehousingRecordInput.getStartTime()) && Objects.nonNull(warehousingRecordInput.getEndTime())) {
            stockChangeRecordQueryReq.setStartDate(warehousingRecordInput.getStartTime().toLocalDate());
            stockChangeRecordQueryReq.setEndDate(warehousingRecordInput.getEndTime().toLocalDate());
        }
        return stockChangeRecordQueryReq;
    }
}
