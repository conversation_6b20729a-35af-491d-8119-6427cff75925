package com.cosfo.manage.good.convert;

import com.cosfo.manage.good.model.dto.ProductAgentRecordBySkuDTO;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.good.model.vo.ProductSkuAgentRecordDetailVO;
import com.cosfo.manage.good.model.vo.ProductSkuApplyDetailVO;
import com.cosfo.manage.good.model.vo.ProductSkuVo;
import com.cosfo.manage.good.model.vo.ProductSpuDetailVO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/6
 */
public class ProductAgentApplicationRecordConvert {

    /**
     * 转化为ProductSkuApplyDetailVO
     *
     * @param productAgentApplicationRecord
     * @param productSkuDTO
     * @return
     */
    public static ProductSkuApplyDetailVO convertToProductSkuApplyDetailVO(ProductAgentApplicationRecord productAgentApplicationRecord, ProductSkuDTO productSkuDTO) {
        if (productAgentApplicationRecord == null) {
            return null;
        }

        ProductSkuApplyDetailVO productSkuApplyDetailVO = new ProductSkuApplyDetailVO();
        productSkuApplyDetailVO.setRecordId(productAgentApplicationRecord.getId());
        productSkuApplyDetailVO.setStatus(productAgentApplicationRecord.getStatus());
        productSkuApplyDetailVO.setCommitTime(productAgentApplicationRecord.getCreateTime());
        productSkuApplyDetailVO.setApproveTime(productAgentApplicationRecord.getUpdateTime());
        productSkuApplyDetailVO.setReason(productAgentApplicationRecord.getAuditRemark());
        productSkuApplyDetailVO.setAgentTenantId(productSkuApplyDetailVO.getAgentTenantId());
        productSkuApplyDetailVO.setAgentTenantName("杭州鲜沐科技有限公司");
        if (Objects.nonNull(productSkuDTO)) {
            productSkuApplyDetailVO.setTitle(productSkuDTO.getTitle());
            productSkuApplyDetailVO.setMainPicture(productSkuDTO.getMainPicture());
            productSkuApplyDetailVO.setSpecification(productSkuDTO.getSpecification());
            productSkuApplyDetailVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
            productSkuApplyDetailVO.setId(productSkuDTO.getId());
            productSkuApplyDetailVO.setSpuId(productSkuDTO.getSpuId());
            productSkuApplyDetailVO.setCategoryId(productSkuDTO.getCategoryId());
            productSkuApplyDetailVO.setFirstCategory(productSkuDTO.getFirstCategory());
            productSkuApplyDetailVO.setSecondCategory(productSkuDTO.getSecondCategory());
            productSkuApplyDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
            productSkuApplyDetailVO.setPlaceType(productSkuDTO.getPlaceType());
            productSkuApplyDetailVO.setWeight(productSkuDTO.getWeight());
            productSkuApplyDetailVO.setWeightNotes(productSkuDTO.getWeightNotes());
            productSkuApplyDetailVO.setVolume(productSkuDTO.getVolume());
            productSkuApplyDetailVO.setVolumeUnit(productSkuDTO.getVolumeUnit());
            productSkuApplyDetailVO.setFirstCategory(productSkuDTO.getFirstCategory());
            productSkuApplyDetailVO.setSecondCategory(productSkuDTO.getSecondCategory());
            productSkuApplyDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
            productSkuApplyDetailVO.setCustomSkuCode(productSkuDTO.getCustomSkuCode());
            productSkuApplyDetailVO.setTaxRateValue(productSkuDTO.getTaxRateValue());
        }

        return productSkuApplyDetailVO;
    }

    /**
     * 转化为ProductSkuApplyDetailVO
     *
     * @param productAgentApplicationRecord
     * @param productSkuDTO
     * @return
     */
    public static ProductSkuApplyDetailVO convertToProductSkuApplyDetailVO(ProductAgentRecordBySkuDTO productAgentApplicationRecord, ProductSkuDTO productSkuDTO, List<TenantResultResp> tenantList) {
        if (productAgentApplicationRecord == null) {
            return null;
        }

        Map<Long, String> tenantMap = tenantList.stream().collect(Collectors.toMap(TenantResultResp::getId, TenantResultResp::getTenantName));

        ProductSkuApplyDetailVO productSkuApplyDetailVO = new ProductSkuApplyDetailVO();
        productSkuApplyDetailVO.setTenantId(productAgentApplicationRecord.getTenantId());
        productSkuApplyDetailVO.setTenantName(tenantMap.get(productSkuApplyDetailVO.getTenantId()));
        productSkuApplyDetailVO.setRecordId(productAgentApplicationRecord.getId());
        productSkuApplyDetailVO.setId(productAgentApplicationRecord.getSkuId());
        productSkuApplyDetailVO.setStatus(productAgentApplicationRecord.getStatus());
        productSkuApplyDetailVO.setCommitTime(productAgentApplicationRecord.getCreateTime());
        productSkuApplyDetailVO.setApproveTime(productAgentApplicationRecord.getUpdateTime());
        productSkuApplyDetailVO.setReason(productAgentApplicationRecord.getAuditRemark());
        productSkuApplyDetailVO.setAgentTenantId(productSkuApplyDetailVO.getAgentTenantId());
        productSkuApplyDetailVO.setFirstApplyTime(productAgentApplicationRecord.getFirstApplyTime());
        productSkuApplyDetailVO.setLastUpdateTime(productAgentApplicationRecord.getLastUpdateTime());
        productSkuApplyDetailVO.setAgentTenantName("杭州鲜沐科技有限公司");
        if (Objects.nonNull(productSkuDTO)) {
            productSkuApplyDetailVO.setTitle(productSkuDTO.getTitle());
            productSkuApplyDetailVO.setAgentSkuCode(productSkuDTO.getAgentSkuCode());
            productSkuApplyDetailVO.setMainPicture(productSkuDTO.getMainPicture());
            productSkuApplyDetailVO.setSpecification(productSkuDTO.getSpecification());
            productSkuApplyDetailVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
            productSkuApplyDetailVO.setSpecificationType(productSkuDTO.getSpecificationType());
            productSkuApplyDetailVO.setId(productSkuDTO.getId());
            productSkuApplyDetailVO.setSpuId(productSkuDTO.getSpuId());
            productSkuApplyDetailVO.setCategoryId(productSkuDTO.getCategoryId());
            productSkuApplyDetailVO.setFirstCategory(productSkuDTO.getFirstCategory());
            productSkuApplyDetailVO.setSecondCategory(productSkuDTO.getSecondCategory());
            productSkuApplyDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
            productSkuApplyDetailVO.setPlaceType(productSkuDTO.getPlaceType());
            productSkuApplyDetailVO.setWeight(productSkuDTO.getWeight());
            productSkuApplyDetailVO.setWeightNotes(productSkuDTO.getWeightNotes());
            productSkuApplyDetailVO.setVolume(productSkuDTO.getVolume());
            productSkuApplyDetailVO.setVolumeUnit(productSkuDTO.getVolumeUnit());
            productSkuApplyDetailVO.setFirstCategory(productSkuDTO.getFirstCategory());
            productSkuApplyDetailVO.setSecondCategory(productSkuDTO.getSecondCategory());
            productSkuApplyDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
            productSkuApplyDetailVO.setCustomSkuCode(productSkuDTO.getCustomSkuCode());
            productSkuApplyDetailVO.setTaxRateValue(productSkuDTO.getTaxRateValue());
            productSkuApplyDetailVO.setStorageLocation(productSkuDTO.getStorageLocation());
            productSkuApplyDetailVO.setStorageTemperature(productSkuDTO.getStorageTemperature());
        }

        return productSkuApplyDetailVO;
    }

    /**
     * 转化为ProductSkuApplyDetailVO
     *
     * @param productAgentApplicationRecord
     * @param productSkuDTO
     * @return
     */
    public static ProductSkuAgentRecordDetailVO convert2RecordDetail(ProductAgentApplicationRecord productAgentApplicationRecord, ProductSkuDTO productSkuDTO, String tenantName) {
        if (productAgentApplicationRecord == null) {
            return null;
        }

        ProductSkuAgentRecordDetailVO recordDetailVO = new ProductSkuAgentRecordDetailVO();
        recordDetailVO.setRecordId(productAgentApplicationRecord.getId());
        recordDetailVO.setCommitTime(productAgentApplicationRecord.getCreateTime());
        recordDetailVO.setApproveTime(productAgentApplicationRecord.getUpdateTime());
        recordDetailVO.setTenantId(productAgentApplicationRecord.getTenantId());
        recordDetailVO.setTenantName(tenantName);
        if (Objects.nonNull(productSkuDTO)) {
            ProductSpuDetailVO spuDetailVO = getSpuDetailVO(productSkuDTO);

            recordDetailVO.setSpuDetailVO(spuDetailVO);
        }

        return recordDetailVO;
    }

    public static ProductSpuDetailVO getSpuDetailVO(ProductSkuDTO productSkuDTO) {
        ProductSkuVo skuVo = new ProductSkuVo();
        skuVo.setId(productSkuDTO.getId());
        skuVo.setSpecification(productSkuDTO.getSpecification());
        skuVo.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
        skuVo.setPlaceType(productSkuDTO.getPlaceType());
        skuVo.setWeight(productSkuDTO.getWeight());
        skuVo.setWeightNotes(productSkuDTO.getWeightNotes());
        skuVo.setVolume(productSkuDTO.getVolume());
        skuVo.setVolumeUnit(productSkuDTO.getVolumeUnit());
        skuVo.setTaxRateValue(productSkuDTO.getTaxRateValue());
        skuVo.setCustomSkuCode(productSkuDTO.getCustomSkuCode());
        skuVo.setSkuMapping(productSkuDTO.getSkuMapping());
        skuVo.setUseFlag(productSkuDTO.getUseFlag());
        Optional.ofNullable(productSkuDTO.getSkuMapping()).ifPresent(sku -> {
            skuVo.setAgentSkuCode(sku.getSku());
        });

        ProductSpuDetailVO spuDetailVO = new ProductSpuDetailVO();
        spuDetailVO.setProductSkuVoList(Collections.singletonList(skuVo));
        spuDetailVO.setTenantId(productSkuDTO.getTenantId());
        spuDetailVO.setTitle(productSkuDTO.getTitle());
        spuDetailVO.setMainPicture(productSkuDTO.getMainPicture());
        spuDetailVO.setCategoryId(productSkuDTO.getCategoryId());
        spuDetailVO.setFirstCategory(productSkuDTO.getFirstCategory());
        spuDetailVO.setSecondCategory(productSkuDTO.getSecondCategory());
        spuDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
        spuDetailVO.setBrandName(productSkuDTO.getBrandName());
        spuDetailVO.setOrigin(productSkuDTO.getOrigin());
        spuDetailVO.setStorageLocation(productSkuDTO.getStorageLocation());
        spuDetailVO.setStorageTemperature(productSkuDTO.getStorageTemperature());
        spuDetailVO.setGuaranteePeriod(productSkuDTO.getGuaranteePeriod());
        spuDetailVO.setGuaranteeUnit(productSkuDTO.getGuaranteeUnit());
        return spuDetailVO;
    }
}
