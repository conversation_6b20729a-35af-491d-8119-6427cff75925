package com.cosfo.manage.good.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/12/25 16:43
 * @Description:
 */
@Data
public class AgentSkuBizLogVO implements Serializable {
    private static final long serialVersionUID = 1546722176169515687L;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 操作人ID
     */
    private Long operatorAuthUserId;

    /**
     * 操作人名称
     */
    private String operatorUserName;

    /**
     * 操作人手机号
     */
    private String operatorPhone;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 业务时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bizTime;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 货品快照
     */
    private ProductSpuDetailVO spuDetailVO;
}
