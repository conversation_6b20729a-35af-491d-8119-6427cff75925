package com.cosfo.manage.good.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuQueryInput {
    private List<Long> spuIds;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 申请状态 0待审核1已通过2已拒绝
     */
    private Integer status;

    /**
     * 是否代仓 0不是 1是
     */
    private Integer agentType;

    /**
     * 是否关联商品
     */
    private Integer associated;

    /**
     * skuIds
     */
    private List<Long> skuIds;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;
}
