package com.cosfo.manage.good.model.vo;

import com.cosfo.manage.product.model.dto.WarehouseSkuFenceAreaDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/12 18:14
 * @Description: 自营货品仓库列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelfGoodWarehouseDataVO implements Serializable {
    private static final long serialVersionUID = 6609389259040622726L;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库服务商
     */
    private String warehouseProvider;

    /**
     * 仓库所属租户id
     */
    private Long warehouseTenantId;

    /**
     * 覆盖城市数
     */
    private Integer cityNum;

    /**
     * 覆盖城市列表
     */
    private List<WarehouseSkuFenceAreaDTO> cityNames;

    /**
     * 可用库存
     */
    private Integer enabledTotalQuantity;

}
