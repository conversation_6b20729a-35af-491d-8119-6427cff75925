package com.cosfo.manage.good.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.manage.common.easy.excel.model.RowIndex;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 23:01
 */
@Data
public class ProductSkuExcelDataInput {

    /**
     * 自有编码
     */
    @ExcelProperty("自有编码(选填)")
    private String customSkuCode;

    /**
     * 货品名称（必填，最多32个字）
     */
    @ExcelProperty("货品名称（必填，最多32个字）")
    private String title;

    /**
     * 一级类目
     */
    @ExcelProperty("一级类目（必填）")
    private String firstCategoryName;

    /**
     * 二级类目
     */
    @ExcelProperty("二级类目（必填）")
    private String secondCategoryName;

    /**
     * 三级类目
     */
    @ExcelProperty("三级类目（必填）")
    private String thirdCategoryName;

    /**
     * 品牌名
     */
    @ExcelProperty("品牌（选填）")
    private String brandName;

    /**
     * 产地
     */
    @ExcelProperty("产地（选填）")
    private String origin;

    /**
     * 进口/国产
     */
    @ExcelProperty("进口/国产（必填）")
    private String placeTypeDesc;

    /**
     * 存储区域描述
     */
    @ExcelProperty("储存区域（必填）")
    private String storageLocationDesc;



    @ExcelProperty("保质期（必填）\n" +
            "选“定期”时，保质期时长/单位必填")
    private String guaranteePeriodType;

    /**
     * 保质期
     */
    @ExcelProperty("保质期时长")
    private String guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    @ExcelProperty("保质期单位")
    private String guaranteeUnitDesc;


    /**
     * 规格单位
     */
    @ExcelProperty("规格单位（必填）\n" +
            "即销售单位")
    private String specificationUnit;

    /**
     * 规格类型
     */
    @ExcelProperty("规格类型（必填）")
    private String specificationTypeDesc;


    /**
     * 规格
     */
    @ExcelProperty("规格名称（必填）")
    private String specification;



    /**
     * 是否需要代仓（代仓服务商为杭州鲜沐科技有限公司）
     */
    @ExcelProperty("是否同时申请代仓（必填）\n" +
            "代仓服务商为杭州鲜沐科技有限公司\n" +
            "选“是”时，长/宽/高/重量必填")
    private String agentApplication;

    /**
     * 长
     */
    @ExcelProperty("长（单位cm）\n" +
            "支持1位小数点")
    private String length;

    /**
     * 宽
     */
    @ExcelProperty("宽（单位cm）\n" +
            "支持1位小数点")
    private String width;

    /**
     * 高
     */
    @ExcelProperty("高（单位cm）\n" +
            "支持1位小数点")
    private String height;



    /**
     * 重量（单位KG，必填）
     */
    @ExcelProperty("重量（单位KG）\n" +
            "支持2位小数点")
    private String weight;

    /**
     * 错误信息
     */
    @ExcelIgnore
    private String errorMessage;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    @ExcelIgnore
    private Integer storageLocation;

    /**
     * 0 天 1 月 2 年
     */
    @ExcelIgnore
    private Integer guaranteeUnit;

    /**
     * 类目Id
     */
    @ExcelIgnore
    private Long categoryId;

    /**
     * 规格类型
     */
    @ExcelIgnore
    private Integer specificationType;

    /**
     * 地点类型0进口1国产
     */
    @ExcelIgnore
    private Integer placeType;

    /**
     * 体积单位
     */
    @ExcelIgnore
    private Long volumeUnit;

    /**
     * 存储温度
     */
    @ExcelIgnore
    private String storageTemperature;

    @ExcelIgnore
    private String volumeUnitDesc;


}
