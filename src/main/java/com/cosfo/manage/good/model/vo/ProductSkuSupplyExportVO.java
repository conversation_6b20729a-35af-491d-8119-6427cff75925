package com.cosfo.manage.good.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * 自营货品导出实体类
 * @author: xiaowk
 * @date: 2023/6/20 下午3:34
 */
@Data
public class ProductSkuSupplyExportVO {
    /**
     * 货品名称
     */
    private String title;
    /**
     * 成本价
     */
    private String price;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 商品数量
     */
    private Integer itemCount;
    /**
     * 商品编码
     */
    private Long itemId;
    /**
     * 上下架状态
     */
    private String onsaleFlag;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品规格
     */
    private String itemSpecification;
    /**
     * 商品规格单位
     */
    private String itemSpecificationUnit;
    /**
     * 售价
     */
    private String itemPrice;

}
