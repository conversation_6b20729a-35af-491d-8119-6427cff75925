package com.cosfo.manage.good.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@Data
public class ProductSpuListVo {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 货品名称
     */
    private String title;
    /**
     * 主图
     */
    private String mainPicture;

    private Long categoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;

    private Integer storageLocation;
    private String storageTemperature;
    private Integer guaranteePeriod;
    private Integer guaranteeUnit;
    private String origin;
    private String brandName;
    /**
     * sku列表
     */
    private List<ProductSkuVo> productSkuVoList;
}
