package com.cosfo.manage.good.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/12/18 15:00
 * @Description:
 */
@Data
public class ProductSkuAgentRecordDetailVO {

    /**
     * 申请记录ID
     */
    private Long recordId;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 提交时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime commitTime;
    /**
     * 审核时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 货品信息
     */
    private ProductSpuDetailVO spuDetailVO;
}
