package com.cosfo.manage.good.model.vo;

import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/9
 */
@Data
public class WarehousingRecordVO {
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 自有编码
     */
    private String customSkuCode;
    /**
     * 任务编号
     */
    private String taskNo;
    /**
     * 批次
     */
    private String batch;
    /**
     * 货品名称
     */
    private String goodsName;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 库存仓名称
     */
    private String warehouseName;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 变更类型
     */
    private String changeType;
    /**
     * 储存区域
     */
    private String storageArea;
    /**
     * 存储温度
     */
    private String storageStorage;
    /**
     * 存储温度
     */
    private String storageStorageDesc;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 保质期
     */
    private LocalDate qualityDate;
    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 仓库服务商
     */
    private String warehouseProvider;
}
