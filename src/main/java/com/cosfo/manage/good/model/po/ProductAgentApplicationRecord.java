package com.cosfo.manage.good.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货品代仓服务申请记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Getter
@Setter
@TableName("product_agent_application_record")
public class ProductAgentApplicationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 品牌方Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 代理租户Id
     */
    @TableField("agent_tenant_id")
    private Long agentTenantId;

    /**
     * 货品Id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 审核状态 0审核中 1通过 2拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;


}
