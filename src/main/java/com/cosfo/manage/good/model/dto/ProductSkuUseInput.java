package com.cosfo.manage.good.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2023/6/12 17:15
 * @Description:
 */
@Data
public class ProductSkuUseInput implements Serializable {
    private static final long serialVersionUID = 8684730002299440184L;
    /**
     * sku_id
     */
    @NotNull(message = "skuId不可为空")
    private Long skuId;

    /**
     * 停用状态 0-停用 1-启用
     */
    @NotNull(message = "停用状态不可为空")
    private Integer useFlag;
}
