package com.cosfo.manage.good.service;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;

import java.util.List;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2023/12/28 16:47
 * @Description:
 */
public interface SkuAgentLogService {
    /**
     * 查询货品信息快照
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    ProductSkuDTO getProductSkuDTO(Long tenantId, Long skuId);

    /**
     * 保存审核成功的日志
     *
     * @param skuId
     */
    void saveAuditSuccessBizLog(Long skuId);

    /**
     * 保存审核失败的日志
     *
     * @param skuId
     * @param refuseReason
     */
    void saveAuditFailBizLog(Long skuId, String refuseReason);

    /**
     * 保存代仓申请中时，sku编辑的日志
     *
     * @param skuId
     */
    void saveAgentSkuEditBizLog(Long tenantId, Long skuId);

    /**
     * 保存SPU时，保存日志
     *
     * @param tenantId
     * @param spu
     * @param skuList
     */
    void saveSpuBizLog(Long tenantId, ProductSpu spu, List<ProductSku> skuList);

    /**
     * 保存SPU时，保存日志
     *
     * @param tenantId
     * @param newSpu
     * @param oriSpu
     * @param skuList
     */
    void updateSpuBizLog(Long tenantId, ProductSpu newSpu, ProductSpu oriSpu, List<ProductSku> skuList);

    /**
     * 批量保存SKu
     *
     * @param tenantId
     * @param authUserId
     * @param skuList
     */
    void saveBatchSkuBizLog(Long tenantId, Long authUserId, List<ProductSku> skuList);

    /**
     * 自营货品导入时，保存日志
     * @param tenantId
     * @param authUserId
     * @param saveSpuContentMap
     * @param saveSkuContentMap
     * @param updateSpuContentMap
     * @param agentSkuContentMap
     */
    void saveImportSkuBizLog(Long tenantId, Long authUserId, Map<Long, String> saveSpuContentMap, Map<Long, String> saveSkuContentMap, Map<Long, String> updateSpuContentMap, Map<Long, String> agentSkuContentMap);
}
