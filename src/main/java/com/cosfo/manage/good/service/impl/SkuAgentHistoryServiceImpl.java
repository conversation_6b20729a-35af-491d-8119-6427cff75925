package com.cosfo.manage.good.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.BizLogConstants;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.BizLogFacade;
import com.cosfo.manage.good.convert.ProductAgentApplicationRecordConvert;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationRecordQueryConditionDTO;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.good.model.vo.AgentSkuBizLogVO;
import com.cosfo.manage.good.model.vo.ProductSpuDetailVO;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.cosfo.manage.good.service.SkuAgentHistoryService;
import com.cosfo.manage.good.service.SkuAgentLogService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.bizlog.SaveBizLogRecordReq;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/1/3 17:14
 * @Description:
 */
@Service
@Slf4j
public class SkuAgentHistoryServiceImpl implements SkuAgentHistoryService {
    @Resource
    private BizLogFacade bizLogFacade;
    @Resource
    private ProductAgentApplicationRecordDao agentApplicationRecordDao;
    @Resource
    private ProductAgentApplicationRecordService agentApplicationRecordService;
    @Resource
    private SkuAgentLogService skuAgentLogService;

    @Override
    public List<Long> listAllTenantIds() {
        return agentApplicationRecordDao.listAllTenantIds();
    }

    @Override
    public void createTenantHistoryRecord(Long tenantId, List<Long> skuIds) {
        log.info("准备为租户：{},skuIds:{}，清洗代仓申请的历史数据", tenantId, JSON.toJSONString(skuIds));
        // 查询已有的代仓记录
        ProductAgentApplicationRecordQueryConditionDTO recordQuery = ProductAgentApplicationRecordQueryConditionDTO.builder()
            .tenantId(tenantId)
            .skuIds(skuIds)
            .build();
        List<ProductAgentApplicationRecord> agentApplicationRecords = agentApplicationRecordDao.listByCondition(recordQuery);
        if (CollectionUtils.isEmpty(agentApplicationRecords)) {
            log.warn("租户：{}，skuIds:{}，未找到代仓申请记录，不再创建", tenantId, JSON.toJSONString(skuIds));
            return;
        }
        Map<Long, List<ProductAgentApplicationRecord>> agentRecordMap = agentApplicationRecords.stream()
            .sorted(Comparator.comparing(ProductAgentApplicationRecord::getCreateTime))
            .collect(Collectors.groupingBy(ProductAgentApplicationRecord::getSkuId));

        log.info("正在为租户：{},skuIds:{}，清洗代仓申请的历史数据", tenantId, JSON.toJSONString(agentRecordMap.keySet()));
        // 默认的登录信息
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantId);
        UserBase userBase = new UserBase();
        userBase.setSystemOrigin(SystemOriginEnum.COSFO_MANAGE.getType());
        UserInfoHolder.setUser(userBase);
        for (Long skuId : agentRecordMap.keySet()) {
            try {
                List<AgentSkuBizLogVO> agentSkuBizLogVOS = agentApplicationRecordService.listSkuAgentBizLog(skuId, loginContextInfoDTO);
                if (CollectionUtils.isNotEmpty(agentSkuBizLogVOS)) {
                    log.warn("租户：{}，skuId:{}，已存在代仓申请日志，不再创建", tenantId, skuId);
                    continue;
                }
                // 将最新的货品信息作为最后一条代仓申请的快照
                ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(tenantId, skuId);
                ProductSpuDetailVO spuDetailVO = ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO);

                List<ProductAgentApplicationRecord> skuRecordList = agentRecordMap.get(skuId);
                log.info("正在为租户：{},skuId:{}，清洗代仓申请的历史数据，历史记录有{}条", tenantId, skuId, skuRecordList.size());

                for (ProductAgentApplicationRecord record : skuRecordList) {
                    String operationName = BizLogConstants.getAgentOperationNameByStatus(record.getStatus());
                    if (Objects.isNull(operationName)) {
                        log.error("租户：{}，skuId:{}，recordId：{}未获取到正确的申请状态，无法生成日志", tenantId, skuId, record.getId());
                        continue;
                    }
                    String content = "";
                    // 审核拒绝需要有拒绝原因
                    if (operationName.equals(BizLogConstants.SKU_AGENT_REFUSE)) {
                        Map<String, Object> contentMap = new HashMap<>();
                        contentMap.put("skuInfo", JSON.toJSONString(spuDetailVO));
                        contentMap.put("refuseReason", record.getAuditRemark());
                        content = JSON.toJSONString(contentMap);
                    } else {
                        content = JSON.toJSONString(spuDetailVO);
                    }

                    SaveBizLogRecordReq logRecordReq = new SaveBizLogRecordReq();
                    logRecordReq.setSource(SystemOriginEnum.COSFO_MANAGE.getType());
                    logRecordReq.setOperationName(operationName);
                    logRecordReq.setBizKey(skuId.toString());
                    logRecordReq.setBizKeyTenantId(tenantId);
                    logRecordReq.setContent(content);
                    logRecordReq.setTenantId(tenantId);
                    logRecordReq.setCreatedTime(record.getUpdateTime());
                    logRecordReq.setCreatedId(BizLogConstants.NO_EXIST_USER_ID);
                    logRecordReq.setIp("");
                    bizLogFacade.createTenantHistoryRecord(logRecordReq);
                }
            } catch (Exception e) {
                log.error("租户：{},skuId:{}创建历史记录时发生异常！", tenantId, skuId, e);
            }
        }

    }


}
