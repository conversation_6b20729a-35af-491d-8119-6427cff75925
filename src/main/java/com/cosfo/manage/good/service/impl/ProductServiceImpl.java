package com.cosfo.manage.good.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cofso.item.client.enums.PriceStrategyTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.config.CustomConfig;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.*;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.easy.excel.helper.ChainDropDown;
import com.cosfo.manage.common.easy.excel.helper.ChainDropDownWriteHandler;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.*;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.*;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.good.convert.*;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.model.dto.*;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.good.model.vo.*;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.good.service.SkuAgentLogService;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.dao.ProductAgentSkuMappingDao;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.mapper.ProductSpuMapper;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.model.vo.ProductSkuDetailVO;
import com.cosfo.manage.product.repository.ProductSkuRepository;
import com.cosfo.manage.product.repository.ProductSpuRepository;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import com.cosfo.manage.product.service.ProductAgentSkuService;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.summerfarm.enums.CreateTypeEnums;
import com.cosfo.summerfarm.enums.ProductApplicationTypeEnum;
import com.cosfo.summerfarm.enums.ProductPropertyDevEnums;
import com.cosfo.summerfarm.enums.ProductPropertyEnums;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductApplicationDTO;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductApplicationItemDTO;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductAuditResultDTO;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgType;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.enums.GuaranteeUnitEnum;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.goods.client.message.GoodsUpdateMessageDTO;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.manage.client.inventory.InventoryWriteProvider;
import net.summerfarm.manage.client.inventory.dto.req.UpdateSkuOutdatedReqDTO;
import net.summerfarm.manage.client.inventory.enums.SkuOutdatedEnum;
import net.summerfarm.ofc.client.req.DistributionRecordReq;
import net.summerfarm.ofc.client.resp.DistributionRecordResp;
import net.summerfarm.pms.client.provider.PurchaseQueryProvider;
import net.summerfarm.wms.inventory.WarehouseInventoryProvider;
import net.summerfarm.wms.saleinventory.dto.req.StockChangeRecordQueryReq;
import net.summerfarm.wms.saleinventory.dto.res.StockChangeRecordResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Resource
    private ProductSkuRepository productSkuRepository;
    @Resource
    private ProductSpuRepository productSpuRepository;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    MqProducer mqProducer;
    @Resource
    private TenantService tenantService;
    @Resource
    private ProductAgentSkuMappingDao productAgentSkuMappingDao;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private MarketService marketService;
    @Resource
    private SaasInventoryFacade saasInventoryFacade;
    @Resource
    private DistributionRecordFacade distributionRecordFacade;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private CommonService commonService;
    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;
    @Lazy
    @Resource
    private ProductService productService;
    @Resource
    private ProductPricingSupplyService productPricingSupplyService;
    @Resource
    private ProductAgentSkuService productAgentSkuService;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private ProductSpuMapper productSpuMapper;
    @Resource
    private ProductSkuMapper productSkuMapper;

    @Resource
    private MarketItemBusinessService marketItemBusinessService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @DubboReference
    private InventoryWriteProvider inventoryWriteProvider;
    @DubboReference
    private WarehouseInventoryProvider warehouseInventoryProvider;
    @DubboReference
    private PurchaseQueryProvider purchaseQueryProvider;

    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SkuAgentLogService skuAgentLogService;
    @Resource
    private ProductAgentApplicationRecordService productAgentApplicationRecordService;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;
    @Resource
    private CustomConfig customConfig;
    @Resource
    private ProductStockForewarningReportRepository productStockForewarningReportRepository;


    @NacosValue(value = "${product.specification.unit.list:mL,L,G,KG,个,斤,箱,盒,包,袋,瓶,罐,桶,卷,块,片,颗,支,条,只,张,套,组,件,本,台,顶,份,捆}", autoRefreshed = true)
    public Set<String> specificationUnitList;


    @Override
    public Long addProduct(ProductSpuAddInput productSpuAddInput, LoginContextInfoDTO contextInfoDTO) {
        checkProductSpuAddInput(productSpuAddInput, contextInfoDTO.getTenantId());

        // 获取对应的税率
        BigDecimal taxRateValue = categoryServiceFacade.getTaxRate (productSpuAddInput.getCategoryId());
        productSpuAddInput.getProductSkuAddInputList().stream().forEach(el -> el.setTaxRateValue(taxRateValue));
        return productService.createProduct(productSpuAddInput, contextInfoDTO.getTenantId ());
    }

    private void checkProductSpuAddInput(ProductSpuAddInput productSpuAddInput, Long tenantId){
        if(CollectionUtils.isEmpty(customConfig.getCustomSkuCodeRequiredTenantIds()) || !customConfig.getCustomSkuCodeRequiredTenantIds().contains(tenantId)){
            return;
        }
        if (productSpuAddInput.getProductSkuAddInputList().stream().anyMatch(i -> StringUtils.isBlank(i.getCustomSkuCode()))) {
            throw new BizException("自有编码不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProduct(ProductSpuAddInput productSpuAddInput, Long tenantId) {
        //保存spu
        ProductSpu spu = buildSpu(productSpuAddInput, tenantId);
        productSpuRepository.save(spu);

        //保存sku
        List<ProductSku> skuList = buildSkus(productSpuAddInput.getProductSkuAddInputList(), spu.getId(), tenantId);
        productSkuRepository.saveBatch(skuList);
        asynchronousNotifySupplier(spu, skuList, ProductApplicationTypeEnum.APPLICATION);
        // 新增日志
        skuAgentLogService.saveSpuBizLog(tenantId, spu, skuList);
        return spu.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProduct(ProductSpuAddInput productSpuAddInput, Long tenantId) {
        log.info("spu发生更新，更新内容为：{}", JSON.toJSONString(productSpuAddInput));
//        ProductSpu spu = buildUpdateSpu(productSpuAddInput, tenantId);
        ProductSpu oriSpu = productSpuRepository.getById(productSpuAddInput.getId());

//        spu信息可随时修改
        ProductSpu spu = buildUpdateSpuWithInput(productSpuAddInput);
        productSpuRepository.updateById(spu);

        // 修改spu时，更新历史sku信息
        List<ProductSku> productSkus = productSkuRepository.listBySpuIds(Collections.singletonList(spu.getId()), tenantId);
        if (CollectionUtils.isNotEmpty(productSkus)) {
//            asynchronousNotifySupplier(spu, productSkus, ProductApplicationTypeEnum.REAPPLICATION_ITEM);
            //sku变更改为发送消息到货品中心
            asynchronousNotifyGoods(spu, productSkus);
        }

        List<ProductSku> skuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productSpuAddInput.getProductSkuAddInputList())) {
            // 新增规格时
            List<ProductSkuAddInput> adds = productSpuAddInput.getProductSkuAddInputList().stream().filter(e -> ObjectUtil.isNull(e.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adds)) {
                boolean queryTax = adds.stream().anyMatch(i -> Objects.isNull(i.getTaxRateValue()));
                if (queryTax) {
                    BigDecimal taxRateValue = categoryServiceFacade.getTaxRate(productSpuAddInput.getCategoryId());
                    adds.stream()
                        .filter(i -> Objects.isNull(i.getTaxRateValue()))
                        .forEach(el -> el.setTaxRateValue(taxRateValue));
                }
                skuList = buildSkus(adds, spu.getId(), tenantId);
                productSkuRepository.saveBatch(skuList);
                asynchronousNotifySupplier(spu, skuList, ProductApplicationTypeEnum.APPLICATION);
            }
        }

        // 保存日志
        skuAgentLogService.updateSpuBizLog(tenantId, spu, oriSpu, skuList);
    }

    /** 发送消息到货品中心 **/
    private void asynchronousNotifyGoods(ProductSpu spu, List<ProductSku> productSkus) {
        // 组装消息体
        productSkus.forEach(productSku -> {
            GoodsUpdateMessageDTO messageDTO = new GoodsUpdateMessageDTO();
            messageDTO.setTenantId(productSku.getTenantId());
            messageDTO.setSkuId(productSku.getId());
            messageDTO.setSku(productSku.getSku());
            messageDTO.setCategoryId(spu.getCategoryId());
            messageDTO.setTitle(spu.getTitle());
            messageDTO.setGuaranteePeriod(spu.getGuaranteePeriod());
            messageDTO.setGuaranteePeriodUnit(spu.getGuaranteeUnit());
            messageDTO.setMainPicture(spu.getMainPicture());
            messageDTO.setDetailPicture(spu.getDetailPicture());
            messageDTO.setStorageLocation(spu.getStorageLocation());
            messageDTO.setPlaceType(productSku.getPlaceType());
            messageDTO.setSpecification(productSku.getSpecification());
            messageDTO.setSpecificationUnit(productSku.getSpecificationUnit());
            messageDTO.setVolume(productSku.getVolume());
            if (productSku.getWeight() != null) {
                messageDTO.setWeight(BigDecimal.valueOf(productSku.getWeight()));
            }
            messageDTO.setOriginName(spu.getOrigin());
            messageDTO.setBrandName(spu.getBrandName());
            messageDTO.setStorageTemperature(spu.getStorageTemperature());
            // 异步通知供应商
            mqProducer.send("topic_goods_common", "saas_sku_sync", JSONObject.toJSONString(messageDTO));
        });
    }

    private ProductSpu buildUpdateSpuWithInput(ProductSpuAddInput input) {
        ProductSpu spu = new ProductSpu();
        spu.setId(input.getId());
        spu.setTitle(input.getTitle());
        spu.setCategoryId(input.getCategoryId());
        spu.setBrandName(input.getBrandName());
        spu.setOrigin(input.getOrigin());
        spu.setMainPicture(input.getMainPicture());
        spu.setGuaranteePeriod(input.getGuaranteePeriod());
        spu.setGuaranteeUnit(input.getGuaranteeUnit());
        spu.setStorageTemperature(input.getStorageTemperature());
        spu.setStorageLocation(input.getStorageLocation());
        return spu;
    }

    /**
     * 根据不同的代仓状态，校验不同的可修改字段
     * 不可修改的字段也会在参数中，在校验时如果有不可修改的字段会置null
     *
     * @param input
     * @param tenantId
     */
//    private ProductSpu buildUpdateSpu(ProductSpuAddInput input, Long tenantId) {
//        Long spuId = input.getId();
//        ProductSpu spu = productSpuRepository.getByIdAndTenantId(spuId, tenantId);
//        if (Objects.isNull(spu)) {
//            throw new DefaultServiceException("spu不存在");
//        }
//
//        List<ProductSku> productSkus = productSkuRepository.listBySpuIds(Collections.singletonList(input.getId()), tenantId);
//        if (CollectionUtils.isEmpty(productSkus)) {
//            return buildNoAgentSpu(input, spu);
//        }
//
//        List<ProductAgentApplicationRecord> agentRecords = productAgentApplicationRecordDao.listByCondition(ProductAgentApplicationRecordQueryConditionDTO.builder()
//            .tenantId(tenantId)
//            .skuIds(productSkus.stream().map(ProductSku::getId).collect(Collectors.toList()))
//            .build());
//        if (CollectionUtils.isEmpty(agentRecords)) {
//            return buildNoAgentSpu(input, spu);
//        }
//        Map<Long, List<ProductAgentApplicationRecord>> skuAgentMap = agentRecords.stream().collect(Collectors.groupingBy(ProductAgentApplicationRecord::getSkuId));
//        List<Integer> skuAgentCodes = skuAgentMap.keySet().stream()
//            .map(skuId -> {
//                List<ProductAgentApplicationRecord> skuAgentRecords = skuAgentMap.get(skuId);
//                if (CollectionUtils.isEmpty(skuAgentRecords)) {
//                    return SkuAgentStatusEnum.UN_APPLY.getCode();
//                } else {
//                    return skuAgentRecords.get(0).getStatus();
//                }
//            })
//            .collect(Collectors.toList());
//        Integer spuAgentCode = SpuAgentStatusEnum.getCodeBySkus(skuAgentCodes);
//        SpuAgentStatusEnum statusEnum = SpuAgentStatusEnum.getByCode(spuAgentCode);
//        if (Objects.isNull(statusEnum)) {
//            log.error("查询到错误的代仓状态码！spuId:{}", input.getId());
//            throw new BizException(ResultDTOEnum.SERVER_ERROR.getMessage());
//        }
//        switch (statusEnum) {
//            case APPLYING: {
//                return buildApplyingSpu(input, spu);
//            }
//            case APPROVED: {
//                return buildApprovedSpu(input, spu);
//            }
//            case NO_AGENT: {
//                return buildNoAgentSpu(input, spu);
//            }
//            default: {
//                log.error("查询到错误的代仓状态码！spuId:{}", input.getId());
//                throw new BizException(ResultDTOEnum.SERVER_ERROR.getMessage());
//            }
//        }
//    }

    private ProductSpu buildApplyingSpu(ProductSpuAddInput input, ProductSpu spu) {
        spu.setMainPicture(input.getMainPicture());
        return spu;
    }

    private ProductSpu buildApprovedSpu(ProductSpuAddInput input, ProductSpu spu) {
        spu.setBrandName(input.getBrandName());
        spu.setOrigin(input.getOrigin());
        spu.setMainPicture(input.getMainPicture());
        return spu;
    }

    private ProductSpu buildNoAgentSpu(ProductSpuAddInput input, ProductSpu spu) {
        spu.setTitle(input.getTitle());
        spu.setCategoryId(input.getCategoryId());
        spu.setBrandName(input.getBrandName());
        spu.setOrigin(input.getOrigin());
        spu.setMainPicture(input.getMainPicture());
        return spu;
    }

    @Override
    public PageInfo<ProductSpuListVo> pageOld(ProductQueryInput productQueryInput, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        Integer pageSize = productQueryInput.getPageSize();
        Integer pageNum = productQueryInput.getPageNum();

        // 查询类目
        Long categoryId = productQueryInput.getCategoryId();
        if (Objects.nonNull(categoryId)) {
            List<Long> categoryIds = productCategoryService.queryChildCategoryIdsOld (categoryId);
            if (CollectionUtils.isEmpty(categoryIds)) {
                return PageInfoHelper.createPageInfo(Collections.emptyList(), pageSize);
            }
            productQueryInput.setCategoryIds(categoryIds);
        }

        PageHelper.startPage(pageNum, pageSize);
        productQueryInput.setParamSkuId(productQueryInput.getSkuId());
        List<ProductSpu> spuList = productSpuRepository.listByCondition(productQueryInput, tenantId);
        if (CollectionUtils.isEmpty(spuList)){
            return PageInfoHelper.createPageInfo(Collections.emptyList(), pageSize);
        }
        List<ProductSku> skuList = productSkuRepository.listByCondition(ProductSkuQueryInput.builder()
            .spuIds(spuList.stream().map(ProductSpu::getId).collect(Collectors.toList()))
                .agentType(productQueryInput.getAgentType())
            .skuId(productQueryInput.getSkuId())
            .associated(productQueryInput.getAssociated())
            .useFlag(productQueryInput.getUseFlag())
            .build(), tenantId);

        return buildProductSpuVoPageInfo(spuList, skuList, pageSize,tenantId);
    }

    @Override
    public PageInfo<ProductSpuListVo> page(ProductQueryInput productQueryInput, LoginContextInfoDTO contextInfoDTO) {
        PageInfo<ProductSpuListVo> page = productFacade.pageSpu(productQueryInput, contextInfoDTO.getTenantId ());
        return page;
    }

    @Override
    public void export(ProductQueryInput productQueryInput, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        boolean dealFlag = dealProductQueryInputCondition(productQueryInput, tenantId);
        if(!dealFlag){
            throw new BizException("查询没有记录");
        }

        //生成对应的查询条件
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FIVE);
        if (!StringUtils.isBlank(productQueryInput.getTitle())) {
            queryParamsMap.put(Constants.GOODS_NAME, productQueryInput.getTitle());
        }
        if (Objects.nonNull(productQueryInput.getCategoryId())) {
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(productQueryInput.getCategoryId());
            queryParamsMap.put(Constants.CATEGORY, categoryDTO.getCategoryStr());
        }
        if (Objects.nonNull(productQueryInput.getSkuId())) {
            queryParamsMap.put(Constants.SKU_ID_UPPER, productQueryInput.getSkuId().toString());
        }
        if (Objects.nonNull(productQueryInput.getAssociated())) {
            queryParamsMap.put(Constants.ASSOCIATED_DESC, SkuAssociatedItemEnum.getByCode(productQueryInput.getAssociated()).getDesc());
        }
        if (Objects.nonNull(productQueryInput.getUseFlag())) {
            queryParamsMap.put(Constants.USEFLAG, SkuUseFlagEnum.getByCode(productQueryInput.getUseFlag()).getDesc());
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PRODUCT_SKU_SELF.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(ExcelTypeEnum.PRODUCT_SKU_SELF.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO)
                .asyncWriteWithOssResp(productQueryInput, ee -> {
            String filePath ;
            // 1、表格处理
            if (grayReleaseConfig.executeProductCenterGray(contextInfoDTO.getTenantId())) {
                filePath = generateProductSpuExportFile (productQueryInput, tenantId);
            }else{
                filePath = generateProductSpuExportFileOld (productQueryInput, tenantId);
            }
            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    private String generateProductSpuExportFile(ProductQueryInput productQueryInput, Long tenantId){
        Integer pageNum = 1;
        Integer pageSize = 200;
        boolean loopFlag = true;

        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.PRODUCT_SKU_SELF.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        AtomicInteger serialNumber = new AtomicInteger();

        while (loopFlag) {
            productQueryInput.setPageNum (pageNum);
            productQueryInput.setPageSize (pageSize);
            PageInfo<ProductSpuListVo> pageInfo = productFacade.pageSpu(productQueryInput, tenantId);
            if(CollectionUtils.isEmpty(pageInfo.getList ())){
                excelWriter.fill(Collections.emptyList(), writeSheet);
                break;
            }
            if(!pageInfo.isHasNextPage()){
                loopFlag = false;
            }
            pageNum++;
            List<ProductSkuExportVO> exportList = buildProductSkuExportVO(pageInfo.getList (), tenantId, serialNumber);
            excelWriter.fill(exportList, writeSheet);
        }

        excelWriter.finish();
        return filePath;
    }
    private String generateProductSpuExportFileOld(ProductQueryInput productQueryInput, Long tenantId){
        Integer pageNum = 1;
        Integer pageSize = 500;
        boolean loopFlag = true;

        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.PRODUCT_SKU_SELF.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        AtomicInteger serialNumber = new AtomicInteger();

        while (loopFlag) {
            PageHelper.startPage(pageNum, pageSize);
            List<ProductSpu> spus = productSpuRepository.listByCondition(productQueryInput, tenantId);
            if(CollectionUtils.isEmpty(spus)){
                loopFlag = false;
                excelWriter.fill(Collections.emptyList(), writeSheet);
                break;
            }
            PageInfo pageInfo = new PageInfo(spus);
            if(!pageInfo.isHasNextPage()){
                loopFlag = false;
            }
            pageNum++;
            List<Long> spuIds = spus.stream().map(ProductSpu::getId).collect(Collectors.toList());

            List<ProductSku> skus = productSkuRepository.listByCondition(ProductSkuQueryInput.builder()
                    .spuIds(spuIds)
                    .skuId(productQueryInput.getSkuId())
                    .associated(productQueryInput.getAssociated())
                    .useFlag(productQueryInput.getUseFlag())
                    .build(), tenantId);

            List<ProductSkuExportVO> exportList = buildProductSkuExportVOOld (spus, skus, tenantId, serialNumber);

            excelWriter.fill(exportList, writeSheet);
        }

        excelWriter.finish();

        return filePath;
    }
    private List<ProductSkuExportVO> buildProductSkuExportVO(List<ProductSpuListVo> list, Long tenantId, AtomicInteger serialNumber) {
        List<ProductSkuExportVO> resultList = Lists.newArrayList();
        for (ProductSpuListVo productSpu : list) {
            int currentIndex = serialNumber.incrementAndGet();
            List<ProductSkuVo> skus = productSpu.getProductSkuVoList ();
            if(CollectionUtil.isNotEmpty (skus)) {
                for (ProductSkuVo productSku : skus) {
                    ProductSkuExportVO vo = new ProductSkuExportVO ();
                    vo.setSerialNumber (String.valueOf (currentIndex));
                    vo.setId(productSpu.getId());
                    vo.setTitle(productSpu.getTitle());
                    vo.setCategoryId(productSpu.getCategoryId());
                    vo.setCategoryStr(productSpu.getFirstCategory ()+StringConstants.LEFT_SLASH+productSpu.getSecondCategory ()+StringConstants.LEFT_SLASH+productSpu.getThirdCategory ());
                    vo.setBrandName(productSpu.getBrandName());
                    vo.setOrigin(productSpu.getOrigin());
                    vo.setStorageLocation(productSpu.getStorageLocation());
                    vo.setStorageLocationStr(ProductSkuEnum.STORAGE_LOCATION.getDesc(productSpu.getStorageLocation()));
                    vo.setStorageTemperature(productSpu.getStorageTemperature());
                    vo.setGuaranteePeriod(productSpu.getGuaranteePeriod());
                    vo.setGuaranteeUnit(productSpu.getGuaranteeUnit());
                    vo.setGuaranteeUnitStr(ProductSkuEnum.GUARANTEE_UNIT.getDesc(productSpu.getGuaranteeUnit()));
                    vo.setGuaranteePeriodType(buildGuaranteePeriodType(productSpu.getGuaranteeUnit(), productSpu.getGuaranteePeriod()));

                    vo.setSkuId(productSku.getId());
                    if (productSku.getSpecificationType() != null) {
                        vo.setSpecificationType(SpecificationTypeEnums.getDescByType(productSku.getSpecificationType()));
                    }else {
                        vo.setSpecificationType("");
                    }
                    if(!StringUtils.isBlank(productSku.getSpecification())) {
                        if(productSku.getSpecification().contains(Constants.UNDERLINE)){
                            String[] values = productSku.getSpecification().split(Constants.UNDERLINE);
                            vo.setSpecification(values[1]);
                            vo.setSpecificationType(SpecificationTypeEnums.getDescByType(NumberUtils.toInt(values[0], -1)));
                        }else{
                            vo.setSpecification(productSku.getSpecification());
                        }
                    }
                    vo.setSpecificationUnit(productSku.getSpecificationUnit());
                    vo.setPlaceType(productSku.getPlaceType());
                    vo.setPlaceTypeStr(PlaceTypeEnums.getDescByType(productSku.getPlaceType()));
                    vo.setVolumeUnit(productSku.getVolumeUnit());
                    vo.setVolumeUnitStr(VolumeUnitEnums.getDescByVolumeUnit(productSku.getVolumeUnit()));
                    vo.setVolume(productSku.getVolume() + vo.getVolumeUnitStr());
                    vo.setWeight(productSku.getWeight());
                    if(productSku.getWeight() != null) {
                        vo.setWeightStr(productSku.getWeight() + "KG");
                    }
                    vo.setTaxRateValue(productSku.getTaxRateValue());
                    if(productSku.getTaxRateValue() != null) {
                        NumberFormat percent = NumberFormat.getPercentInstance();
                        percent.setMaximumFractionDigits(2);
                        vo.setTaxRateValueStr(percent.format(productSku.getTaxRateValue()));
                    }
                    vo.setHavingAppliedStr(Objects.equals (productSku.getAgentType (),AgentTypeEnum.SUMMERFARM_AGENT.getCode()) ? "已通过申请":"未申请/未通过");
                    vo.setCustomSkuCode(productSku.getCustomSkuCode());
                    vo.setUseFlag(productSku.getUseFlag());
                    vo.setUseFlagStr(SkuUseFlagEnum.getByCode(productSku.getUseFlag()).getDesc());
                    resultList.add (vo);
                }
            }
        }
        return resultList;
    }

    /** 超过100年/1200个月/36500天则为长期，否则为定期 **/
    private String buildGuaranteePeriodType(Integer guaranteeUnit, Integer guaranteePeriod) {
        final String longTerm = "长期";
        if (GuaranteeUnitEnum.YEAR.getValue().equals(guaranteeUnit) && guaranteePeriod != null && guaranteePeriod >= 100) {
            return longTerm;
        }
        if (GuaranteeUnitEnum.MONTH.getValue().equals(guaranteeUnit) && guaranteePeriod != null && guaranteePeriod >= 1200) {
            return longTerm;
        }
        if (GuaranteeUnitEnum.DAY.getValue().equals(guaranteeUnit) && guaranteePeriod != null && guaranteePeriod >= 36500) {
            return longTerm;
        }
        return "定期";
    }

    private List<ProductSkuExportVO> buildProductSkuExportVOOld(List<ProductSpu> spus, List<ProductSku> skus, Long tenantId, AtomicInteger serialNumber){
        if(CollectionUtils.isEmpty(spus) || CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }
        List<ProductSkuExportVO> resultList = Lists.newArrayList();

        Set<Long> categoryIdSet = spus.stream().map(ProductSpu::getCategoryId).collect(Collectors.toSet());
        Map<Long, ProductCategoryDTO> categoryIdMap = productCategoryService.batchQueryWholeCategory(categoryIdSet);

        Map<Long, List<ProductSku>> skuMap = skus.stream().collect(Collectors.groupingBy(ProductSku::getSpuId));

        List<Long> skuIds = skus.stream().map(ProductSku::getId).distinct().collect(Collectors.toList());

        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setSkuIds(skuIds);
        productAgentApplicationRecordQueryConditionDTO.setTenantId(tenantId);
        // 查询代仓申请记录
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        Map<Long, List<ProductAgentApplicationRecord>> listMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            listMap = productAgentApplicationRecords.stream().collect(Collectors.groupingBy(ProductAgentApplicationRecord::getSkuId));
        }

        for (ProductSpu productSpu : spus) {
            int currentIndex = serialNumber.incrementAndGet();
            Long spuId = productSpu.getId();

            resultList.addAll(buildProductSkuExportVOOld (currentIndex, productSpu, skuMap.get(spuId), categoryIdMap, listMap));
        }

        return resultList;
    }

    private List<ProductSkuExportVO> buildProductSkuExportVOOld(int serialNumber, ProductSpu spu, List<ProductSku> skus, Map<Long, ProductCategoryDTO> categoryIdMap, Map<Long, List<ProductAgentApplicationRecord>> listMap){
        if(spu == null || CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }

        List<ProductSkuExportVO> resultList = Lists.newArrayList();
        for (ProductSku productSku : skus) {
            ProductSkuExportVO vo = new ProductSkuExportVO();
            vo.setSerialNumber(String.valueOf(serialNumber));
            fillProductSkuExportVOBySpu(spu, vo, categoryIdMap);
            fillProductSkuExportVOBySku(productSku, vo, listMap);
            resultList.add(vo);
        }

        return resultList;
    }

    private void fillProductSkuExportVOBySpu(ProductSpu spu, ProductSkuExportVO vo, Map<Long, ProductCategoryDTO> categoryIdMap){
        if(spu == null){
            return;
        }
        vo.setId(spu.getId());
        vo.setTitle(spu.getTitle());
        vo.setCategoryId(spu.getCategoryId());
        if(spu.getCategoryId() != null) {
            ProductCategoryDTO productCategoryDTO = categoryIdMap.get(spu.getCategoryId());
            if (productCategoryDTO != null) {
                vo.setCategoryStr(productCategoryDTO.getCategoryStr());
            }
        }
        vo.setBrandName(spu.getBrandName());
        vo.setOrigin(spu.getOrigin());
        vo.setStorageLocation(spu.getStorageLocation());
        vo.setStorageLocationStr(ProductSkuEnum.STORAGE_LOCATION.getDesc(spu.getStorageLocation()));
        vo.setStorageTemperature(spu.getStorageTemperature());
        vo.setGuaranteePeriod(spu.getGuaranteePeriod());
        vo.setGuaranteeUnit(spu.getGuaranteeUnit());
        vo.setGuaranteeUnitStr(ProductSkuEnum.GUARANTEE_UNIT.getDesc(spu.getGuaranteeUnit()));
        vo.setGuaranteePeriodType(buildGuaranteePeriodType(spu.getGuaranteeUnit(),spu.getGuaranteePeriod()));
    }

    private void fillProductSkuExportVOBySku(ProductSku sku, ProductSkuExportVO vo, Map<Long, List<ProductAgentApplicationRecord>> listMap){
        if(sku == null){
            return;
        }
        vo.setSkuId(sku.getId());
        vo.setSpecification("");
        if (sku.getSpecificationType() != null) {
            vo.setSpecificationType(SpecificationTypeEnums.getDescByType(sku.getSpecificationType()));
        }else {
            vo.setSpecificationType("");
        }
        if(!StringUtils.isBlank(sku.getSpecification())) {
            if(sku.getSpecification().contains(Constants.UNDERLINE)){
                String[] values = sku.getSpecification().split(Constants.UNDERLINE);
                vo.setSpecification(values[1]);
                vo.setSpecificationType(SpecificationTypeEnums.getDescByType(NumberUtils.toInt(values[0], -1)));
            }else{
                vo.setSpecification(sku.getSpecification());
            }
        }
        vo.setSpecificationUnit(sku.getSpecificationUnit());
        vo.setPlaceType(sku.getPlaceType());
        vo.setPlaceTypeStr(PlaceTypeEnums.getDescByType(sku.getPlaceType()));
        vo.setVolumeUnit(sku.getVolumeUnit());
        vo.setVolumeUnitStr(VolumeUnitEnums.getDescByVolumeUnit(sku.getVolumeUnit()));
        vo.setVolume(sku.getVolume() + vo.getVolumeUnitStr());
        vo.setWeight(sku.getWeight());
        if(sku.getWeight() != null) {
            vo.setWeightStr(sku.getWeight().toString() + "KG");
        }
        vo.setTaxRateValue(sku.getTaxRateValue());
        if(sku.getTaxRateValue() != null) {
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(2);
            vo.setTaxRateValueStr(percent.format(sku.getTaxRateValue()));
        }

        // 是否申请过代仓服务
        Boolean havingApplied = false;
        Integer agentStatus = SkuAgentStatusEnum.UN_APPLY.getCode();
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = listMap.get(sku.getId());
        if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            havingApplied = true;
            agentStatus = productAgentApplicationRecords.get(0).getStatus();
        }
        vo.setHavingApplied(havingApplied);
        vo.setHavingAppliedStr(agentStatus.equals(SkuAgentStatusEnum.APPROVED.getCode()) ? "已通过申请":"未申请/未通过");
        vo.setCustomSkuCode(sku.getCustomSkuCode());
        vo.setUseFlag(sku.getUseFlag());
        vo.setUseFlagStr(SkuUseFlagEnum.getByCode(sku.getUseFlag()).getDesc());

    }

    private boolean dealProductQueryInputCondition(ProductQueryInput productQueryInput, Long tenantId){
        Long skuId = productQueryInput.getSkuId();

        // 查询类目
        Long categoryId = productQueryInput.getCategoryId();
        if (Objects.nonNull(categoryId)) {
            if (grayReleaseConfig.executeProductCenterGray(tenantId)) {
                List<Long> categoryIds = productCategoryService.queryChildCategoryIds (categoryId);
                if (CollectionUtils.isEmpty (categoryIds)) {
                    return false;
                }
                productQueryInput.setCategoryIds (categoryIds);
            }else {
                List<Long> categoryIds = productCategoryService.queryChildCategoryIdsOld (categoryId);
                if (CollectionUtils.isEmpty (categoryIds)) {
                    return false;
                }
                productQueryInput.setCategoryIds (categoryIds);
            }
        }

        if (!Objects.isNull(skuId)) {
            ProductSku sku = productSkuRepository.getByIdAndTenantId(skuId, tenantId);
            if (Objects.isNull(sku)) {
                return false;
            }
            Long spuId = sku.getSpuId();
            productQueryInput.setSpuId(spuId);
        }
        return true;
    }

    @Override
    public ProductSpuDetailVO getDetail(Long spuId,Long tenantId) {
        if (grayReleaseConfig.executeProductCenterGray(tenantId)) {
            ProductSpuDetailVO productSpuDetailVO = productFacade.selectProductSpuDetailById (spuId, tenantId);
            if(ObjectUtil.isEmpty (productSpuDetailVO)){
                return null;
            }
            List<ProductSkuVo> productSkuVoList = productSpuDetailVO.getProductSkuVoList ();
            if(CollectionUtil.isNotEmpty (productSkuVoList)) {
                ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO ();
                List<Long> skuIds = productSpuDetailVO.getProductSkuVoList ().stream ().map (ProductSkuVo::getId).collect (Collectors.toList ());
                productAgentApplicationRecordQueryConditionDTO.setSkuIds (skuIds);
                productAgentApplicationRecordQueryConditionDTO.setTenantId (tenantId);

                List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition (productAgentApplicationRecordQueryConditionDTO);
                Map<Long, List<ProductAgentApplicationRecord>> listMap = new HashMap<> ();
                if (!CollectionUtils.isEmpty (productAgentApplicationRecords)) {
                    listMap = productAgentApplicationRecords.stream ().collect (Collectors.groupingBy (ProductAgentApplicationRecord::getSkuId));
                }

                Map<Long, Boolean> skuIdDeleteTagMap = checkProductSkuExistBizRecordByMapping (productSpuDetailVO.getProductSkuVoList (), tenantId);

                Map<Long, List<ProductAgentApplicationRecord>> finalListMap = listMap;
                productSkuVoList.forEach (productSkuVo-> {
                    setAgentInfo(productSkuVo, finalListMap);
                    productSkuVo.setShowDeleteTag (!skuIdDeleteTagMap.get (productSkuVo.getId ()));
                });

                productSpuDetailVO.setAgentStatus(SpuAgentStatusEnum.getCodeBySkus(productSpuDetailVO.getProductSkuVoList().stream()
                        .map(ProductSkuVo::getAgentStatus)
                        .collect(Collectors.toList())));
            }
            return productSpuDetailVO;
        }else {
            ProductSpu spu = productSpuRepository.getByIdAndTenantId (spuId, tenantId);
            if (Objects.isNull (spu)) {
                return null;
            }
            List<ProductSku> skus = productSkuRepository.listBySpuIds (Collections.singletonList (spu.getId ()), tenantId);
            ProductSpuDetailVO productSpuDetailVO = buildProductSpuDetailVO (spu, skus, tenantId);

            if (!CollectionUtils.isEmpty (productSpuDetailVO.getProductSkuVoList ())) {
                List<Long> skuIdList = productSpuDetailVO.getProductSkuVoList ().stream ().map (ProductSkuVo::getId).collect (Collectors.toList ());
                Map<Long, Boolean> skuIdDeleteTagMap = checkProductSkuExistBizRecord (skuIdList, tenantId);

                productSpuDetailVO.getProductSkuVoList ().forEach (e -> {
                    e.setShowDeleteTag (!skuIdDeleteTagMap.get (e.getId ()));
                });
            }
            return productSpuDetailVO;
        }
    }

    private Map<Long, Boolean> checkProductSkuExistBizRecordByMapping(List<ProductSkuVo> productSkuVoList, Long tenantId) {

        // 可删除的skuId
        Set<Long> canDeleteSkuIdSet = productSkuVoList.stream ().map (ProductSkuVo::getId).collect(Collectors.toSet ());
        Map<Long, Boolean> resultMap = new HashMap<>();

        for (Long skuId : canDeleteSkuIdSet) {
            // 默认true，有关联业务数据, 不能删除
            resultMap.put(skuId, true);
        }

        // 查询货品关联商品
        List<MarketItemDTO> marketItemDTOS = marketItemService.queryBySkuIds(canDeleteSkuIdSet, tenantId);
        if (!CollectionUtils.isEmpty(marketItemDTOS)) {
            // 有关联商品，返回true
            for (MarketItemDTO marketItemDTO : marketItemDTOS) {
                if (canDeleteSkuIdSet.contains(marketItemDTO.getSkuId())) {
                    // 有关联商品的skuid，后续不用校验，从canDeleteSkuIdList删掉
                    canDeleteSkuIdSet.remove(marketItemDTO.getSkuId());
                }
            }
        }
        List<ProductsMappingResp> mappings = productSkuVoList.stream ().filter (e->ObjectUtil.isNotEmpty (e.getSkuMapping ())).map (ProductSkuVo::getSkuMapping).collect (Collectors.toList ());
        if (CollectionUtils.isNotEmpty (mappings)) {
            List<String> skuCodeList = mappings.stream().map(ProductsMappingResp::getSku).collect(Collectors.toList());
            Map<String, Long> skuCodeMap = mappings.stream().collect(Collectors.toMap(ProductsMappingResp::getSku, ProductsMappingResp::getSkuId));

            if (!CollectionUtils.isEmpty(skuCodeList)) {
                try {

                    DubboResponse<Map<String, Boolean>> resp = warehouseInventoryProvider.queryHasInventoryRecordBySkuCodeList(skuCodeList);
                    if (!resp.isSuccess()) {
                        log.error("queryHasInventoryRecordBySkuCodeList查询错误 skuCodeList={}, resp={}", skuCodeList, JSONObject.toJSONString(resp));
                        canDeleteSkuIdSet.clear();
                    } else {
                        if (resp.getData() != null) {
                            for (Entry<String, Boolean> entry : resp.getData().entrySet()) {
                                Boolean hasRecord = entry.getValue();
                                if (hasRecord == null || hasRecord) {
                                    canDeleteSkuIdSet.remove(skuCodeMap.get(entry.getKey()));
                                }
                            }
                        }
                    }


                    // 采购存在记录，sku不能删除
                    List<String> cannotDeleteSkuList = RpcResponseUtil.handler(purchaseQueryProvider.querySkuUseInPurchase(skuCodeList));
                    if(!CollectionUtils.isEmpty(cannotDeleteSkuList)) {
                        Set<Long> cannotDeleteSkuIdSet = cannotDeleteSkuList.stream().map(e -> skuCodeMap.get(e)).filter(Objects::nonNull).collect(Collectors.toSet());
                        canDeleteSkuIdSet.removeAll(cannotDeleteSkuIdSet);
                    }

                } catch (Exception e) {
                    log.error("queryInventoryBySkuCodeList查询错误 skuCodeList={} ", skuCodeList, e);
                    canDeleteSkuIdSet.clear();
                }
            }
        }

        // 所有校验通过，说明不存在业务记录的skuId，可以删除
        for (Long skuId : canDeleteSkuIdSet) {
            resultMap.put(skuId, false);
        }
        return resultMap;
    }

    /**
     * 检查skuId是否存在业务记录
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    private Map<Long, Boolean> checkProductSkuExistBizRecord(List<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        // 可删除的skuId
        Set<Long> canDeleteSkuIdSet = Sets.newHashSet(skuIds);
        Map<Long, Boolean> resultMap = new HashMap<>();

        for (Long skuId : canDeleteSkuIdSet) {
            // 默认true，有关联业务数据, 不能删除
            resultMap.put(skuId, true);
        }

        // 查询货品关联商品
        List<MarketItemDTO> marketItemDTOS = marketItemService.queryBySkuIds(canDeleteSkuIdSet, tenantId);
        if (!CollectionUtils.isEmpty(marketItemDTOS)) {
            // 有关联商品，返回true
            for (MarketItemDTO marketItemDTO : marketItemDTOS) {
                if (canDeleteSkuIdSet.contains(marketItemDTO.getSkuId())) {
                    // 有关联商品的skuid，后续不用校验，从canDeleteSkuIdList删掉
                    canDeleteSkuIdSet.remove(marketItemDTO.getSkuId());
                }
            }
        }

        List<ProductAgentSkuMapping> mappingList = productAgentSkuMappingDao.selectBySkuIds(canDeleteSkuIdSet, tenantId);
        List<String> skuCodeList = mappingList.stream().map(e -> e.getAgentSkuCode()).collect(Collectors.toList());
        Map<String, Long> skuCodeMap = mappingList.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuCode, e -> e.getSkuId()));

        if (!CollectionUtils.isEmpty(skuCodeList)) {
            try {

                DubboResponse<Map<String, Boolean>> resp = warehouseInventoryProvider.queryHasInventoryRecordBySkuCodeList(skuCodeList);
                if (!resp.isSuccess()) {
                    log.error("queryHasInventoryRecordBySkuCodeList查询错误 skuCodeList={}, resp={}", skuCodeList, JSONObject.toJSONString(resp));
                    canDeleteSkuIdSet.clear();
                } else {
                    if (resp.getData() != null) {
                        for (Entry<String, Boolean> entry : resp.getData().entrySet()) {
                            Boolean hasRecord = entry.getValue();
                            if (hasRecord == null || hasRecord) {
                                canDeleteSkuIdSet.remove(skuCodeMap.get(entry.getKey()));
                            }
                        }
                    }
                }


                // 采购存在记录，sku不能删除
                List<String> cannotDeleteSkuList = RpcResponseUtil.handler(purchaseQueryProvider.querySkuUseInPurchase(skuCodeList));
                if(!CollectionUtils.isEmpty(cannotDeleteSkuList)) {
                    Set<Long> cannotDeleteSkuIdSet = cannotDeleteSkuList.stream().map(e -> skuCodeMap.get(e)).filter(Objects::nonNull).collect(Collectors.toSet());
                    canDeleteSkuIdSet.removeAll(cannotDeleteSkuIdSet);
                }

            } catch (Exception e) {
                log.error("queryInventoryBySkuCodeList查询错误 skuCodeList={} ", skuCodeList, e);
                canDeleteSkuIdSet.clear();
            }
        }

        // 所有校验通过，说明不存在业务记录的skuId，可以删除
        for (Long skuId : canDeleteSkuIdSet) {
            resultMap.put(skuId, false);
        }
        return resultMap;
    }

    @Override
    public void deleteProductSku(Long skuId, LoginContextInfoDTO contextInfoDTO) {
        AssertCheckParams.notNull(skuId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");

        Long tenantId = contextInfoDTO.getTenantId();

        ProductSku productSku = productSkuMapper.selectByPrimaryKey(skuId);
        if (productSku == null) {
            throw new BizException("sku不存在");
        }

        // 校验是否存在业务记录或者关联商品上架
        Map<Long, Boolean> checkMap = checkProductSkuExistBizRecord(Lists.newArrayList(skuId), tenantId);
        // 存在业务记录
        if (checkMap.get(skuId)) {
            throw new BizException("sku存在业务记录，不能删除");
        }

        List<ProductAgentSkuMapping> productAgentSkuMappingList = productAgentSkuMappingDao.selectBySkuIds(Lists.newArrayList(skuId), tenantId);
        if (CollectionUtils.isEmpty(productAgentSkuMappingList)) {
            throw new BizException("请稍后重试删除");
        }

        String skuCode = productAgentSkuMappingList.get(0).getAgentSkuCode();
        UpdateSkuOutdatedReqDTO skuOutdatedReqDTO = new UpdateSkuOutdatedReqDTO();
        skuOutdatedReqDTO.setSku(skuCode);
        skuOutdatedReqDTO.setOutdated(SkuOutdatedEnum.IN_VALID.getCode());
        // 删除鲜沐sku
        DubboResponse resp = inventoryWriteProvider.updateSkuOutdated(Lists.newArrayList(skuOutdatedReqDTO));
        if (!resp.isSuccess()) {
            throw new BizException(resp.getMsg());
        }


        // 事务内执行
        AtomicReference<String> errorMsg = new AtomicReference<>();
        boolean executeResult = transactionTemplate.execute(status -> {
            Boolean result = true;
            try {

                // 删除sku
                productSkuMapper.deleteByPrimaryKey(skuId);
                log.warn("删除货品skuId={}, ProductSku={}", skuId, JSONObject.toJSONString(productSku));

                Long mappingId = productAgentSkuMappingList.get(0).getId();
                // 删除sku映射鲜沐sku的记录
                productAgentSkuMappingDao.deleteById(mappingId);
                log.warn("删除货品skuId={}, ProductAgentSkuMapping={}", skuId, JSONObject.toJSONString(productAgentSkuMappingList.get(0)));

                ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
                productAgentApplicationRecordQueryConditionDTO.setSkuIds(Arrays.asList(skuId));
                productAgentApplicationRecordQueryConditionDTO.setTenantId(tenantId);
                // 查询代仓申请记录
                List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
                if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
                    // 有代仓申请记录，删除
                    for (ProductAgentApplicationRecord record : productAgentApplicationRecords) {
                        productAgentApplicationRecordDao.delete(record.getId());
                        log.warn("删除货品skuId={}, ProductAgentApplicationRecord={}", skuId, JSONObject.toJSONString(record));
                    }
                }
            } catch (Exception e) {
                log.error("deleteProductSku error.", e);
                status.setRollbackOnly();
                if (e instanceof BizException) {
                    errorMsg.set(e.getMessage());
                } else {
                    errorMsg.set(ResultDTOEnum.SERVER_ERROR.getMessage());
                }
                result = false;
            }
            return result;
        });

        if (!executeResult) {
            throw new BizException(errorMsg.get());
        }

    }

    @Override
    public void deleteProduct(Long spuId, LoginContextInfoDTO contextInfoDTO) {
        AssertCheckParams.notNull(spuId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");

        Long tenantId = contextInfoDTO.getTenantId();
        List<ProductSku> skus = productSkuRepository.listBySpuIds(Collections.singletonList(spuId), tenantId);
        if (!CollectionUtils.isEmpty(skus)) {
            throw new BizException("存在规格信息，不能删除货品");
        }

        ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(spuId);
        if (productSpu != null) {
            // 删除spu
            productSpuMapper.deleteById(spuId);
            log.warn("删除货品spuId={}, ProductSpu={}", spuId, JSONObject.toJSONString(productSpu));
            BizLogRecordContext.put("spuTitle", productSpu.getTitle());
        }
    }

    //    @Override
//    public void applyCommit(Long skuId, LoginContextInfoDTO contextInfoDTO) {
//        ProductSku productSku = productSkuRepository.getById(skuId);
//        if (ObjectUtil.isNull(productSku)) {
//            return;
//        }
//        Integer approveStatus = productSku.getApproveStatus();
//        if (ApproveStatusEnum.REFUSE.getCode().equals(approveStatus)) {
//            productSku.setCommitTime(LocalDateTime.now());
//            productSku.setApproveStatus(ApproveStatusEnum.PREPARE.getCode());
//            productSku.setReason("");
//        }
//        Long spuId = productSku.getSpuId();
//        ProductSpu spu = productSpuRepository.getByIdAndTenantId(spuId, productSku.getTenantId());
//        asynchronousNotifySupplier(spu, Collections.singletonList(productSku));
//    }

    @Override
    public void receiveCallBackData(SummerfarmProductApplicationDTO summerfarmProductApplicationDto) {
        // 校验回调入参
        List<SummerfarmProductApplicationItemDTO> itemList = summerfarmProductApplicationDto.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            log.error("代仓申请回调参数缺失，流程结束");
            return;
        }

        for (SummerfarmProductApplicationItemDTO item : itemList) {
            Long skuId = item.getId();
            ProductSku sku = productSkuRepository.getById(skuId);
            sku.setSku(item.getSkuCode());
            productSkuMapper.updateByPrimaryKeySelective(sku);
            if (ObjectUtil.isNotNull(sku)) {
                // 新建代仓信息
                ProductAgentSkuMapping productAgentSkuMapping = new ProductAgentSkuMapping();
                productAgentSkuMapping.setTenantId(sku.getTenantId());
                productAgentSkuMapping.setAgentSkuId(item.getSupplierSkuId());
                productAgentSkuMapping.setSkuId(skuId);
                productAgentSkuMapping.setAgentTenantId(SupplierTenantEnum.SUMMERFARM.getId());
                productAgentSkuMapping.setAgentSkuCode(item.getSkuCode());
                productAgentSkuMapping.setSpuId(sku.getSpuId());
                productAgentSkuMapping.setAgentSpuCode(summerfarmProductApplicationDto.getSpu());
                productAgentSkuMapping.setAgentSpuId(summerfarmProductApplicationDto.getSupplierSpuId());
                productAgentSkuMappingDao.save(productAgentSkuMapping);
            }

            Object o = redisUtils.get (String.valueOf (skuId));
            if(ObjectUtil.isNotNull (o)){
                //异步创建代仓申请
                ProductAgentApplyingDTO productAgentApplyingDTO  = new ProductAgentApplyingDTO ();
                productAgentApplyingDTO.setAgentTenantId (XianmuSupplyTenant.TENANT_ID);
                productAgentApplyingDTO.setSkuId (skuId);
                LoginContextInfoDTO merchantInfoDTO = new LoginContextInfoDTO ();
                merchantInfoDTO.setTenantId (sku.getTenantId());
                productAgentApplicationRecordService.apply(productAgentApplyingDTO, merchantInfoDTO);

                marketService.add (JSON.parseObject (o.toString (),MarketSpuInput.class),sku.getTenantId());
                redisUtils.delete (String.valueOf (skuId));
            }
        }
        log.info("代仓申请回调处理完毕");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void receiveSummerfarmProductAuditResult(SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO) {
        Long supplierSkuId = summerfarmProductAuditResultDTO.getSkuId();
        Integer auditResult = summerfarmProductAuditResultDTO.getAuditResult();
        String refuseReason = summerfarmProductAuditResultDTO.getRefuseReason();
        LocalDateTime auditTime = summerfarmProductAuditResultDTO.getAuditTime();
        if (Objects.isNull(supplierSkuId) || Objects.isNull(auditResult)) {
            log.error("代仓服务申请回调参数为空，流程结束");
            return;
        }
        ProductAgentItemStatusEnum[] values = ProductAgentItemStatusEnum.values();
        if (Arrays.stream(values).noneMatch(el -> Objects.equals(el.getStatus(), auditResult))) {
            throw new BizException("代仓服务申请审核状态错误，抛出异常");
        }
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingDao.selectByAgentSkuId(supplierSkuId);
        if (CollectionUtils.isEmpty(productAgentSkuMappings)) {
            throw new BizException("货品映射关系不存在");
        }

        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappings.get(NumberConstant.ZERO);
        // 更新代仓服务申请记录
        ProductAgentApplicationRecord productAgentApplicationRecord = new ProductAgentApplicationRecord();
        productAgentApplicationRecord.setTenantId(productAgentSkuMapping.getTenantId());
        productAgentApplicationRecord.setSkuId(productAgentSkuMapping.getSkuId());
        productAgentApplicationRecord.setAgentTenantId(productAgentSkuMapping.getAgentTenantId());
        productAgentApplicationRecord.setStatus(auditResult);
        if (ProductAgentItemStatusEnum.FAIL.getStatus().equals(auditResult)) {
            productAgentApplicationRecord.setAuditRemark(refuseReason);
        }

        productAgentApplicationRecordDao.updateAuditResult(productAgentApplicationRecord);
        // 更新sku的agent_type
        if (ProductAgentItemStatusEnum.SUCCESS.getStatus().equals(auditResult)) {
            ProductSku productSku = new ProductSku();
            productSku.setId(productAgentSkuMapping.getSkuId());
            productSku.setAgentType(AgentTypeEnum.SUMMERFARM_AGENT.getCode());
            productSku.setSubAgentType(SubAgentTypeEnum.PROXY_WAREHOUSE.getValue());
            productSkuMapper.updateByPrimaryKeySelective(productSku);
        }
        // 记录日志
        skuAgentBizLog(auditResult,productAgentSkuMapping.getSkuId(),refuseReason);
        if (!Objects.equals(auditResult, ProductAgentItemStatusEnum.SUCCESS.getStatus())) {
            log.info("代仓申请未通过，流程结束");
            return;
        }

        log.info("代仓服务申请项：{}审核结果处理完毕supplierSkuId={}", supplierSkuId, supplierSkuId);

    }

    private void skuAgentBizLog(Integer auditResult,Long skuId,String refuseReason){
        log.info(">>>> 保存代仓审核日志：auditResult:{},skuId:{},refuseReason:{}",auditResult,skuId,refuseReason);
        if (ProductAgentItemStatusEnum.SUCCESS.getStatus().equals(auditResult)){
            skuAgentLogService.saveAuditSuccessBizLog(skuId);
        }else if (ProductAgentItemStatusEnum.FAIL.getStatus().equals(auditResult)){
            skuAgentLogService.saveAuditFailBizLog(skuId,refuseReason);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSku(ProductSpuAddInput input, LoginContextInfoDTO contextInfoDTO) {
        if (Objects.isNull(input)) {
            return;
        }
        if (CollectionUtils.isEmpty(input.getProductSkuAddInputList())) {
            return;
        }
        ProductSpu spu = productSpuRepository.getByIdAndTenantId(input.getId(), contextInfoDTO.getTenantId());
        if (Objects.isNull(spu)) {
            throw new BizException("未找到spu信息！");
        }
        if (Objects.isNull(input.getCategoryId()) || Objects.isNull(input.getId())) {
            throw new BizException("spuId、类目ID不可为空");
        }
        if (input.getProductSkuAddInputList().stream().anyMatch(i -> Objects.nonNull(i.getId()))) {
            throw new BizException("id不为空请走编辑接口");
        }

        checkProductSpuAddInput(input, contextInfoDTO.getTenantId());

        boolean queryTax = input.getProductSkuAddInputList().stream().anyMatch(i -> Objects.isNull(i.getTaxRateValue()));
        if (queryTax) {
            BigDecimal taxRateValue = categoryServiceFacade.getTaxRate(input.getCategoryId());
            input.getProductSkuAddInputList().stream()
                .filter(i -> Objects.isNull(i.getTaxRateValue()))
                .forEach(el -> el.setTaxRateValue(taxRateValue));
        }
        List<ProductSku> skuList = buildSkus(input.getProductSkuAddInputList(), input.getId(), contextInfoDTO.getTenantId());
        productSkuRepository.saveBatch(skuList);

        asynchronousNotifySupplier(spu, skuList, ProductApplicationTypeEnum.APPLICATION);

        // 保存日志
        skuAgentLogService.saveBatchSkuBizLog(contextInfoDTO.getTenantId(), contextInfoDTO.getAuthUserId(), skuList);
    }

    @Override
    public void updateSku(ProductSkuAddInput skuInput, LoginContextInfoDTO contextInfoDTO) {
        AssertCheckParams.notNull(skuInput.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "skuId不能为空");
        log.info("更新sku信息，skuInput={}", JSONObject.toJSONString(skuInput));
        Map<Long, List<MarketItemInfoResp>> longListMap = marketItemFacade.queryAssociatedItemBySkuIds (Collections.singleton (skuInput.getId ()), contextInfoDTO.getTenantId ());
        if(CollectionUtil.isNotEmpty (longListMap) && CollectionUtil.isNotEmpty (longListMap.get (skuInput.getId ()))){
            throw new BizException ("已经关联商品，不可修改");
        }
        ProductSku productSku = productSkuRepository.getById(skuInput.getId());
        if (ObjectUtil.isNull(productSku)) {
            return;
        }
        if (!productSku.getTenantId().equals(contextInfoDTO.getTenantId())) {
            throw new BizException(ResultDTOEnum.TENANT_ERROR.getMessage());
        }

        if(!Objects.equals (productSku.getSpecification (),skuInput.getSpecification()) || !Objects.equals (productSku.getSpecificationUnit (),skuInput.getSpecificationUnit())) {
            //校验是否有库存
            List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = productAgentSkuService.queryAgentProductWarehouseData (Collections.singletonList (skuInput.getId()), contextInfoDTO.getTenantId());
            int invCount = productAgentWarehouseDataVos.stream ().mapToInt (ProductAgentWarehouseDataVO::getEnabledQuantity).sum ();

            if (invCount != 0) {
                throw new BizException ("该sku还剩余" + invCount + "库存，无法修改规格");
            }
        }

        ProductSku oriSku = ProductInfoConvert.INSTANCE.copySku(productSku);

        //sku信息可随时修改
        productSku.setSpecificationType(skuInput.getSpecificationType());
        productSku.setSpecification(skuInput.getSpecification());
        productSku.setSpecificationUnit(skuInput.getSpecificationUnit());
        productSku.setPlaceType(skuInput.getPlaceType());
        productSku.setWeight(skuInput.getWeight());
        productSku.setWeightNotes(skuInput.getWeightNotes());
        productSku.setVolume(skuInput.getVolume());
        productSku.setVolumeUnit(skuInput.getVolumeUnit());
        productSku.setTaxRateValue(skuInput.getTaxRateValue());
        productSku.setCustomSkuCode(skuInput.getCustomSkuCode());
        productSku.setTaxRateValue(skuInput.getTaxRateValue());
        productSkuRepository.updateById(productSku);

        //如果是审核中修改最后提交时间
        List<ProductAgentApplicationRecord> agentRecords = productAgentApplicationRecordDao.listByCondition(ProductAgentApplicationRecordQueryConditionDTO.builder()
            .tenantId(productSku.getTenantId()).status(SkuAgentStatusEnum.APPLYING.getCode())
            .skuIds(Collections.singletonList(productSku.getId()))
            .build());
        if (!CollectionUtils.isEmpty(agentRecords)) {
            ProductAgentApplicationRecord record = agentRecords.get(0);
            record.setUpdateTime(LocalDateTime.now());
            productAgentApplicationRecordDao.updateById(record);
            skuAgentLogService.saveAgentSkuEditBizLog(contextInfoDTO.getTenantId(),productSku.getId());
        }

        BizLogRecordContext.put("newSku",productSku);
        BizLogRecordContext.put("oriSku",oriSku);

//        if (CollectionUtils.isEmpty(agentRecords)
//            || SkuAgentStatusEnum.REFUSE.getCode().equals(agentRecords.get(0).getStatus())) {
//            // 未申请代仓 或 已拒绝
//
//        } else if (SkuAgentStatusEnum.APPROVED.getCode().equals(agentRecords.get(0).getStatus())) {
//            // 已通过
//
//        } else {
//            throw new BizException("当前sku正在代仓申请中，不可修改！");
//        }

        Long spuId = productSku.getSpuId();
        ProductSpu spu = productSpuRepository.getByIdAndTenantId(spuId, productSku.getTenantId());

        asynchronousNotifyGoods(spu, Collections.singletonList(productSku));
//        asynchronousNotifySupplier(spu, Collections.singletonList(productSku), ProductApplicationTypeEnum.REAPPLICATION_ITEM);
    }

    /**
     * 异步通知供应商
     */
    public void asynchronousNotifySupplier(ProductSpu productSpu, List<ProductSku> productSkus, ProductApplicationTypeEnum productApplicationTypeEnum) {
        // 组装消息体
        productSkus.stream().forEach(productSku -> {
            SummerfarmProductApplicationDTO summerfarmProductApplicationDto = assembleMessage(productSpu, productSku, productApplicationTypeEnum);
            SummerfarmMsgModel summerfarmMsgModel = new SummerfarmMsgModel();
            summerfarmMsgModel.setMsgType(SummerfarmMsgType.PRODUCT_AGENT_APPLY);
            summerfarmMsgModel.setMsgData(summerfarmProductApplicationDto);

            // 异步通知供应商
            mqProducer.send(SummerfarmMQTopic.SAAS_TO_SUMMRFARM, null, JSONObject.toJSONString(summerfarmMsgModel));
        });
    }

    private SummerfarmProductApplicationDTO assembleMessage(ProductSpu productSpu, ProductSku productSku, ProductApplicationTypeEnum productApplicationTypeEnum) {
        TenantDTO tenantDTO = tenantService.queryTenantById(productSpu.getTenantId());
        SummerfarmProductApplicationDTO summerfarmProductApplicationDto = new SummerfarmProductApplicationDTO();
        summerfarmProductApplicationDto.setTenantId(productSpu.getTenantId());
        summerfarmProductApplicationDto.setProductApplicationTypeEnum(productApplicationTypeEnum);
        summerfarmProductApplicationDto.setId(productSku.getId());
        summerfarmProductApplicationDto.setAdminId(tenantDTO.getAdminId());
        summerfarmProductApplicationDto.setTenantName(tenantDTO.getTenantName());
        summerfarmProductApplicationDto.setTitle(productSpu.getTitle());
        summerfarmProductApplicationDto.setCategoryId(productSpu.getCategoryId());
        if (!StringUtils.isEmpty(productSpu.getBrandName())) {
            summerfarmProductApplicationDto.setBrandName(productSpu.getBrandName());
        } else {
            summerfarmProductApplicationDto.setBrandName("帆台默认品牌");
        }

        if (!StringUtils.isEmpty(productSpu.getOrigin())) {
            summerfarmProductApplicationDto.setOrigin(productSpu.getOrigin());
        } else {
            summerfarmProductApplicationDto.setOrigin("帆台默认产地");
        }

        summerfarmProductApplicationDto.setStorageLocation(ProductSkuEnum.STORAGE_LOCATION.convertToSummerfarmLocation(productSpu.getStorageLocation()));
        summerfarmProductApplicationDto.setStorageTemperature(productSpu.getStorageTemperature());
        if (Objects.equals(productSpu.getGuaranteeUnit(), ProductSkuEnum.GUARANTEE_UNIT.YEAR.getType())) {
            summerfarmProductApplicationDto.setGuaranteePeriod(productSpu.getGuaranteePeriod() * NumberConstant.TWELVE);
            summerfarmProductApplicationDto.setGuaranteeUnit(ProductSkuEnum.GUARANTEE_UNIT.MONTH.getType());
        } else {
            summerfarmProductApplicationDto.setGuaranteePeriod(productSpu.getGuaranteePeriod());
            summerfarmProductApplicationDto.setGuaranteeUnit(productSpu.getGuaranteeUnit());
        }
        //summerfarmProductApplicationDto.setApplicationType(ProductApplicationTypeEnum.APPLICATION.getType());
        summerfarmProductApplicationDto.setApplicationType(productApplicationTypeEnum.getType());
        String mainPicture = productSpu.getMainPicture();
        if (!Objects.isNull(mainPicture)) {
            // 图片上传
            String pic = Arrays.stream(mainPicture.split(","))
                .map(e -> "https://azure.cosfo.cn/" + e)
                .findFirst()
                .orElse("");
            summerfarmProductApplicationDto.setPicturePath(pic);
        }
//        summerfarmProductApplicationDto.setSupplierSpuId(productSpu.getSupplierSpuId());

        SummerfarmProductApplicationItemDTO item = new SummerfarmProductApplicationItemDTO();
        item.setSkuCode (productSku.getSku ());
        item.setId(productSku.getId());
        if (productSku.getSpecification().contains("_")) {
            String substring = productSku.getSpecification().substring(NumberConstant.TWO);
            item.setSpecification(substring);
        } else {
            item.setSpecification(productSku.getSpecification());
        }

        item.setSpecificationUnit(productSku.getSpecificationUnit());
        item.setWeightNum(Objects.isNull(productSku.getWeight()) ? BigDecimal.ONE : new BigDecimal(productSku.getWeight()));
        item.setVolume(StringUtils.isBlank(productSku.getVolume()) ? StringConstants.DEFAULT_VOLUME : productSku.getVolume());
        item.setDomesticFlag(productSku.getPlaceType());
        // 自营
        List<ProductAgentApplicationRecord> agentRecords = productAgentApplicationRecordDao.listByCondition(ProductAgentApplicationRecordQueryConditionDTO.builder()
            .tenantId(productSku.getTenantId()).status(SkuAgentStatusEnum.APPLYING.getCode())
            .skuIds(Collections.singletonList(productSku.getId()))
            .build());
        //代仓申请记录审核中才需要上新审核
        if (!CollectionUtil.isEmpty(agentRecords)) {
            item.setCreateTypeEnums(CreateTypeEnums.SELF_AND_AGENT);
        } else {
            item.setCreateTypeEnums(CreateTypeEnums.SELF);
        }

        Map<Integer, String> skuMap = assemblySkuDefaultProperty(item.getSpecification());
        item.setPropertyValues(skuMap);
        summerfarmProductApplicationDto.setItemList(Collections.singletonList(item));
        // 获取spu默认值
        Map<Integer, String> map = assemblySpuDefaultProperty(productSpu.getOrigin(), productSpu.getBrandName(), productSpu.getStorageTemperature());
        summerfarmProductApplicationDto.setPropertyValues(map);
        return summerfarmProductApplicationDto;
    }

    /**
     * 组装spu默认属性值
     *
     * @return
     */
    private Map<Integer, String> assemblySpuDefaultProperty(String origin, String brand, String storageTemperature) {
        Map<Integer, String> map = new HashMap<>();
        log.info("是否生产环境：{}", SpringContextUtil.isPro());
        if (SpringContextUtil.isPro()) {
            if (StringUtils.isEmpty(brand)) {
                brand = ProductPropertyEnums.BRAND.getDefaultValue();
            }

            if (StringUtils.isEmpty(origin)) {
                origin = ProductPropertyEnums.ORIGIN.getDefaultValue();
            }

            map.put(ProductPropertyEnums.ORIGIN.getCode(), origin);
            map.put(ProductPropertyEnums.BRAND.getCode(), brand);
            map.put(ProductPropertyEnums.STORAGE_TEMPERATURE.getCode(), storageTemperature);
            map.put(ProductPropertyEnums.OUTER_PACKING.getCode(), ProductPropertyEnums.OUTER_PACKING.getDefaultValue());
            map.put(ProductPropertyEnums.MEAT_VARIETY.getCode(), ProductPropertyEnums.MEAT_VARIETY.getDefaultValue());
            map.put(ProductPropertyEnums.COMMODITY_FORM.getCode(), ProductPropertyEnums.COMMODITY_FORM.getDefaultValue());
            map.put(ProductPropertyEnums.HUMIDNESS.getCode(), ProductPropertyEnums.HUMIDNESS.getDefaultValue());
            map.put(ProductPropertyEnums.SUGAR_CONTENT.getCode(), ProductPropertyEnums.SUGAR_CONTENT.getDefaultValue());
            map.put(ProductPropertyEnums.VEGETABLE_VARIETY.getCode(), ProductPropertyEnums.VEGETABLE_VARIETY.getDefaultValue());
            map.put(ProductPropertyEnums.GLUTEN_CONTENT.getCode(), ProductPropertyEnums.GLUTEN_CONTENT.getDefaultValue());
            map.put(ProductPropertyEnums.MILK_FAT_CONTENT.getCode(), ProductPropertyEnums.MILK_FAT_CONTENT.getDefaultValue());
            map.put(ProductPropertyEnums.USAGE_METHOD.getCode(), ProductPropertyEnums.USAGE_METHOD.getDefaultValue());
            map.put(ProductPropertyEnums.INGREDIENT.getCode(), ProductPropertyEnums.INGREDIENT.getDefaultValue());
            map.put(ProductPropertyEnums.SPU_TASTE.getCode(), ProductPropertyEnums.SPU_TASTE.getDefaultValue());
            map.put(ProductPropertyEnums.OTHER.getCode(), ProductPropertyEnums.OTHER.getDefaultValue());
            map.put(ProductPropertyEnums.SKU_TASTE.getCode(), ProductPropertyEnums.SKU_TASTE.getDefaultValue());
            map.put(ProductPropertyEnums.OTHER.getCode(), ProductPropertyEnums.OTHER.getDefaultValue());
            map.put(ProductPropertyEnums.BREED.getCode(), ProductPropertyEnums.BREED.getDefaultValue());
            map.put(ProductPropertyEnums.MATURITY.getCode(), ProductPropertyEnums.MATURITY.getDefaultValue());
            map.put(ProductPropertyEnums.PROTEIN_CONTENT.getCode(), ProductPropertyEnums.PROTEIN_CONTENT.getDefaultValue());
            map.put(ProductPropertyEnums.MILK_FAT_CONTENT_PERCENTAGE.getCode(), ProductPropertyEnums.MILK_FAT_CONTENT_PERCENTAGE.getDefaultValue());
            map.put(ProductPropertyEnums.FRUIT_GAUGE.getCode(), ProductPropertyEnums.FRUIT_GAUGE.getDefaultValue());
            map.put(ProductPropertyEnums.DIMENSION.getCode(), ProductPropertyEnums.DIMENSION.getDefaultValue());
            map.put(ProductPropertyEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getCode(), ProductPropertyEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getDefaultValue());
        } else {
            if (StringUtils.isEmpty(brand)) {
                brand = ProductPropertyDevEnums.BRAND.getDefaultValue();
            }

            if (StringUtils.isEmpty(origin)) {
                origin = ProductPropertyDevEnums.ORIGIN.getDefaultValue();
            }

            map.put(ProductPropertyDevEnums.ORIGIN.getCode(), origin);
            map.put(ProductPropertyDevEnums.BRAND.getCode(), brand);
            map.put(ProductPropertyDevEnums.STORAGE_TEMPERATURE.getCode(), storageTemperature);
            map.put(ProductPropertyDevEnums.SHELF_LEFE_DAYS.getCode(), ProductPropertyDevEnums.SHELF_LEFE_DAYS.getDefaultValue());
            map.put(ProductPropertyDevEnums.STORAGE_AREA.getCode(), ProductPropertyDevEnums.STORAGE_AREA.getDefaultValue());
            map.put(ProductPropertyDevEnums.OUTER_PACKING.getCode(), ProductPropertyDevEnums.OUTER_PACKING.getDefaultValue());
            map.put(ProductPropertyDevEnums.MEAT_VARIETY.getCode(), ProductPropertyDevEnums.MEAT_VARIETY.getDefaultValue());
            map.put(ProductPropertyDevEnums.COMMODITY_FORM.getCode(), ProductPropertyDevEnums.COMMODITY_FORM.getDefaultValue());
            map.put(ProductPropertyDevEnums.HUMIDNESS.getCode(), ProductPropertyDevEnums.HUMIDNESS.getDefaultValue());
            map.put(ProductPropertyDevEnums.SUGAR_CONTENT.getCode(), ProductPropertyDevEnums.SUGAR_CONTENT.getDefaultValue());
            map.put(ProductPropertyDevEnums.VEGETABLE_VARIETY.getCode(), ProductPropertyDevEnums.VEGETABLE_VARIETY.getDefaultValue());
            map.put(ProductPropertyDevEnums.GLUTEN_CONTENT.getCode(), ProductPropertyDevEnums.GLUTEN_CONTENT.getDefaultValue());
            map.put(ProductPropertyDevEnums.MILK_FAT_CONTENT.getCode(), ProductPropertyDevEnums.MILK_FAT_CONTENT.getDefaultValue());
            map.put(ProductPropertyDevEnums.USAGE_METHOD.getCode(), ProductPropertyDevEnums.USAGE_METHOD.getDefaultValue());
            map.put(ProductPropertyDevEnums.INGREDIENT.getCode(), ProductPropertyDevEnums.INGREDIENT.getDefaultValue());
            map.put(ProductPropertyDevEnums.SPU_TASTE.getCode(), ProductPropertyDevEnums.SPU_TASTE.getDefaultValue());
            map.put(ProductPropertyDevEnums.OTHER.getCode(), ProductPropertyDevEnums.OTHER.getDefaultValue());
            map.put(ProductPropertyDevEnums.SKU_TASTE.getCode(), ProductPropertyDevEnums.SKU_TASTE.getDefaultValue());
            map.put(ProductPropertyDevEnums.OTHER.getCode(), ProductPropertyDevEnums.OTHER.getDefaultValue());
            map.put(ProductPropertyDevEnums.BREED.getCode(), ProductPropertyDevEnums.BREED.getDefaultValue());
            map.put(ProductPropertyDevEnums.MATURITY.getCode(), ProductPropertyDevEnums.MATURITY.getDefaultValue());
            map.put(ProductPropertyDevEnums.PROTEIN_CONTENT.getCode(), ProductPropertyDevEnums.PROTEIN_CONTENT.getDefaultValue());
            map.put(ProductPropertyDevEnums.MILK_FAT_CONTENT_PERCENTAGE.getCode(), ProductPropertyDevEnums.MILK_FAT_CONTENT_PERCENTAGE.getDefaultValue());
            map.put(ProductPropertyDevEnums.FRUIT_GAUGE.getCode(), ProductPropertyDevEnums.FRUIT_GAUGE.getDefaultValue());
            map.put(ProductPropertyDevEnums.DIMENSION.getCode(), ProductPropertyDevEnums.DIMENSION.getDefaultValue());
            map.put(ProductPropertyDevEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getCode(), ProductPropertyDevEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getDefaultValue());
        }

        return map;
    }

    /**
     * 组装sku默认属性值
     *
     * @param specification
     * @return
     */
    public Map<Integer, String> assemblySkuDefaultProperty(String specification) {
        Map<Integer, String> map = new HashMap<>();
        if (SpringContextUtil.isPro()) {
            map.put(ProductPropertyEnums.SPECIFICATION.getCode(), specification);
            map.put(ProductPropertyEnums.COMMODITY_NATURE.getCode(), ProductPropertyEnums.COMMODITY_NATURE.getDefaultValue());
            map.put(ProductPropertyEnums.RANK.getCode(), ProductPropertyEnums.RANK.getDefaultValue());
            map.put(ProductPropertyEnums.SKU_TASTE.getCode(), ProductPropertyEnums.SKU_TASTE.getDefaultValue());
            map.put(ProductPropertyEnums.FRUIT_GAUGE.getCode(), ProductPropertyEnums.FRUIT_GAUGE.getDefaultValue());
            map.put(ProductPropertyEnums.DIMENSION.getCode(), ProductPropertyEnums.DIMENSION.getDefaultValue());
            map.put(ProductPropertyEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getCode(), ProductPropertyEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getDefaultValue());
            map.put(ProductPropertyEnums.CUSTOM_ATTRIBUTE.getCode(), ProductPropertyEnums.CUSTOM_ATTRIBUTE.getDefaultValue());
        } else {
            map.put(ProductPropertyDevEnums.SPECIFICATION.getCode(), specification);
            map.put(ProductPropertyDevEnums.COMMODITY_NATURE.getCode(), ProductPropertyDevEnums.COMMODITY_NATURE.getDefaultValue());
            map.put(ProductPropertyDevEnums.RANK.getCode(), ProductPropertyDevEnums.RANK.getDefaultValue());
            map.put(ProductPropertyDevEnums.SKU_TASTE.getCode(), ProductPropertyDevEnums.SKU_TASTE.getDefaultValue());
            map.put(ProductPropertyDevEnums.FRUIT_GAUGE.getCode(), ProductPropertyDevEnums.FRUIT_GAUGE.getDefaultValue());
            map.put(ProductPropertyDevEnums.DIMENSION.getCode(), ProductPropertyDevEnums.DIMENSION.getDefaultValue());
            map.put(ProductPropertyDevEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getCode(), ProductPropertyDevEnums.INGREDIENTS_OR_FINISHED_PRODUCTS.getDefaultValue());
            map.put(ProductPropertyDevEnums.CUSTOM_ATTRIBUTE.getCode(), ProductPropertyDevEnums.CUSTOM_ATTRIBUTE.getDefaultValue());
        }

        return map;
    }

    private ProductSpuDetailVO buildProductSpuDetailVO(ProductSpu spu, List<ProductSku> skus, Long tenantId) {

        ProductSpuDetailVO productSpuDetailVO = new ProductSpuDetailVO();
        productSpuDetailVO.setId(spu.getId());
        productSpuDetailVO.setTitle(spu.getTitle());
        productSpuDetailVO.setCategoryId(spu.getCategoryId());
        ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(spu.getCategoryId());
        productSpuDetailVO.setFirstCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getFirstCategoryName());
        productSpuDetailVO.setSecondCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getSecondCategoryName());
        productSpuDetailVO.setThirdCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getThirdCategoryName());
        productSpuDetailVO.setBrandName(spu.getBrandName());
        productSpuDetailVO.setOrigin(spu.getOrigin());
        productSpuDetailVO.setStorageLocation(spu.getStorageLocation());
        productSpuDetailVO.setStorageTemperature(spu.getStorageTemperature());
        productSpuDetailVO.setGuaranteePeriod(spu.getGuaranteePeriod());
        productSpuDetailVO.setGuaranteeUnit(spu.getGuaranteeUnit());
        productSpuDetailVO.setMainPicture(spu.getMainPicture());
        if (CollectionUtil.isNotEmpty(skus)) {
            // 查询货品关联商品
            List<Long> skuIds = skus.stream().map(ProductSku::getId).collect(Collectors.toList());

            ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
            productAgentApplicationRecordQueryConditionDTO.setSkuIds(skuIds);
            productAgentApplicationRecordQueryConditionDTO.setTenantId(tenantId);
            // 查询代仓申请记录
            List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
            Map<Long, List<ProductAgentApplicationRecord>> listMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
                listMap = productAgentApplicationRecords.stream().collect(Collectors.groupingBy(ProductAgentApplicationRecord::getSkuId));
            }
            productSpuDetailVO.setProductSkuVoList(buildSkuVo(skus, listMap));
            productSpuDetailVO.setAgentStatus(SpuAgentStatusEnum.getCodeBySkus(productSpuDetailVO.getProductSkuVoList().stream()
                .map(ProductSkuVo::getAgentStatus)
                .collect(Collectors.toList())));
        }
        return productSpuDetailVO;
    }



    private PageInfo<ProductSpuListVo> buildProductSpuVoPageInfo(List<ProductSpu> spus, List<ProductSku> skus, Integer pageSize,Long tenantId) {
        Map<Long, List<ProductSku>> skuMap;
        if (!CollectionUtils.isEmpty(skus)) {
            skuMap = skus.stream().collect(Collectors.groupingBy(ProductSku::getSpuId));
        } else {
            skuMap = Collections.emptyMap();
        }
        List<ProductSpuListVo> result = spus.stream().map(e -> {
            ProductSpuListVo productSpuListVo = new ProductSpuListVo();
            productSpuListVo.setId(e.getId());
            productSpuListVo.setTitle(e.getTitle());
            productSpuListVo.setMainPicture(e.getMainPicture());
            // todo 待优化
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(e.getCategoryId());
            productSpuListVo.setFirstCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getFirstCategoryName());
            productSpuListVo.setSecondCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getSecondCategoryName());
            productSpuListVo.setThirdCategory(Objects.isNull(categoryDTO) ? "" : categoryDTO.getThirdCategoryName());
            productSpuListVo.setProductSkuVoList(buildSkuVo(skuMap.get(e.getId()), Collections.emptyMap()));
            return productSpuListVo;
        }).sorted(Comparator.comparing(ProductSpuListVo::getId).reversed()).collect(Collectors.toList());
        PageInfo pageInfo = PageInfoHelper.createPageInfo(spus, pageSize);
        pageInfo.setList(result);
        return pageInfo;
    }

    private List<ProductSkuVo> buildSkuVo(List<ProductSku> productSkus, Map<Long, List<ProductAgentApplicationRecord>> listMap) {
        if (CollectionUtils.isEmpty(productSkus)) {
            return null;
        }
        return productSkus.stream().map(e -> {
            ProductSkuVo productSkuVo = new ProductSkuVo();
            productSkuVo.setId(e.getId());
            productSkuVo.setSpecification(e.getSpecification());
            productSkuVo.setSpecificationUnit(e.getSpecificationUnit());
            productSkuVo.setSpecificationType(e.getSpecificationType());
            productSkuVo.setPlaceType(e.getPlaceType());
            productSkuVo.setVolume(e.getVolume());
            productSkuVo.setVolumeUnit(e.getVolumeUnit());
            productSkuVo.setWeight(e.getWeight());
            productSkuVo.setWeightNotes(e.getWeightNotes());
            productSkuVo.setStatus(e.getApproveStatus());
            productSkuVo.setReason(!ApproveStatusEnum.REFUSE.getCode().equals(e.getApproveStatus()) ? "" : e.getReason());
            productSkuVo.setAmount(e.getAmount());
            productSkuVo.setHavingRelated(e.getAssociated());
            productSkuVo.setTaxRateValue(e.getTaxRateValue());
            productSkuVo.setCustomSkuCode(e.getCustomSkuCode());
            productSkuVo.setUseFlag(e.getUseFlag());
            productSkuVo.setAgentType(e.getAgentType());
            setAgentInfo(productSkuVo,listMap);
            return productSkuVo;
        }).collect(Collectors.toList());
    }
    private void setAgentInfo(ProductSkuVo productSkuVo,Map<Long, List<ProductAgentApplicationRecord>> listMap){
        // 是否申请过代仓服务
        Boolean havingApplied = false;
        Integer agentStatus = SkuAgentStatusEnum.UN_APPLY.getCode();
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = listMap.get(productSkuVo.getId());
        if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            havingApplied = true;
            agentStatus = productAgentApplicationRecords.get(0).getStatus();
        }
        productSkuVo.setAgentStatus(agentStatus);
        productSkuVo.setStatus (agentStatus);
        productSkuVo.setHavingApplied(havingApplied);
    }
    private List<ProductSku> buildSkus(List<ProductSkuAddInput> productSkuAddInputList, Long spuId, Long tenantId) {
        Long adminId = Optional.ofNullable(tenantService.queryTenantById(tenantId))
                .map(TenantDTO::getAdminId)
                .orElse(null);

        return productSkuAddInputList.stream().map(e -> buildSku(e, spuId, tenantId, adminId))
                .collect(Collectors.toList());
    }

    private ProductSku buildSku(ProductSkuAddInput e, Long spuId, Long tenantId, Long adminId) {
        ProductSku productSku = new ProductSku();
        productSku.setId(e.getId());
        productSku.setApproveStatus(ApproveStatusEnum.PASS.getCode());
        // productSku.setApproveTime(LocalDateTime.now());
        // productSku.setAmount(e.getAmount());
        // productSku.setApproveStatus(ApproveStatusEnum.PREPARE.getCode());
        if (e.getAgentType() == null) {
            productSku.setAgentType(AgentTypeEnum.NO.getCode());
        }
        productSku.setCommitTime(LocalDateTime.now());
        productSku.setPlaceType(e.getPlaceType());
        productSku.setVolume(e.getVolume());
        productSku.setWeight(e.getWeight());
        productSku.setTenantId(tenantId);
        productSku.setSpuId(spuId);
        productSku.setSpecification(e.getSpecification());
        productSku.setSpecificationUnit(e.getSpecificationUnit());
        productSku.setWeightNotes(e.getWeightNotes());
        productSku.setVolumeUnit(e.getVolumeUnit());
        productSku.setTaxRateValue(e.getTaxRateValue());
        productSku.setCustomSkuCode(e.getCustomSkuCode());
        productSku.setSku (e.getSkuCode ());
        productSku.setSpecificationType(e.getSpecificationType());
        if (adminId != null) {
            productSku.setOwnerId(adminId.intValue());
        }
        return productSku;
    }

    private ProductSpu buildSpu(ProductSpuAddInput input, Long tenantId) {
        ProductSpu productSpu = new ProductSpu();
        productSpu.setId(input.getId());
        productSpu.setTenantId(tenantId);
        productSpu.setCategoryId(input.getCategoryId());
        productSpu.setBrandName(input.getBrandName());
        productSpu.setTitle(input.getTitle());
        productSpu.setMainPicture(input.getMainPicture());
        productSpu.setDetailPicture(input.getDetailPicture());
        productSpu.setStorageLocation(input.getStorageLocation());
        productSpu.setStorageTemperature(input.getStorageTemperature());
        productSpu.setGuaranteePeriod(input.getGuaranteePeriod());
        productSpu.setGuaranteeUnit(input.getGuaranteeUnit());
        productSpu.setOrigin(input.getOrigin());
        return productSpu;
    }

    @Override
    public Map<String, Long> createMallProduct(Long skuId, Long classificationId, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(classificationId)) {
            throw new BizException("商品分组不能为空");
        }

        // 查询代仓品
        // 查询出模板商品的信息
        ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(skuId);
        AssertCheckParams.notNull(productSkuDTO, ResultDTOEnum.PRODUCT_NOT_FOUND.getCode(), ResultDTOEnum.PRODUCT_NOT_FOUND.getMessage());
        MarketSpuInput marketSpuInput = new MarketSpuInput();
        marketSpuInput.setCategoryId(productSkuDTO.getCategoryId());
        marketSpuInput.setTitle(productSkuDTO.getTitle());
        marketSpuInput.setSubTitle(productSkuDTO.getSubTitle());
        marketSpuInput.setMainPicture(productSkuDTO.getMainPicture());
        marketSpuInput.setClassificationId(classificationId);
        MarketAddResDTO addRes = marketService.add(marketSpuInput, loginContextInfoDTO.getTenantId());
        if (Objects.isNull(addRes)) {
            throw new BizException("商品新增失败！");
        }
        Long marketId = addRes.getMarketId();

        MarketItemInput insertSkuDTO = new MarketItemInput();
        insertSkuDTO.setMarketId(marketId);
        // 此接口的Controller已废弃，如需要使用，增加insertSkuDTO.setGoodsType;
        //insertSkuDTO.setWarehouseType(WarehouseTypeEnum.NO_WAREHOUSE.getCode());
        //insertSkuDTO.setDeliveryType(DeliveryTypeEnum.THIRD_DELIVERY.getCode());
        insertSkuDTO.setPriceType(StorePriceTypeEnum.ALL.getCode());
        insertSkuDTO.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
        insertSkuDTO.setSkuId(skuId);
        insertSkuDTO.setAfterSaleUnit(productSkuDTO.getSpecificationUnit());
        insertSkuDTO.setMaxAfterSaleAmount(NumberConstants.ONE);
        insertSkuDTO.setMiniOrderQuantity(NumberConstants.ONE);

        Long itemId = marketItemService.save(insertSkuDTO, loginContextInfoDTO.getTenantId());

        Map<String, Long> skuInfoMap = new HashMap<>(NumberConstants.TWO);
        skuInfoMap.put(Constants.SKU_ID, itemId);
        skuInfoMap.put(Constants.SPU_ID, marketId);
        return skuInfoMap;
    }

    @Override
    public ItemIdAndSkuIdVO createDefaultMarketItemByProductSkuId(Long skuId, Long marketId, Long classificationId, Long tenantId) {
        if (skuId == null || classificationId == null || tenantId == null) {
            throw new BizException("参数不能为空");
        }

        List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(tenantId, Collections.singletonList(skuId));
        if(!CollectionUtils.isEmpty(marketItems)){
            log.warn("商品信息已存在 tenantId={}, skuId={}", tenantId, skuId);
            return null;
        }

        // 查询代仓品
        // 查询出模板商品的信息
        ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(skuId);
        if(productSkuDTO == null || !tenantId.equals(productSkuDTO.getTenantId())){
            log.warn("未查询到货品信息 tenantId={}, skuId={}", tenantId, skuId);
            return null;
        }

        if(marketId == null) {
            MarketSpuInput marketSpuInput = new MarketSpuInput();
            marketSpuInput.setCategoryId(productSkuDTO.getCategoryId());
            marketSpuInput.setTitle(productSkuDTO.getTitle());
            marketSpuInput.setSubTitle(productSkuDTO.getSubTitle());
            marketSpuInput.setMainPicture(productSkuDTO.getMainPicture());
            marketSpuInput.setClassificationId(classificationId);
            MarketAddResDTO addRes = marketService.add(marketSpuInput, tenantId);
            if (Objects.isNull(addRes)) {
                throw new BizException("商品新增失败！");
            }
            marketId = addRes.getMarketId();
        }

        MarketItemInput marketItemInput = new MarketItemInput();
        marketItemInput.setMarketId(marketId);
        marketItemInput.setGoodsType(com.cofso.item.client.enums.GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        marketItemInput.setPriceType(StorePriceTypeEnum.ALL.getCode());
        marketItemInput.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
        marketItemInput.setItemSaleMode(0);
        marketItemInput.setSkuId(skuId);
        marketItemInput.setAfterSaleUnit(productSkuDTO.getSpecificationUnit());
        marketItemInput.setMaxAfterSaleAmount(NumberConstants.ONE);
        marketItemInput.setMiniOrderQuantity(NumberConstants.ONE);

        MarketAreaItemMappingInput areaItemMappingInput = new MarketAreaItemMappingInput();
        areaItemMappingInput.setStorePriceType(PriceTargetTypeEnum.TENANT.getCode());
        areaItemMappingInput.setType(PriceStrategyTypeEnum.ASSIGN.getCode());
        areaItemMappingInput.setMappingNumber(new BigDecimal("99999"));
        areaItemMappingInput.setPriceType(PriceStrategyConstants.PRICE_TYPE_DEFAULT);
        marketItemInput.setDefaultPrice(areaItemMappingInput);

        Long itemId = marketItemService.save(marketItemInput, tenantId);

        ItemIdAndSkuIdVO itemIdAndSkuIdVO = new ItemIdAndSkuIdVO();
        itemIdAndSkuIdVO.setMarketId(marketId);
        itemIdAndSkuIdVO.setItemId(itemId);
        itemIdAndSkuIdVO.setSkuId(skuId);
        itemIdAndSkuIdVO.setSpuId(productSkuDTO.getSpuId());

        return itemIdAndSkuIdVO;
    }

    @Override
    public PageInfo<WarehousingRecordVO> warehousingRecord(WarehousingRecordInput warehousingRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.nonNull(warehousingRecordInput.getWarehousingType()) && Objects.nonNull(warehousingRecordInput.getOutOfStockType())) {
            throw new BizException("不能同时选择出库类型和入库类型");
        }

        StockChangeRecordQueryReq input = WarehouseRecordConvert.convertToStockChangeRecordQueryReq(warehousingRecordInput);
        // 查询代仓商品记录
        ProductQueryInput productQueryInput = new ProductQueryInput();
        productQueryInput.setTitle (warehousingRecordInput.getTitle ());
        productQueryInput.setSkuId (warehousingRecordInput.getSkuId ());
        List<ProductSkuDTO> skuDTOS = productFacade.listSku (productQueryInput, loginContextInfoDTO.getTenantId ());
        if (!CollectionUtils.isEmpty(skuDTOS)) {
            List<Long> skuIds =  skuDTOS.stream ().filter (e->ObjectUtil.isNotNull (e.getSkuMapping ())).map (ProductSkuDTO::getSkuMapping).collect(Collectors.toList()).stream().filter (e->ObjectUtil.isNotNull (e.getAgentSkuId ())).map(ProductsMappingResp::getAgentSkuId).collect(Collectors.toList());
            input.setTenantSkuIdList(skuIds);
        }
        PageInfo pageInfo = saasInventoryFacade.pageQueryStockChangeRecord(input);
        List<StockChangeRecordResp> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        // TODO 根据 warehouseTenantId 判断仓库服务商
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        List<Long> agentSkuIds = list.stream().map(StockChangeRecordResp::getSkuId).collect(Collectors.toList());
        List<ProductsMappingResp> productAgentSkuMappingList = productFacade.listProductMappingByAgentSkuIdsAndTenantId (agentSkuIds, loginContextInfoDTO.getTenantId());
        Map<Long, ProductsMappingResp> productAgentSkuMappingMap = productAgentSkuMappingList.stream().collect(Collectors.toMap(ProductsMappingResp::getAgentSkuId, item -> item));
        // 代仓货品skuId
        List<Long> skuIds = productAgentSkuMappingList.stream().map(ProductsMappingResp::getSkuId).collect(Collectors.toList());
        // 查询代仓货品
        List<ProductSkuDTO> productSkuDTOS = productFacade.listSkuByIds (skuIds, loginContextInfoDTO.getTenantId());
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuDTOS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));
        List<WarehousingRecordVO> warehousingRecordVOS = list.stream().map(item -> {
            ProductsMappingResp productAgentSkuMapping = productAgentSkuMappingMap.get(item.getSkuId());
            ProductSkuDTO productSkuDTO = null;
            if(ObjectUtil.isEmpty (productAgentSkuMapping)){
                log.error ("productAgentSkuMappingMap.get鲜沐skuid异常，xmskuid={}",item.getSkuId());
            }else {
                productSkuDTO = productSkuDTOMap.get (productAgentSkuMapping.getSkuId ());
            }
            WarehousingRecordVO warehousingRecordVO = WarehouseRecordConvert.convertToWarehousingRecordVO (item, productSkuDTO);
            if (loginContextInfoDTO.getTenantId ().equals (item.getWarehouseTenantId ())) {
                warehousingRecordVO.setWarehouseProvider (tenantDTO.getCompanyName ());
            } else {
                warehousingRecordVO.setWarehouseProvider ("杭州鲜沐科技有限公司");
            }

            return warehousingRecordVO;
        }).collect(Collectors.toList());
        pageInfo.setList(warehousingRecordVOS);
        return pageInfo;
    }

    @Override
    public PageInfo<DeliveryPlanRecordVO> pageDeliveryPlanRecord(DeliveryPlanRecordInput deliveryPlanRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        DistributionRecordReq distributionRecordReq = DistributionRecordConvert.convertToDistributionRecordQueryInput(deliveryPlanRecordInput);
        distributionRecordReq.setTenantId(loginContextInfoDTO.getTenantId());
        PageInfo pageInfo = distributionRecordFacade.pageDistributionRecord(distributionRecordReq);
        List<DistributionRecordResp> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            // 订单
            List<String> orderNos = list.stream().filter(item -> item.getBusinessNo().startsWith(Global.NORMAL_ORDER_CODE)).map(DistributionRecordResp::getBusinessNo).collect(Collectors.toList());
            log.error("订单信息：{}", orderNos);
            Map<String, OrderDTO> orderDTOMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(orderNos)) {
                List<OrderDTO> orderDTOS = orderBusinessService.queryByOrderNos(orderNos, loginContextInfoDTO.getTenantId());
                log.error("订单信息：{}, {}", orderNos, JSONObject.toJSONString(orderDTOS));
                orderDTOMap = orderDTOS.stream().collect(Collectors.toMap(OrderDTO::getOrderNo, item -> item));
            }

            Map<String, OrderAfterSaleResp> orderAfterSaleDTOMap = new HashMap<>();
            // 售后单
            List<String> orderAfterSaleNos = list.stream().filter(item -> item.getBusinessNo().startsWith(Global.NORMAL_ORDER_AFTER_SALE_CODE)).map(DistributionRecordResp::getBusinessNo)
                .collect(Collectors.toList());
            List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(orderAfterSaleNos);
            log.error("售后订单信息：{}, {}", orderNos, JSONObject.toJSONString(orderAfterSaleResps));
            orderAfterSaleDTOMap = orderAfterSaleResps.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, item -> item));


            Map<String, OrderDTO> finalOrderDTOMap = orderDTOMap;
            Map<String, OrderAfterSaleResp> finalOrderAfterSaleDTOMap = orderAfterSaleDTOMap;
            List<DeliveryPlanRecordVO> deliveryPlanRecordVOS = list.stream().map(item -> {
                DeliveryPlanRecordVO deliveryPlanRecordVO = DistributionRecordConvert.convertToDeliveryPlanRecordVO(item);
                if (item.getBusinessNo().startsWith(Global.NORMAL_ORDER_CODE)) {
                    OrderDTO orderDTO = finalOrderDTOMap.get(item.getBusinessNo());
                    if (Objects.nonNull(orderDTO)) {
                        deliveryPlanRecordVO.setTotalPrice(NumberUtil.sub(orderDTO.getTotalPrice(), orderDTO.getDeliveryFee()));
                        deliveryPlanRecordVO.setDeliveryFee(orderDTO.getDeliveryFee());
                        deliveryPlanRecordVO.setPayTime(orderDTO.getPayTime());
                        deliveryPlanRecordVO.setFinishedTime(orderDTO.getFinishedTime());
                        deliveryPlanRecordVO.setDeliveryTime(orderDTO.getDeliveryTime());
                        deliveryPlanRecordVO.setOrderId(orderDTO.getId());
                        // 订单
                        deliveryPlanRecordVO.setBusinessType(NumberConstants.ONE);
                    }
                } else if (item.getBusinessNo().startsWith(Global.NORMAL_ORDER_AFTER_SALE_CODE)) {
                    OrderAfterSaleResp orderAfterSaleDTO = finalOrderAfterSaleDTOMap.get(item.getBusinessNo());
                    if (Objects.nonNull(orderAfterSaleDTO)) {
                        deliveryPlanRecordVO.setTotalPrice(orderAfterSaleDTO.getTotalPrice());
                        deliveryPlanRecordVO.setDeliveryFee(Objects.isNull(orderAfterSaleDTO.getDeliveryFee()) ? BigDecimal.ZERO : orderAfterSaleDTO.getDeliveryFee());
                        deliveryPlanRecordVO.setDeliveryTime(orderAfterSaleDTO.getRecycleTime());
                        deliveryPlanRecordVO.setFinishedTime(orderAfterSaleDTO.getFinishedTime());
                        deliveryPlanRecordVO.setOrderAfterSaleId(orderAfterSaleDTO.getId());
                        deliveryPlanRecordVO.setOrderId(orderAfterSaleDTO.getOrderId());
                        // 售后单
                        deliveryPlanRecordVO.setBusinessType(NumberConstants.TWO);
                    }
                }

                return deliveryPlanRecordVO;
            }).collect(Collectors.toList());
            pageInfo.setList(deliveryPlanRecordVOS);
        }

        return pageInfo;
    }

    @Override
    public void exportWarehousingRecord(WarehousingRecordInput warehousingRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.nonNull(warehousingRecordInput.getWarehousingType()) && Objects.nonNull(warehousingRecordInput.getOutOfStockType())) {
            throw new BizException("不能同时选择出库类型和入库类型");
        }

        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FOUR);
        if (Objects.nonNull(warehousingRecordInput.getWarehouseNo())) {
            WarehouseStorageResp warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(warehousingRecordInput.getWarehouseNo().intValue());
            if (Objects.nonNull(warehouseStorageResp)) {
                queryParamsMap.put(WarehousingRecordConstants.WAREHOUSE_NAME, warehouseStorageResp.getWarehouseName());
            }
        }

        if (Objects.nonNull(warehousingRecordInput.getOutOfStockType())) {
            queryParamsMap.put(WarehousingRecordConstants.OUT_OF_STOCK_TYPE, OutOfStockTypeEnum.getNameById(warehousingRecordInput.getOutOfStockType()));
        }

        if (Objects.nonNull(warehousingRecordInput.getWarehousingType())) {
            queryParamsMap.put(WarehousingRecordConstants.IN_STORE_TYPE, InStockTypeEnum.getNameById(warehousingRecordInput.getWarehousingType()));
        }

        if (!StringUtils.isEmpty(warehousingRecordInput.getTitle())) {
            queryParamsMap.put(WarehousingRecordConstants.GOODS_NAME, warehousingRecordInput.getTitle());
        }

        if (Objects.nonNull(warehousingRecordInput.getSkuId())) {
            queryParamsMap.put(WarehousingRecordConstants.SKU_ID, warehousingRecordInput.getSkuId());
        }

        if (Objects.nonNull(warehousingRecordInput.getStartTime()) && Objects.nonNull(warehousingRecordInput.getEndTime())) {
            queryParamsMap.put(WarehousingRecordConstants.DATE,
                TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(warehousingRecordInput.getStartTime()), TimeUtils.FORMAT) + "-" + TimeUtils
                    .changeDate2String(TimeUtils.localDateTimeConvertDate(warehousingRecordInput.getEndTime()), TimeUtils.FORMAT));
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.WAREHOUSE_RECORD_EXPORT.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.WAREHOUSE_RECORD_EXPORT.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(warehousingRecordInput, ee -> {
            // 1、表格处理
            String filePath = generateWarehousingRecordFile(warehousingRecordInput, loginContextInfoDTO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });

//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(loginContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为应付
//        record.setType(FileDownloadTypeEnum.WAREHOUSE_RECORD_EXPORT.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            generateWarehousingRecordFile(warehousingRecordInput, loginContextInfoDTO, record.getId());
//        });
    }

    /**
     * 生成出入库记录
     *
     * @param warehousingRecordInput
     * @param loginContextInfoDTO
     */
    private String generateWarehousingRecordFile(WarehousingRecordInput warehousingRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        warehousingRecordInput.setPageIndex(1);
        warehousingRecordInput.setPageSize(100);
        try {
            InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.WAREHOUSE_RECORD_EXPORT.getName());
            String filePath = ExcelUtils.tempExcelFilePath();
            ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(templateFileInputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            generateWarehousingRecordListLoop(warehousingRecordInput, loginContextInfoDTO, excelWriter, writeSheet);
//            List<WarehousingRecordVO> list = generateWarehousingRecordList(warehousingRecordInput, loginContextInfoDTO, Boolean.TRUE);

            // 数据写入excel
//            String filePath = commonService.exportExcel(list, ExcelTypeEnum.WAREHOUSE_RECORD_EXPORT.getName());

            excelWriter.finish();

            return filePath;

//            //上传文件到七牛云
//            String qiNiuFilePath = null;
//            try {
//                qiNiuFilePath = QiNiuUtils.uploadFile(filePath, ExcelTypeEnum.WAREHOUSE_RECORD_EXPORT.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH:mm:ss") + ".xlsx");
//            } catch (Exception e) {
//                log.error("上传文件至七牛云失败");
//                throw new DefaultServiceException("上传文件至七牛云失败");
//            }
//
//            // 删除临时文件
//            commonService.deleteFile(filePath);
//
//            //更新文件下载记录
//            FileDownloadRecord update = new FileDownloadRecord();
//            update.setId(recordId);
//            if (StringUtils.isBlank(qiNiuFilePath)) {
//                update.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//            } else {
//                update.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
//                update.setUrl(qiNiuFilePath);
//            }
//            fileDownloadRecordService.updateSelectiveByPrimaryKey(update);
        } catch (Exception e) {
            log.error("导出出入库记录失败，失败原因", e);
//            FileDownloadRecord update = new FileDownloadRecord();
//            update.setId(recordId);
//            update.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//            fileDownloadRecordService.updateSelectiveByPrimaryKey(update);
        }
        return null;
    }

    public void generateWarehousingRecordListLoop(WarehousingRecordInput warehousingRecordInput, LoginContextInfoDTO loginContextInfoDTO, ExcelWriter excelWriter, WriteSheet writeSheet) {
        boolean hasNextPage = true;
        while (hasNextPage) {
            PageInfo<WarehousingRecordVO> pageInfo = warehousingRecord (warehousingRecordInput, loginContextInfoDTO);
            List<WarehousingRecordVO> warehousingRecordVOS = pageInfo.getList();
            excelWriter.fill(warehousingRecordVOS, writeSheet);
            if (pageInfo.getTotal() > warehousingRecordInput.getPageIndex() * (warehousingRecordInput.getPageSize())) {
                warehousingRecordInput.setPageIndex(warehousingRecordInput.getPageIndex() + 1);
            } else {
                break;
            }
        }
    }

    @Override
    public void exportDeliveryPlanRecord(DeliveryPlanRecordInput deliveryPlanRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FOUR);
        if (Objects.nonNull(deliveryPlanRecordInput.getWarehouseNo())) {
            WarehouseStorageResp warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(deliveryPlanRecordInput.getWarehouseNo().intValue());
            if (Objects.nonNull(warehouseStorageResp)) {
                queryParamsMap.put(WarehousingRecordConstants.WAREHOUSE_NAME, warehouseStorageResp.getWarehouseName());
            }
        }

        if (!StringUtils.isEmpty(deliveryPlanRecordInput.getCityName())) {
            queryParamsMap.put(DistributionRecordConstants.CITY, deliveryPlanRecordInput.getCityName());
        }

        if (!StringUtils.isEmpty(deliveryPlanRecordInput.getBusinessNo())) {
            queryParamsMap.put(DistributionRecordConstants.BUSINESS_NO, deliveryPlanRecordInput.getBusinessNo());
        }

        if (!StringUtils.isEmpty(deliveryPlanRecordInput.getStoreName())) {
            queryParamsMap.put(DistributionRecordConstants.STORE_NAME, deliveryPlanRecordInput.getStoreName());
        }

        if (Objects.nonNull(deliveryPlanRecordInput.getStoreType())) {
            queryParamsMap.put(DistributionRecordConstants.STORE_TYPE, StoreTypeEnum.getDesc(deliveryPlanRecordInput.getStoreType()));
        }

        if (Objects.nonNull(deliveryPlanRecordInput.getStartTime()) && Objects.nonNull(deliveryPlanRecordInput.getEndTime())) {
            queryParamsMap.put(DistributionRecordConstants.DATE,
                TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(deliveryPlanRecordInput.getStartTime()), TimeUtils.FORMAT) + "-" + TimeUtils
                    .changeDate2String(TimeUtils.localDateTimeConvertDate(deliveryPlanRecordInput.getEndTime()), TimeUtils.FORMAT));
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.DISTRIBUTION_RECORD_EXPORT.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.DISTRIBUTION_RECORD_EXPORT.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(deliveryPlanRecordInput, ee -> {
            // 1、表格处理
            String filePath = generateDeliveryPlanRecordFile(deliveryPlanRecordInput, loginContextInfoDTO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });

//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(loginContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为应付
//        record.setType(FileDownloadTypeEnum.DISTRIBUTION_RECORD_EXPORT.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            generateDeliveryPlanRecordFile(deliveryPlanRecordInput, loginContextInfoDTO, record.getId());
//        });
    }


    /**
     * 生成配送记录
     *
     * @param deliveryPlanRecordInput
     * @param loginContextInfoDTO
     */
    private String generateDeliveryPlanRecordFile(DeliveryPlanRecordInput deliveryPlanRecordInput, LoginContextInfoDTO loginContextInfoDTO) {
        deliveryPlanRecordInput.setPageIndex(1);
        deliveryPlanRecordInput.setPageSize(100);
        try {
            InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.DISTRIBUTION_RECORD_EXPORT.getName());
            String filePath = ExcelUtils.tempExcelFilePath();
            ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(templateFileInputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            generateDeliveryPlanRecordListLoop(deliveryPlanRecordInput, loginContextInfoDTO, excelWriter, writeSheet);
//            List<DeliveryPlanRecordVO> list = generateWarehousingRecordList(deliveryPlanRecordInput, loginContextInfoDTO, Boolean.TRUE);

            // 数据写入excel
//            String filePath = commonService.exportExcel(list, ExcelTypeEnum.DISTRIBUTION_RECORD_EXPORT.getName());

            excelWriter.finish();

            return filePath;

//            //上传文件到七牛云
//            String qiNiuFilePath = QiNiuUtils.uploadFile(filePath, ExcelTypeEnum.DISTRIBUTION_RECORD_EXPORT.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH:mm:ss") + ".xlsx");
//
//            // 删除临时文件
//            commonService.deleteFile(filePath);
//
//            //更新文件下载记录
//            FileDownloadRecord update = new FileDownloadRecord();
//            update.setId(recordId);
//            if (StringUtils.isBlank(qiNiuFilePath)) {
//                update.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//            } else {
//                update.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
//                update.setUrl(qiNiuFilePath);
//            }
//            fileDownloadRecordService.updateSelectiveByPrimaryKey(update);
        } catch (Exception e) {
            log.error("导出配送记录失败，失败原因", e);
//            FileDownloadRecord update = new FileDownloadRecord();
//            update.setId(recordId);
//            update.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//            fileDownloadRecordService.updateSelectiveByPrimaryKey(update);
        }
        return null;
    }

//    public List<DeliveryPlanRecordVO> generateWarehousingRecordList(DeliveryPlanRecordInput deliveryPlanRecordInput, LoginContextInfoDTO loginContextInfoDTO, Boolean hasNextPage) {
//        List<DeliveryPlanRecordVO> list = new ArrayList<>();
//        if (hasNextPage) {
//            PageInfo<DeliveryPlanRecordVO> pageInfo = pageDeliveryPlanRecord(deliveryPlanRecordInput, loginContextInfoDTO);
//            List<DeliveryPlanRecordVO> deliveryPlanRecordVos = pageInfo.getList();
//            list.addAll(deliveryPlanRecordVos);
//            if (pageInfo.getTotal() > deliveryPlanRecordInput.getPageIndex() * (deliveryPlanRecordInput.getPageSize())) {
//                deliveryPlanRecordInput.setPageIndex(deliveryPlanRecordInput.getPageIndex() + 1);
//                List warehousingRecordList = generateWarehousingRecordList(deliveryPlanRecordInput, loginContextInfoDTO, pageInfo.isHasNextPage());
//                list.addAll(warehousingRecordList);
//            }
//        }
//
//        return list;
//    }

    public void generateDeliveryPlanRecordListLoop(DeliveryPlanRecordInput deliveryPlanRecordInput, LoginContextInfoDTO loginContextInfoDTO, ExcelWriter excelWriter, WriteSheet writeSheet) {
        boolean hasNextPage = true;
        while (hasNextPage) {
            PageInfo<DeliveryPlanRecordVO> pageInfo = pageDeliveryPlanRecord(deliveryPlanRecordInput, loginContextInfoDTO);
            List<DeliveryPlanRecordVO> deliveryPlanRecordVos = pageInfo.getList();
            excelWriter.fill(deliveryPlanRecordVos, writeSheet);
            if (pageInfo.getTotal() > deliveryPlanRecordInput.getPageIndex() * (deliveryPlanRecordInput.getPageSize())) {
                deliveryPlanRecordInput.setPageIndex(deliveryPlanRecordInput.getPageIndex() + 1);
            } else {
                break;
            }
        }
    }

    @Override
    public ExcelImportResDTO importAgentProduct(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) throws IOException {
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 读excel
        List<ProductSkuExcelDataInput> list = null;
        try{
           list = ExcelUtils.read(file.getInputStream(), ProductSkuExcelDataInput.class);
        }catch (Exception e){
            log.error ("importAgentProduct,读取失败，tenantid={}",tenantId,e);
            throw new DefaultServiceException("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入货品数据不能为空");
        }

        if (list.size() > NumberConstants.ONE_THOUSAND) {
            throw new DefaultServiceException("单次导入货品上限为1000个");
        }

        Long adminId = Optional.ofNullable(tenantService.queryTenantById(tenantId))
                .map(TenantDTO::getAdminId)
                .orElse(null);

        // 保存导入内容
        List<ProductSkuExcelDataInput> errorList = doSaveSpuSku(tenantId, adminId, loginContextInfoDTO.getAuthUserId(),list);

        // 异常数据写入excel
        String qiNiuFilePath = null;
        if (!CollectionUtils.isEmpty(errorList)) {
            // 从第几号开始合并
            String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.IMPORT_AGENT_PRODUCT.getName());
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "导入货品错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
            commonService.deleteFile(filePath);
        }

        // 返回导入结果
        ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
        excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
        excelImportResDTO.setSuccessRow(list.size() - excelImportResDTO.getFailRow());
        excelImportResDTO.setErrorFilePath(qiNiuFilePath);
        return excelImportResDTO;
    }

    /**
     * 保存导入内容
     * @param tenantId
     * @param adminId
     * @param list
     * @return
     */
    private List<ProductSkuExcelDataInput>  doSaveSpuSku(Long tenantId, Long adminId,Long authUserId,List<ProductSkuExcelDataInput> list) {
        List<ProductSkuExcelDataInput> errorList = new ArrayList<>();
        Map<Long,String> saveSpuContentMap = new HashMap<>();
        Map<Long,String> saveSkuContentMap = new HashMap<>();
        Map<Long,String> updateSpuContentMap = new HashMap<>();
        Map<Long,String> agentSkuContentMap = new HashMap<>();

        try{
            for (ProductSkuExcelDataInput productSkuExcelDataInput : list) {
                checkSpuImportData(productSkuExcelDataInput);
                if (!StringUtils.isBlank(productSkuExcelDataInput.getErrorMessage())) {
                    errorList.add(productSkuExcelDataInput);
                    continue;
                }

                // sku属性校验
                checkSkuImportData(productSkuExcelDataInput, tenantId);

                if (Objects.nonNull(productSkuExcelDataInput.getErrorMessage())) {
                    errorList.add(productSkuExcelDataInput);
                    continue;
                }

                List<ProductSpu> productSpus = productSpuRepository.getByTitleAndTenantId(tenantId, productSkuExcelDataInput.getTitle());
                Long spuId = null;
                ProductSpu productSpu = null;
                if (CollectionUtils.isEmpty(productSpus)) {
                    //保存spu
                    productSpu = ProductConverter.convertToProductSpu(productSkuExcelDataInput, tenantId);
                    productSpuRepository.save(productSpu);
                    spuId = productSpu.getId();

                    // 保存日志对象
                    Map<String, Object> content = new HashMap<>();
                    content.put("spuTitle", productSpu.getTitle());
                    saveSpuContentMap.put(spuId, JSON.toJSONString(content));
                } else {
                    productSpu = productSpus.get(NumberConstant.ZERO);
                    ProductSpu oriSpu = ProductConverter.copySpu(productSpu);
                    // 更新
                    assert productSpu != null;
                    productSpu.setStorageLocation(productSkuExcelDataInput.getStorageLocation());
                    productSpu.setStorageTemperature(productSkuExcelDataInput.getStorageTemperature());
                    productSpu.setGuaranteePeriod(Integer.valueOf(productSkuExcelDataInput.getGuaranteePeriod()));
                    productSpu.setGuaranteeUnit(productSkuExcelDataInput.getGuaranteeUnit());
                    productSpu.setOrigin(productSkuExcelDataInput.getOrigin());
                    productSpu.setBrandName(productSkuExcelDataInput.getBrandName());
                    productSpuRepository.updateById(productSpu);
                    spuId = productSpu.getId();

                    // 保存日志对象
                    Map<String, Object> content = new HashMap<>();
                    content.put("newSpu", productSpu);
                    content.put("oriSpu", oriSpu);
                    updateSpuContentMap.put(spuId, JSON.toJSONString(content));
                }

                ProductApplicationTypeEnum productApplicationTypeEnum = ProductApplicationTypeEnum.APPLICATION;

                ProductSku productSku = ProductConverter.convertToProductSku(productSkuExcelDataInput, tenantId);
                productSku.setSpuId(spuId);
                if (adminId != null) {
                    productSku.setOwnerId(adminId.intValue());
                }
                List<ProductSku> productSkus = Arrays.asList(productSku);
                productSkuRepository.saveBatch(productSkus);

                // 保存日志对象
                Long skuId = productSkus.get(0).getId();
                Map<String, Object> content = new HashMap<>();
                content.put("skuId", skuId);
                saveSkuContentMap.put(skuId, JSON.toJSONString(content));

                // 是否申请代仓服务
                AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByValue(productSkuExcelDataInput.getAgentApplication());
                if (AgentTypeEnum.SUMMERFARM_AGENT.equals(agentTypeEnum)) {
                    ProductAgentApplicationRecord productAgentApplicationRecord = new ProductAgentApplicationRecord();
                    productAgentApplicationRecord.setSkuId(skuId);
                    productAgentApplicationRecord.setTenantId(tenantId);
                    productAgentApplicationRecord.setAgentTenantId(XianmuSupplyTenant.TENANT_ID);
                    productAgentApplicationRecord.setStatus(ProductAgentItemStatusEnum.PROCESSING.getStatus());
                    productAgentApplicationRecordDao.save(productAgentApplicationRecord);

                    // 保存日志对象
                    // 记录货品快照
                    ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(tenantId, skuId);
                    agentSkuContentMap.put(skuId, JSON.toJSONString(ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO)));
                }

                asynchronousNotifySupplier(productSpu, productSkus, productApplicationTypeEnum);
            }
        }finally {
            // 原代码中，保存操作没加入事务。保证已执行save update的数据会被加入日志
            skuAgentLogService.saveImportSkuBizLog(tenantId, authUserId, saveSpuContentMap, saveSkuContentMap, updateSpuContentMap, agentSkuContentMap);
        }


        return errorList;
    }

    private void checkSpuImportData(ProductSkuExcelDataInput excelDataInput) {
        if (StringUtils.isBlank(excelDataInput.getTitle())) {
            excelDataInput.setErrorMessage("商品主标题不能为空");
            return;
        }

        if (excelDataInput.getTitle().length() > NumberConstants.THIRTY_TWO) {
            excelDataInput.setErrorMessage("商品主标题长度不能超过32个字");
            return;
        }

        if (StringUtils.isBlank(excelDataInput.getFirstCategoryName()) || StringUtils.isBlank(excelDataInput.getSecondCategoryName()) || StringUtils.isBlank(excelDataInput.getThirdCategoryName())) {
            excelDataInput.setErrorMessage("类目信息不能为空");
            return;
        }
        if (!checkSpuCategoryNew (excelDataInput)) {
            return;
        }
        if (StringUtils.isEmpty(excelDataInput.getStorageLocationDesc())) {
            excelDataInput.setErrorMessage("储存区域不能为空");
            return;
        }

        Integer type = ProductSkuEnum.STORAGE_LOCATION.getType(excelDataInput.getStorageLocationDesc());
        if (Objects.isNull(type)) {
            excelDataInput.setErrorMessage("储存区域格式有误");
            return;
        }
        excelDataInput.setStorageLocation(type);
        excelDataInput.setStorageTemperature(ProductSkuEnum.STORAGE_LOCATION.getStorageTemperature(type));

        // 保质期时长必须为正整数
        if (!StringUtils.isEmpty(excelDataInput.getGuaranteePeriod())) {
            if (!NumberUtil.isInteger(excelDataInput.getGuaranteePeriod())) {
                excelDataInput.setErrorMessage("保质期时长必须为正整数");
                return;
            }
            if (Integer.parseInt(excelDataInput.getGuaranteePeriod()) <= 0) {
                excelDataInput.setErrorMessage("保质期时长必须为正整数");
                return;
            }
        }

        if (!StringUtils.isEmpty(excelDataInput.getGuaranteeUnitDesc())) {
            Integer guaranteeUnit = ProductSkuEnum.GUARANTEE_UNIT.getType(excelDataInput.getGuaranteeUnitDesc());
            if (Objects.isNull(guaranteeUnit)) {
                excelDataInput.setErrorMessage("保质期单位格式错误");
                return;
            }
            excelDataInput.setGuaranteeUnit(guaranteeUnit);
        }

        ProductSkuEnum.GUARANTEE_PERIOD_TYPE periodTypeEnum = ProductSkuEnum.GUARANTEE_PERIOD_TYPE.getType(excelDataInput.getGuaranteePeriodType());
        if (Objects.isNull(periodTypeEnum)) {
            excelDataInput.setErrorMessage("保质期类型格式错误");
            return;
        }
        if (ProductSkuEnum.GUARANTEE_PERIOD_TYPE.PERIODICAL.getDesc().equals(excelDataInput.getGuaranteePeriodType())) {
            if (StringUtils.isEmpty(excelDataInput.getGuaranteePeriod())) {
                excelDataInput.setErrorMessage("保质期时长不能为空");
                return;
            }

            if (StringUtils.isEmpty(excelDataInput.getGuaranteeUnitDesc())) {
                excelDataInput.setErrorMessage("保质期单位不能为空");
                return;
            }
        }else {
            //长期默认为100年
            excelDataInput.setGuaranteePeriod("100");
            excelDataInput.setGuaranteeUnit(ProductSkuEnum.GUARANTEE_UNIT.YEAR.getType());
        }



    }

    public boolean checkSpuCategoryNew(ProductSkuExcelDataInput excelDataDTO) {
        List<CategoryVO> categoryVOS = productCategoryService.selectByParentIdAndNameNew (Constants.DEFAULT_CLASSIFICATION_ID, excelDataDTO.getFirstCategoryName ());
        if (CollectionUtil.isEmpty (categoryVOS)) {
            excelDataDTO.setErrorMessage("未查询到该一级类目");
            return Boolean.FALSE;
        }
        for(CategoryVO first : categoryVOS){
            Optional<CategoryVO> second = first.getCategoryVOS ().stream ().filter (e1 -> e1.getName ().equals (excelDataDTO.getSecondCategoryName ())).findFirst ();
            if(second.isPresent ()){
                Optional<CategoryVO> third = second.get ().getCategoryVOS ().stream ().filter (e1 -> e1.getName ().equals (excelDataDTO.getThirdCategoryName ())).findFirst ();
                if(third.isPresent ()){
                    excelDataDTO.setCategoryId(third.get ().getId());
                    excelDataDTO.setErrorMessage(null);
                    return Boolean.TRUE;
                }else{
                    excelDataDTO.setErrorMessage("该二级类目下没有此三级类目");
                }
            }else{
                excelDataDTO.setErrorMessage("该一级类目下没有此二级类目");
            }
        }
        return Boolean.FALSE;
    }

//    private Boolean checkSpuCategory(ProductSkuExcelDataInput excelDataDTO) {
//        ProductCategory firstCategory = productCategoryService.selectByParentIdAndName(Constants.DEFAULT_CLASSIFICATION_ID, excelDataDTO.getFirstCategoryName());
//        if (Objects.isNull(firstCategory)) {
//            excelDataDTO.setErrorMessage("未查询到该一级类目");
//            return Boolean.FALSE;
//        }
//
//        ProductCategory secondCategory = productCategoryService.selectByParentIdAndName(firstCategory.getId(), excelDataDTO.getSecondCategoryName());
//        if (Objects.isNull(secondCategory)) {
//            excelDataDTO.setErrorMessage("该一级类目下没有此二级类目");
//            return Boolean.FALSE;
//        }
//
//        ProductCategory thirdCategory = productCategoryService.selectByParentIdAndName(secondCategory.getId(), excelDataDTO.getThirdCategoryName());
//        if (Objects.isNull(thirdCategory)) {
//            excelDataDTO.setErrorMessage("该二级类目下没有此三级类目");
//            return Boolean.FALSE;
//        }
//        excelDataDTO.setCategoryId(thirdCategory.getId());
//        return Boolean.TRUE;
//    }

    private void checkSkuImportData(ProductSkuExcelDataInput productSkuExcelDataInput, Long tenantId) {
        if (StringUtils.isEmpty(productSkuExcelDataInput.getSpecificationTypeDesc())) {
            productSkuExcelDataInput.setErrorMessage("规格类型不能为空");
            return;
        }

        Integer specificationType = SpecificationTypeEnums.getType(productSkuExcelDataInput.getSpecificationTypeDesc());
        if (Objects.isNull(specificationType)) {
            productSkuExcelDataInput.setErrorMessage("规格单位格式错误");
            return;
        }
        productSkuExcelDataInput.setSpecificationType(specificationType);

        if (StringUtils.isEmpty(productSkuExcelDataInput.getSpecification())) {
            productSkuExcelDataInput.setSpecification("规格不能为空");
            return;
        }

        if (SpecificationTypeEnums.CAPACITY.getType().equals(specificationType)) {
            //规格必须是 1单位*2单位 的格式
            String regex = "(\\d+(\\.\\d+)?(" + String.join("|", specificationUnitList) + "))\\*(\\d+(\\.\\d+)?(" + String.join("|", specificationUnitList) + "))";
            if (!productSkuExcelDataInput.getSpecification().matches(regex)) {
                productSkuExcelDataInput.setErrorMessage("规格名称列中格式或单位填写有误,规格格式为:数量单位*数量单位,如：200mL*3箱");
                return;
            }
        }else if (SpecificationTypeEnums.INTERVAL.getType().equals(specificationType)){
            //规格必须是 毛重/净重数量-数量单位 的格式
            String regex = "(毛重|净重)(\\d+(\\.\\d+)?)-(\\d+(\\.\\d+)?(" + String.join("|", specificationUnitList) + "))";
            if (!productSkuExcelDataInput.getSpecification().matches(regex)) {
                productSkuExcelDataInput.setErrorMessage("规格名称列中格式或单位填写有误,规格格式为:毛重/净重数量-数量单位，如：净重1-2斤");
                return;
            }
        }

        if (StringUtils.isEmpty(productSkuExcelDataInput.getSpecificationUnit())) {
            productSkuExcelDataInput.setErrorMessage("规格单位不能为空");
            return;
        }

        if (StringUtils.isEmpty(productSkuExcelDataInput.getPlaceTypeDesc())) {
            productSkuExcelDataInput.setErrorMessage("进口/国产不能为空");
            return;
        }

        Integer type = PlaceTypeEnums.getType(productSkuExcelDataInput.getPlaceTypeDesc());
        if (Objects.isNull(type)) {
            productSkuExcelDataInput.setErrorMessage("进口/国产格式错误");
            return;
        }
        productSkuExcelDataInput.setPlaceType(type);
        if (StringUtils.isEmpty(productSkuExcelDataInput.getAgentApplication())) {
            productSkuExcelDataInput.setErrorMessage("是否需要代仓不能为空");
            return;
        }
        AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByValue(productSkuExcelDataInput.getAgentApplication());
        if (agentTypeEnum == null) {
            productSkuExcelDataInput.setErrorMessage("是否需要代仓格式错误");
            return;
        }else {
            //申请了代仓长宽高不能为空
            if (AgentTypeEnum.SUMMERFARM_AGENT.equals(agentTypeEnum)) {
                if (StringUtils.isEmpty(productSkuExcelDataInput.getLength())) {
                    productSkuExcelDataInput.setErrorMessage("申请代仓时长宽高不能为空");
                    return;
                }
                if (StringUtils.isEmpty(productSkuExcelDataInput.getWidth())) {
                    productSkuExcelDataInput.setErrorMessage("申请代仓时长宽高不能为空");
                    return;
                }
                if (StringUtils.isEmpty(productSkuExcelDataInput.getHeight())) {
                    productSkuExcelDataInput.setErrorMessage("申请代仓时长宽高不能为空");
                    return;
                }
                if (StringUtils.isEmpty(productSkuExcelDataInput.getWeight())) {
                    productSkuExcelDataInput.setErrorMessage("申请代仓时重量不能为空");
                    return;
                }
            }
        }

        if (!StringUtils.isEmpty(productSkuExcelDataInput.getLength())) {
            BigDecimal lengthBigDecimal;
            if (!NumberUtil.isNumber(productSkuExcelDataInput.getLength())) {
                productSkuExcelDataInput.setErrorMessage("长必须是数字类型");
                return;
            } else {
                lengthBigDecimal = new BigDecimal(productSkuExcelDataInput.getLength());
                if (lengthBigDecimal.scale() > 1) {
                    productSkuExcelDataInput.setErrorMessage("长宽高小数位不能超过2位");
                    return;
                }
            }
        }

        if (!StringUtils.isEmpty(productSkuExcelDataInput.getWidth())) {
            BigDecimal widthBigDecimal;
            if (!NumberUtil.isNumber(productSkuExcelDataInput.getWidth())) {
                productSkuExcelDataInput.setErrorMessage("宽必须是数字类型");
                return;
            } else {
                widthBigDecimal = new BigDecimal(productSkuExcelDataInput.getWidth());
                if (widthBigDecimal.scale() > 1) {
                    productSkuExcelDataInput.setErrorMessage("长宽高小数位不能超过2位");
                    return;
                }
            }
        }

        if (!StringUtils.isEmpty(productSkuExcelDataInput.getHeight())) {
            BigDecimal heightBigDecimal;
            if (!NumberUtil.isNumber(productSkuExcelDataInput.getHeight())) {
                productSkuExcelDataInput.setErrorMessage("高必须是数字类型");
                return;
            } else {
                heightBigDecimal = new BigDecimal(productSkuExcelDataInput.getHeight());
                if (heightBigDecimal.scale() > 1) {
                    productSkuExcelDataInput.setErrorMessage("长宽高小数位不能超过2位");
                    return;
                }
            }
        }
        productSkuExcelDataInput.setVolumeUnit(VolumeUnitEnums.CUBIC_CENTIMETER.getVolumeUnit());
        if (!StringUtils.isEmpty(productSkuExcelDataInput.getWeight())) {
            BigDecimal weightBigDecimal;
            if (!NumberUtil.isNumber(productSkuExcelDataInput.getWeight())) {
                productSkuExcelDataInput.setErrorMessage("重量必须是数字类型");
                return;
            } else {
                weightBigDecimal = new BigDecimal(productSkuExcelDataInput.getWeight());
                if (weightBigDecimal.scale() > 2) {
                    productSkuExcelDataInput.setErrorMessage("重量小数位不能超过2位");
                    return;
                }
            }
        }

        String regex = Constants.REGEX_CHAR_AND_NUMBER;
        if (Objects.nonNull(productSkuExcelDataInput.getCustomSkuCode()) && !productSkuExcelDataInput.getCustomSkuCode().matches(regex)) {
            productSkuExcelDataInput.setErrorMessage("自有编码只能是数字或英文");
        }
    }

    @Override
    public void generateImportProductTemplate() {
        try {
            List<ProductCategoryTreeDTO> categoryTreeDTOList = categoryServiceFacade.listCategoryTree();
            if(CollectionUtils.isEmpty(categoryTreeDTOList)){
                return;
            }
            Map<Integer, ChainDropDown> chainDropDownMap = buildChainDropDown(categoryTreeDTOList);

            InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), "import_product_template.xlsx");
            String filePath = ExcelUtils.tempExcelFilePath();

            Workbook wb = WorkbookFactory.create(templateFileInputStream);
            Sheet sheet = wb.getSheetAt(0);

            ChainDropDownWriteHandler.generateDataValidation(wb, (XSSFSheet)sheet, chainDropDownMap);

            wb.write(new FileOutputStream(filePath));
            wb.close();

            String url = ExcelTemplateType.IMPORT_AGENT_PRODUCT.getUrl();
            String filename = ExcelTemplateType.IMPORT_AGENT_PRODUCT.getDesc() + ".xlsx";
            if(!SpringContextUtil.isPro()){
                filename = "dev" + Constants.SLASH + filename;
                url = ExcelTemplateType.IMPORT_AGENT_PRODUCT.getDevUrl();
            }

            // 同名的url， 先删除，再更新
            QiNiuUtils.deleteFile(new String[]{url});

            String qiNiuFilePath = QiNiuUtils.uploadFile(filePath, filename);
            log.info("刷新自营货品导入模板 path={}", qiNiuFilePath);

            //删除临时文件
            commonService.deleteFile(filePath);
        } catch (Exception e) {
            log.error("刷新自营货品导入模板异常: {}", e.getMessage(), e);
            throw new BizException("刷新失败");
        }

    }

    private Map<Integer, ChainDropDown> buildChainDropDown(List<ProductCategoryTreeDTO> list){
        Map<Integer, ChainDropDown> map = new HashMap<>(list.size());

        Integer firstColumnIdx = 2;
        Integer secondColumnIdx = 3;
        Integer thirdColumnIdx = 4;

        for (ProductCategoryTreeDTO productCategoryTreeDTO : list) {
            // 一级类目
            map.computeIfAbsent(firstColumnIdx,c -> {
                ChainDropDown firstChainDropDown = new ChainDropDown();
                firstChainDropDown.setRootFlag(true);
                firstChainDropDown.setTypeName("类目");
                firstChainDropDown.setRowIndex(0);
                return firstChainDropDown;
            });

            Map<String, List<String>> firstDataMap = map.get(firstColumnIdx).getDataMap();
            firstDataMap.computeIfAbsent(ChainDropDown.ROOT_KEY, k -> Lists.newArrayList());

            String firstCategoryName = productCategoryTreeDTO.getName();
            firstDataMap.get(ChainDropDown.ROOT_KEY).add(firstCategoryName);

            if(CollectionUtils.isEmpty(productCategoryTreeDTO.getChildList())){
                continue;
            }

            for (ProductCategoryTreeDTO secondDto : productCategoryTreeDTO.getChildList()) {
                // 二级类目
                map.computeIfAbsent(secondColumnIdx,c -> {
                    ChainDropDown secondChainDropDown = new ChainDropDown();
                    secondChainDropDown.setRootFlag(false);
                    secondChainDropDown.setTypeName("类目");
                    secondChainDropDown.setRowIndex(1);
                    return secondChainDropDown;
                });

                Map<String, List<String>> secondDataMap = map.get(secondColumnIdx).getDataMap();
                secondDataMap.computeIfAbsent(firstCategoryName,k -> Lists.newArrayList());

                String secondCategoryName = secondDto.getName();
                secondDataMap.get(firstCategoryName).add(secondCategoryName);

                if(CollectionUtils.isEmpty(secondDto.getChildList())){
                    continue;
                }
                for (ProductCategoryTreeDTO thirdDTO : secondDto.getChildList()) {
                    // 三级类目
                    map.computeIfAbsent(thirdColumnIdx, c -> {
                        ChainDropDown thirdChainDropDown = new ChainDropDown();
                        thirdChainDropDown.setRootFlag(false);
                        thirdChainDropDown.setTypeName("类目");
                        thirdChainDropDown.setRowIndex(1);
                        return thirdChainDropDown;
                    });

                    Map<String, List<String>> thirdDataMap = map.get(thirdColumnIdx).getDataMap();
                    thirdDataMap.computeIfAbsent(secondCategoryName,k -> Lists.newArrayList());

                    String thirdCategoryName = thirdDTO.getName();
                    thirdDataMap.get(secondCategoryName).add(thirdCategoryName);

                }
            }
        }

        ChainDropDown secIdx = map.get(secondColumnIdx);
        ChainDropDown thirdIdx = map.get(thirdColumnIdx);
        if(Objects.nonNull(secIdx) && Objects.nonNull(thirdIdx)) {
            thirdIdx.setRowIndex(1 + secIdx.getDataMap().size());
        }

        return map;
    }

    @Override
    public void updateTaxRateValue(Long tenantId,Long id, BigDecimal taxRateValue) {
        AssertCheckParams.notNull(id, ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");
        AssertCheckParams.notNull(taxRateValue, ResultDTOEnum.PARAMETER_MISSING.getCode(), "税率不能为空");
        ProductSku productSku = productSkuRepository.getByIdAndTenantId(id, tenantId);
        if (Objects.isNull(productSku)){
            throw new BizException("sku 不存在！");
        }
        ProductSku sku = new ProductSku();
        sku.setId(id);
        sku.setTaxRateValue(taxRateValue);
        productSkuRepository.updateById(sku);

        ProductSku oriSku = new ProductSku();
        oriSku.setId(id);
        oriSku.setTaxRateValue(productSku.getTaxRateValue());
        Map<String, Object> content = new HashMap<>();
        content.put("newSku", sku);
        content.put("oriSku", oriSku);
        BizLogRecordContext.put("newSku", sku);
        BizLogRecordContext.put("content", content);
    }

    @Override
    public void updateCustomSkuCode(Long tenantId,Long id, String customSkuCode) {
        AssertCheckParams.notNull(id, ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");
        AssertCheckParams.notNull(customSkuCode, ResultDTOEnum.PARAMETER_MISSING.getCode(), "自有编码不能为空");
        ProductSku productSku = productSkuRepository.getByIdAndTenantId(id, tenantId);
        if (Objects.isNull(productSku)){
            throw new BizException("sku 不存在！");
        }
        ProductSku sku = new ProductSku();
        sku.setId(id);
        sku.setCustomSkuCode(customSkuCode);
        productSkuRepository.updateById(sku);

        ProductSku oriSku = new ProductSku();
        oriSku.setId(id);
        oriSku.setCustomSkuCode(productSku.getCustomSkuCode());
        Map<String, Object> content = new HashMap<>();
        content.put("newSku", sku);
        content.put("oriSku", oriSku);
        BizLogRecordContext.put("newSku", sku);
        BizLogRecordContext.put("content", content);
    }
    @Override
    public ProductSkuDetailVO getBySkuId(Long skuId,Long tenantId) {
        ProductSkuDTO productSkuDTO = productSkuService.selectProductSkuDetailById (skuId);
        ProductSkuDetailVO productSkuDetailVO = ProductConvert.convertToProductSkuDetailVO(productSkuDTO);
        // 构建仓库信息
        buildWearhouseData(Collections.singletonList(productSkuDetailVO),tenantId);

        // 设置代仓申请状态
        productSkuDetailVO.setAgentStatus(productAgentApplicationRecordDao.getAgentApplicationRecordStatus(skuId, tenantId));

        return productSkuDetailVO;
    }

//    @Override
//    public ProductSkuDTO getBySkuCode(String skuCode,Long tenantId) {
//        return productFacade.selectProductSkuDetailBySkuCode (skuCode,tenantId);
//    }

    /**
     * 构建仓库信息
     *
     * @param productSkuDetailVOS
     */
    private void buildWearhouseData(List<ProductSkuDetailVO> productSkuDetailVOS, Long tenantId) {
        if (CollectionUtils.isEmpty(productSkuDetailVOS)) {
            return;
        }
        List<Long> skuIds = productSkuDetailVOS.stream().map(ProductSkuDetailVO::getId).collect(Collectors.toList());
        /**
         * 构建 报价货品 的仓库信息（报价单ID、供应价、覆盖城市、供应有效期）
         */
        buildQuotationSku(productSkuDetailVOS, tenantId, skuIds);

        /**
         * 构建 自营货品 的仓库信息（仓库、仓库服务商、覆盖区域、覆盖城市列表、可用库存）
         */
        buildSelfGoodsSku(productSkuDetailVOS, tenantId, skuIds);
    }


    /**
     * 构建 自营货品 的仓库信息（仓库、仓库服务商、覆盖区域、覆盖城市列表、可用库存）
     *
     * @param productSkuDetailVOS
     * @param tenantId
     * @param skuIds
     */
    private void buildSelfGoodsSku(List<ProductSkuDetailVO> productSkuDetailVOS, Long tenantId, List<Long> skuIds) {
        List<ProductAgentWarehouseDataVO> selfSkuList = productAgentSkuService.queryAgentProductWarehouseData(skuIds, tenantId);
        if (CollectionUtils.isEmpty(selfSkuList)) {
            return;
        }
        Map<Long, List<ProductAgentWarehouseDataVO>> selfSkuMap = selfSkuList.stream().collect(Collectors.groupingBy(ProductAgentWarehouseDataVO::getSkuId));
        for (ProductSkuDetailVO sku : productSkuDetailVOS) {
            List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS = selfSkuMap.get(sku.getId());
            if (CollectionUtils.isEmpty(productAgentWarehouseDataVOS)) {
                continue;
            }
            sku.setWarehouseDataVos(ProductInfoConvert.INSTANCE.convert2VOs(productAgentWarehouseDataVOS));
        }
    }

    /**
     * 构建 报价货品 的仓库信息（供应价、覆盖城市、供应有效期）
     *
     * @param productSkuDetailVOS
     * @param tenantId
     * @param skuIds
     */
    private void buildQuotationSku(List<ProductSkuDetailVO> productSkuDetailVOS, Long tenantId, List<Long> skuIds) {
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> skuMap = productPricingSupplyService.querySupplyPriceSkuIdMap (skuIds, tenantId);
        if (MapUtils.isEmpty(skuMap)) {
            return;
        }
        Map<Long, ProductSkuDetailVO> priceMap  = new HashMap<> ();
        Map<Long, List<Long>> preferentialCostPriceRangeQueryMap  = new HashMap<> ();
        skuMap.forEach ((skuId,list)->{
            ProductSkuDetailVO sku = new ProductSkuDetailVO();
            // 为避免多次循环，不使用stream.map
            List<Long> cityIds = new ArrayList<> ();
            for (ProductPricingSupplyCityMappingDTO price : list) {
                if (Objects.isNull(sku.getStartTime()) || sku.getStartTime().isAfter(price.getStartTime())) {
                    sku.setStartTime(price.getStartTime());
                }
                if (Objects.isNull(sku.getEndTime()) || sku.getEndTime().isBefore(price.getEndTime())) {
                    sku.setEndTime(price.getEndTime());
                }
                if (Objects.isNull(sku.getProductSupplyPriceId())) {
                    sku.setProductSupplyPriceId(price.getProductPricingSupplyId());
                }
                cityIds.add (price.getCityId ());
            }
            sku.setCityNum(list.size());
            BigDecimal maxPrice = list.stream().filter(e -> ObjectUtil.isNotNull (e.getMaxPrice()) && e.getMaxPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMaxPrice).max((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
            BigDecimal minPrice = list.stream().filter(e -> ObjectUtil.isNotNull (e.getMinPrice()) && e.getMinPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMinPrice).min((x1, x2) -> x1.compareTo(x2)).orElse(BigDecimal.ZERO);
            sku.setMaxPrice (maxPrice);
            sku.setMinPrice (minPrice);
            priceMap.put (skuId,sku);
            preferentialCostPriceRangeQueryMap.put (skuId,cityIds);
        });
        Map<Long, ProductSkuPreferentialCostPriceRangeResp> productSkuPreferentialCostPriceRangeMap = productPricingSupplyService.queryPreferentialCostPriceRange (tenantId,preferentialCostPriceRangeQueryMap);
        for (ProductSkuDetailVO sku : productSkuDetailVOS) {
            ProductSkuDetailVO priceSku = priceMap.get(sku.getId());
            if (Objects.isNull(priceSku)) {
                sku.setMaxPrice(BigDecimal.ZERO);
                sku.setMinPrice(BigDecimal.ZERO);
            } else {
                sku.setProductSupplyPriceId(priceSku.getProductSupplyPriceId());
                ProductSkuPreferentialCostPriceRangeResp productSkuPreferentialCostPriceRange = productSkuPreferentialCostPriceRangeMap.get (sku.getId ());
                sku.setMaxPrice(productPricingSupplyService.getMaxPrice(priceSku.getMaxPrice(),productSkuPreferentialCostPriceRange));
                sku.setMinPrice(productPricingSupplyService.getMinPrice(priceSku.getMinPrice(),productSkuPreferentialCostPriceRange));
                sku.setStartTime(priceSku.getStartTime());
                sku.setEndTime(priceSku.getEndTime());
                sku.setCityNum(priceSku.getCityNum());
            }
            sku.setPriceStr(PriceUtil.buildPriceRange(sku.getMaxPrice(),sku.getMinPrice()));
        }
    }

    @Override
    public void synchronizedProduct(List<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        // 查询货品映射
        List<ProductAgentSkuMapping> productAgentSkuMappingList = productAgentSkuMappingDao.selectBySkuIds(skuIds, tenantId);
        if (!CollectionUtils.isEmpty(productAgentSkuMappingList)) {
            // 需要移除的skuId
            List<Long> removeSkuIds = productAgentSkuMappingList.stream().map(ProductAgentSkuMapping::getSkuId).collect(Collectors.toList());
            skuIds.removeAll(removeSkuIds);
        }

        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        // 查询所有商品
        ProductSkuQueryInput skuQuery = new ProductSkuQueryInput();
        skuQuery.setSkuIds(skuIds);
        List<ProductSku> productSkus = productSkuRepository.listByCondition(skuQuery, tenantId);
        List<Long> spuIds = productSkus.stream().map(ProductSku::getSpuId).collect(Collectors.toList());
        ProductQueryInput productQueryInput = new ProductQueryInput();
        productQueryInput.setSpuIds(spuIds);
        List<ProductSpu> productSpus = productSpuRepository.listByCondition(productQueryInput, tenantId);
        Map<Long, ProductSpu> productSpuMap = productSpus.stream().collect(Collectors.toMap(ProductSpu::getId, item -> item));
        for (ProductSku productSku : productSkus) {
            ProductSpu productSpu = productSpuMap.get(productSku.getSpuId());
            asynchronousNotifySupplier(productSpu, Arrays.asList(productSku), ProductApplicationTypeEnum.APPLICATION);
        }
    }

    @Override
    public void updateSkuUseFlag(ProductSkuUseInput input, Long tenantId) {
        // 校验
        checkUnUse(input.getSkuId(), input.getUseFlag(), tenantId);
        // 更新
        ProductSku sku = new ProductSku();
        sku.setId(input.getSkuId());
        sku.setUseFlag(input.getUseFlag());
        productSkuRepository.updateById(sku);

        // 更新库存预警表的 货品停用状态
        productStockForewarningReportRepository.updateUseFlagBySkuId(tenantId, input.getSkuId(), input.getUseFlag());

        Map<String, Object> content = new HashMap<>();
        content.put("newSku", sku);
        content.put("oriSku", BizLogRecordContext.get("oriSku"));
        BizLogRecordContext.put("newSku", sku);
        BizLogRecordContext.put("content", content);
    }

    @Override
    public void checkSkuUseFlag(ProductSkuUseInput input, Long tenantId) {
        checkUnUse(input.getSkuId(), input.getUseFlag(), tenantId);
    }

    @Override
    public Integer associatedItemCount(Long skuId, Long tenantId) {
        return marketItemFacade.associatedItemCount(skuId, tenantId);
    }

    @Override
    public List<ProductSkuDTO> queryByCustomSkuCodeList(List<String> customSkuCodeList, Long tenantId) {
        return productFacade.queryByCustomSkuCodeList(customSkuCodeList,tenantId);
    }

    /**
     * 校验停用条件
     */
    private void checkUnUse(Long skuId, Integer useFlag, Long tenantId) {
        ProductSku sku = productSkuRepository.getById(skuId);
        if (Objects.isNull(sku)){
            throw new BizException("skuId:" + skuId + "不存在！");
        }
        ProductSku oriSku = new ProductSku();
        oriSku.setId(sku.getId());
        oriSku.setUseFlag(sku.getUseFlag());
        BizLogRecordContext.put("oriSku", oriSku);

        if (!sku.getTenantId().equals(tenantId)) {
            throw new BizException(ResultDTOEnum.TENANT_ERROR.getMessage());
        }
        if (sku.getUseFlag().equals(useFlag)) {
            return;
        }
        if (SkuUseFlagEnum.IN_USER.getCode().equals(useFlag)) {
            return;
        }
        /**
         * 启用条件，满足以下所有
         * ● 库存为0
         * ● 未关联销售商品
         */
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = productAgentSkuService.queryAgentProductWarehouseData(Collections.singletonList(skuId), tenantId);
        int invCount = productAgentWarehouseDataVos.stream().mapToInt(ProductAgentWarehouseDataVO::getEnabledQuantity).sum();

        if (invCount != 0) {
            throw new BizException("该sku还剩余" + invCount + "库存，无法停用");
        }

        // 是否关联商品  0-未关联 1-关联
        Integer associated = sku.getAssociated();
        if (associated == 1) {
            throw new BizException("该sku有关联的销售商品,请先解除关联。");
        }
    }
}
