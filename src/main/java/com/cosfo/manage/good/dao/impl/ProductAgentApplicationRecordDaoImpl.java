package com.cosfo.manage.good.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.manage.common.context.ProductAgentItemStatusEnum;
import com.cosfo.manage.common.context.SkuAgentStatusEnum;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationRecordQueryConditionDTO;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.good.mapper.ProductAgentApplicationRecordMapper;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 货品代仓服务申请记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class ProductAgentApplicationRecordDaoImpl extends ServiceImpl<ProductAgentApplicationRecordMapper, ProductAgentApplicationRecord> implements ProductAgentApplicationRecordDao {

    @Override
    public List<ProductAgentApplicationRecord> listByCondition(ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO) {
        LambdaQueryWrapper<ProductAgentApplicationRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductAgentApplicationRecord::getTenantId, productAgentApplicationRecordQueryConditionDTO.getTenantId());
        queryWrapper.eq(Objects.nonNull(productAgentApplicationRecordQueryConditionDTO.getAgentTenantId()) ,ProductAgentApplicationRecord::getAgentTenantId, productAgentApplicationRecordQueryConditionDTO.getAgentTenantId());
        queryWrapper.eq(Objects.nonNull(productAgentApplicationRecordQueryConditionDTO.getSkuId()), ProductAgentApplicationRecord::getSkuId, productAgentApplicationRecordQueryConditionDTO.getSkuId());
        queryWrapper.in(Objects.nonNull(productAgentApplicationRecordQueryConditionDTO.getSkuIds()), ProductAgentApplicationRecord::getSkuId, productAgentApplicationRecordQueryConditionDTO.getSkuIds());
        queryWrapper.eq(Objects.nonNull(productAgentApplicationRecordQueryConditionDTO.getStatus()), ProductAgentApplicationRecord::getStatus, productAgentApplicationRecordQueryConditionDTO.getStatus());
        queryWrapper.orderByDesc(ProductAgentApplicationRecord::getId);
        return list(queryWrapper);
    }

    @Override
    public Integer getAgentApplicationRecordStatus(Long skuId, Long tenantId) {
            Integer agentStatus = SkuAgentStatusEnum.UN_APPLY.getCode();
            if(skuId == null || tenantId == null){
                return agentStatus;
            }

            // 查询代仓申请记录
            ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
            productAgentApplicationRecordQueryConditionDTO.setTenantId(tenantId);
            productAgentApplicationRecordQueryConditionDTO.setSkuId(skuId);
            // 查询申请记录
            List<ProductAgentApplicationRecord> agentRecordList = listByCondition(productAgentApplicationRecordQueryConditionDTO);
            if (!CollectionUtils.isEmpty(agentRecordList)) {
                agentStatus = agentRecordList.get(0).getStatus();
            }
            return agentStatus;
    }

    @Override
    public void updateAuditResult(ProductAgentApplicationRecord productAgentApplicationRecord) {
        LambdaUpdateWrapper<ProductAgentApplicationRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductAgentApplicationRecord::getStatus, productAgentApplicationRecord.getStatus());
        updateWrapper.set(ProductAgentApplicationRecord::getAuditRemark, productAgentApplicationRecord.getAuditRemark());
        updateWrapper.eq(ProductAgentApplicationRecord::getTenantId, productAgentApplicationRecord.getTenantId());
        updateWrapper.eq(ProductAgentApplicationRecord::getSkuId, productAgentApplicationRecord.getSkuId());
        updateWrapper.eq(ProductAgentApplicationRecord::getAgentTenantId, productAgentApplicationRecord.getAgentTenantId());
        updateWrapper.eq(ProductAgentApplicationRecord::getStatus, ProductAgentItemStatusEnum.PROCESSING.getStatus());
        update(updateWrapper);
    }

    @Override
    public void delete(Long id) {
        getBaseMapper().deleteById(id);
    }

    @Override
    public List<Long> listAllTenantIds() {
        QueryWrapper<ProductAgentApplicationRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT tenant_id");
        return listObjs(queryWrapper, t -> (Long) t);
    }

}
