package com.cosfo.manage.good.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.good.model.dto.DeliveryPlanRecordInput;
import com.cosfo.manage.good.model.dto.DetailQueryInput;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.good.model.dto.ProductSkuAddInput;
import com.cosfo.manage.good.model.dto.ProductSkuUseInput;
import com.cosfo.manage.good.model.dto.ProductSpuAddInput;
import com.cosfo.manage.good.model.dto.SaleProductRelateInfoInput;
import com.cosfo.manage.good.model.dto.WarehousingRecordInput;
import com.cosfo.manage.good.model.vo.DeliveryPlanRecordVO;
import com.cosfo.manage.good.model.vo.ProductSpuDetailVO;
import com.cosfo.manage.good.model.vo.ProductSpuListVo;
import com.cosfo.manage.good.model.vo.WarehousingRecordVO;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.vo.ProductSkuDetailVO;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.summerfarm.model.input.SummerfarmSkuInput;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 货品模块
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@RestController
@RequestMapping("/product")
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;
    @Autowired
    private MarketService marketService;
    @Autowired
    private ProductSkuService productSkuService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    /**
     * 新增自采货品
     *
     * @param productSpuAddInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/add")
    public CommonResult add(@RequestBody @Valid ProductSpuAddInput productSpuAddInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        BizLogRecordContext.put("authUserId", contextInfoDTO.getAuthUserId());
        Long spuId = productService.addProduct(productSpuAddInput, contextInfoDTO);
        return CommonResult.ok(spuId);
    }

    /**
     * 更新自采货品
     *
     * @param productSpuAddInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/update")
    public CommonResult update(@RequestBody ProductSpuAddInput productSpuAddInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        BizLogRecordContext.put("authUserId", contextInfoDTO.getAuthUserId());
        productService.updateProduct(productSpuAddInput, contextInfoDTO.getTenantId());
        return CommonResult.ok();

    }

    /**
     * 自采货品(代仓列表)列表
     *
     * @param productQueryInput
     * @return
     */
    @PostMapping("query/list")
    public CommonResult<PageInfo<ProductSpuListVo>> list(@RequestBody ProductQueryInput productQueryInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        if (grayReleaseConfig.executeProductCenterGray(contextInfoDTO.getTenantId())) {
            PageInfo<ProductSpuListVo> page = productService.page(productQueryInput, contextInfoDTO);
            return CommonResult.ok(page);
        } else {
            PageInfo<ProductSpuListVo> page = productService.pageOld(productQueryInput, contextInfoDTO);
            return CommonResult.ok(page);
        }
    }

    /**
     * 导出自采货品(代仓列表)列表
     *
     * @param productQueryInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export-list")
    public CommonResult exportList(@RequestBody ProductQueryInput productQueryInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productService.export(productQueryInput, contextInfoDTO);
        return CommonResult.ok();
    }

    /**
     * 自营货品导入模板刷新
     *
     * @param code
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/refresh-import-template")
    public CommonResult refreshProductImportTemplate(String code) {
        if("xianmu@#$".equals(code)) {
            productService.generateImportProductTemplate();
        }
        return CommonResult.ok();
    }

    /**
     * 货品 - 详情
     *
     * @param detailQueryInput
     * @return
     */
    @PostMapping("query/detail")
    public CommonResult<ProductSpuDetailVO> detail(@RequestBody DetailQueryInput detailQueryInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        ProductSpuDetailVO vo = productService.getDetail(detailQueryInput.getId(), contextInfoDTO.getTenantId());
        return CommonResult.ok(vo);
    }

    /**
     * 货品sku - 删除
     *
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/delete-sku")
    @BizLogRecord(operationName = "删除sku", bizKey = "#skuId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Void> deleteSku(Long id) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productService.deleteProductSku(id, contextInfoDTO);
        // 日志上下文
        BizLogRecordContext.put("skuId", id);
        BizLogRecordContext.put("tenantId", contextInfoDTO.getTenantId());
        Map<String, Object> content = new HashMap<>();
        content.put("skuId", id);
        BizLogRecordContext.put("content", content);
        return CommonResult.ok();
    }

    /**
     * 货品spu - 删除
     *
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/delete-product")
    @BizLogRecord(operationName = "删除spu", bizKey = "#spuId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult deleteProduct(Long id) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productService.deleteProduct(id, contextInfoDTO);
        // 日志上下文
        BizLogRecordContext.put("spuId", id);
        BizLogRecordContext.put("tenantId", contextInfoDTO.getTenantId());
        Map<String, Object> content = new HashMap<>();
        content.put("spuTitle", BizLogRecordContext.get("spuTitle"));
        BizLogRecordContext.put("content", content);
        return CommonResult.ok();
    }
//    /**
//     * 货品申请记录列表  -- 已废弃
//     *
//     * @param productQueryInput
//     * @return
//     */
//    @PostMapping("query/apply/list")
//    public CommonResult<PageInfo<ProductSkuApplyListVO>> applyList(@RequestBody @Valid ProductQueryInput productQueryInput){
//        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
//        PageInfo<ProductSkuApplyListVO> page = productService.pageApply(productQueryInput,contextInfoDTO);
//        return CommonResult.ok(page);
//    }
//    /**
//     * 货品申请记录 - 详情
//     *
//     * @return
//     */
//    @PostMapping("query/apply/detail")
//    public CommonResult<ProductSkuApplyDetailVO> applyDetail(@RequestBody @Valid DetailQueryInput detailQueryInput){
//        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
//        ProductSkuApplyDetailVO vo = productService.getApplyDetail(detailQueryInput.getId(),contextInfoDTO);
//        return CommonResult.ok(vo);
//    }
//    /**
//     * 提交审核
//     *
//     * @return
//     */
//    @PostMapping("query/apply/applyCommit")
//    public CommonResult<Void> applyCommit(@RequestBody @Valid DetailQueryInput detailQueryInput){
//        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
//        productService.applyCommit(detailQueryInput.getId(),contextInfoDTO);
//        return CommonResult.ok();
//    }

    /**
     * 新增sku
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("sku/add")
    public CommonResult updateSku(@RequestBody ProductSpuAddInput input) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productService.addSku(input, contextInfoDTO);
        return CommonResult.ok();

    }

    /**
     * 修改sku
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("sku/update")
    @BizLogRecord(operationName = "编辑sku", bizKey = "#newSku.id", bizKeyTenantId = "#newSku.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult updateSku(@RequestBody ProductSkuAddInput skuInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productService.updateSku(skuInput, contextInfoDTO);

        Map<String, Object> content = new HashMap<>();
        content.put("newSku", BizLogRecordContext.get("newSku"));
        content.put("oriSku", BizLogRecordContext.get("oriSku"));
        BizLogRecordContext.put("content", content);

        return CommonResult.ok();

    }

    /**
     * 创建商城商品
     *
     * @param skuId
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/create_mall_product")
    public CommonResult createMallProduct(Long skuId, Long classificationId) {
        Map<String, Long> mallProduct = productService.createMallProduct(skuId, classificationId, getMerchantInfoDTO());
        return CommonResult.ok(mallProduct);
    }

    /**
     * 查看关联商品信息
     *
     * @param saleProductRelateInfoInput
     * @return
     */
    @PostMapping("/query/relate_sale_product")
    public CommonResult<PageInfo<MarketSpuVO>> queryRelateSaleProduct(@RequestBody SaleProductRelateInfoInput saleProductRelateInfoInput) {
        PageInfo<MarketSpuVO> pageInfo = marketService.listBySkuId(saleProductRelateInfoInput, getMerchantInfoDTO());
        return CommonResult.ok(pageInfo);
    }

    /**
     * 出入库记录
     *
     * @param warehousingRecordInput
     * @return
     */
    @PostMapping("/query/warehousing-record")
    public CommonResult<PageInfo<WarehousingRecordVO>> warehousingRecord(@RequestBody WarehousingRecordInput warehousingRecordInput) {
        Long tenantId = getMerchantInfoDTO().getTenantId();
        PageInfo<WarehousingRecordVO> warehousingRecordVOPageInfo = productService.warehousingRecord(warehousingRecordInput, getMerchantInfoDTO());
        return CommonResult.ok(warehousingRecordVOPageInfo);
    }

    /**
     * 导出出入库记录
     *
     * @param warehousingRecordInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("export-warehousing-record")
    public CommonResult exportWarehousingRecord(@RequestBody WarehousingRecordInput warehousingRecordInput) {
        productService.exportWarehousingRecord(warehousingRecordInput, getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 配送记录
     *
     * @param deliveryPlanRecordInput
     * @return
     */
    @PostMapping("/query/delivery-plan-record")
    public CommonResult<PageInfo<DeliveryPlanRecordVO>> deliveryPlanRecord(@RequestBody DeliveryPlanRecordInput deliveryPlanRecordInput) {
        PageInfo<DeliveryPlanRecordVO> pageInfo = productService.pageDeliveryPlanRecord(deliveryPlanRecordInput, getMerchantInfoDTO());
        return CommonResult.ok(pageInfo);
    }

    /**
     * 配送记录导出
     *
     * @param deliveryPlanRecordInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("export-delivery-plan-record")
    public CommonResult exportDeliveryPlanRecord(@RequestBody DeliveryPlanRecordInput deliveryPlanRecordInput) {
        productService.exportDeliveryPlanRecord(deliveryPlanRecordInput, getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 导入自营货品 4.19
     *
     * @param file
     * @return
     * @throws IOException
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("import-agent-product")
    public CommonResult importAgentProduct(@RequestBody MultipartFile file) throws IOException {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ExcelImportResDTO excelImportResDTO = productService.importAgentProduct(file, loginContextInfoDTO);
        return CommonResult.ok(excelImportResDTO);
    }

    /**
     * 更新税率
     *
     * @param skuInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/tax-rate-value")
    @BizLogRecord(operationName = "编辑sku", bizKey = "#newSku.id", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult updateTaxRateValue(@RequestBody ProductSkuAddInput skuInput) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        productService.updateTaxRateValue(loginContextInfoDTO.getTenantId(), skuInput.getId(), skuInput.getTaxRateValue());
        BizLogRecordContext.put("tenantId", loginContextInfoDTO.getTenantId());
        return CommonResult.ok();
    }

    /**
     * 更新自有编码
     *
     * @param skuInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/custom-sku-code")
    @BizLogRecord(operationName = "编辑sku", bizKey = "#newSku.id", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult updateCustomSkuCode(@RequestBody ProductSkuAddInput skuInput) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        productService.updateCustomSkuCode(loginContextInfoDTO.getTenantId(), skuInput.getId(), skuInput.getCustomSkuCode());
        BizLogRecordContext.put("tenantId", loginContextInfoDTO.getTenantId());
        return CommonResult.ok();
    }

    /**
     * 我要卖货的货品查询
     *
     * @param productSkuQueryConditionDTO
     * @return
     */
    @PostMapping("query/product-sku-list")
    public CommonResult<List<ProductSkuDetailVO>> queryProductSkus(@RequestBody ProductSkuQueryConditionDTO productSkuQueryConditionDTO) {
        Long tenantId = getMerchantInfoDTO().getTenantId();
        List<Long> skuIds = productSkuQueryConditionDTO.getSkuIds();
        if (CollectionUtil.isEmpty(skuIds)) {
            return CommonResult.ok(Collections.emptyList());
        }
        ProductSkuDetailVO productSkuDetailVO = productService.getBySkuId(skuIds.get(0), tenantId);
        if (ObjectUtil.isEmpty(productSkuDetailVO)) {
            return CommonResult.ok(Collections.emptyList());
        }
        return CommonResult.ok(Collections.singletonList(productSkuDetailVO));
    }

    /**
     * 同步货品
     * 手动触发同步，后端修正数据的兜底代码。      暂不做迁移 @Cathy
     *
     * @param productSkuQueryConditionDTO
     * @return
     */
    @PostMapping("upsert/synchronized-product")
    public CommonResult<Boolean> synchronizedProduct(@RequestBody ProductSkuQueryConditionDTO productSkuQueryConditionDTO) {
        productService.synchronizedProduct(productSkuQueryConditionDTO.getSkuIds(), productSkuQueryConditionDTO.getTenantId());
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 同步货品的接口
     *
     * @param summerfarmSkuInput
     */
    @RequestMapping(value = "/synchronized-supply-sku", method = RequestMethod.POST)
    public CommonResult synchronizedSupplySku(@RequestBody SummerfarmSkuInput summerfarmSkuInput, Long tenantId) {
        productSkuService.synchronizedSupplySku(summerfarmSkuInput, tenantId);
        return CommonResult.ok();
    }

    /**
     * 更新sku的停用状态
     *
     * @param input
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/sku-use-flag")
    @BizLogRecord(operationName = "编辑sku", bizKey = "#newSku.id", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Void> updateSkuUseFlag(@RequestBody @Valid ProductSkuUseInput input) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        productService.updateSkuUseFlag(input, loginContextInfoDTO.getTenantId());
        BizLogRecordContext.put("tenantId", loginContextInfoDTO.getTenantId());
        return CommonResult.ok();
    }

    /**
     * 校验是sku是否可以停用
     *
     * @param input
     * @return
     */
    @PostMapping("check/sku-use-flag")
    public CommonResult<Void> checkSkuUseFlag(@RequestBody @Valid ProductSkuUseInput input) {
        productService.checkSkuUseFlag(input, getMerchantInfoDTO().getTenantId());
        return CommonResult.ok();
    }

    /**
     * 查询sku关联的商品数量
     *
     * @param skuId
     * @return
     */
    @PostMapping("count/associatedItem")
    public CommonResult<Integer> associatedItemCount(Long skuId) {
        return CommonResult.ok(productService.associatedItemCount(skuId, getMerchantInfoDTO().getTenantId()));
    }
}
