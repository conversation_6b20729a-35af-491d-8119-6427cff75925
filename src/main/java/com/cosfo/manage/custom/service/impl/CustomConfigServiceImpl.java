package com.cosfo.manage.custom.service.impl;

import com.cosfo.manage.common.config.CustomConfig;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.custom.model.vo.CustomConfigVO;
import com.cosfo.manage.custom.service.CustomConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-12-26
 * @Description:
 */
@Slf4j
@Service
public class CustomConfigServiceImpl implements CustomConfigService {

    @Resource
    private CustomConfig customConfig;

    @Override
    public CustomConfigVO queryConfigVO() {
        Long tenantId = UserLoginContextUtils.getTenantId();

        Integer juepeiOrderExportFlag = DefaultFlagEnum.FALSE.getFlag();
        if (customConfig.getJuepeiTenantIds().contains(tenantId)) {
            juepeiOrderExportFlag = DefaultFlagEnum.TURE.getFlag();
        }

        return CustomConfigVO.builder().juepeiOrderExportFlag(juepeiOrderExportFlag).build();
    }
}
