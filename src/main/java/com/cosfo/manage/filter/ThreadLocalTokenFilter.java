//package com.cosfo.manage.filter;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.cosfo.manage.common.util.RedisUtils;
//import com.cosfo.manage.common.util.ThreadTokenHolder;
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.shiro.SecurityUtils;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import javax.servlet.*;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//import java.util.Objects;
//
///**
// * 用于把当前上下文中获取到的token信息放到tokenHolder里
// *
// * <AUTHOR>
// * @date 2020/7/13
// */
//@Component
//@Slf4j
//@Order(1000)
//public class ThreadLocalTokenFilter implements Filter {
//    @Resource
//    private RedisUtils redisUtils;
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        ThreadTokenHolder.clearMerchantInfo();
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        Object principal = SecurityUtils.getSubject().getPrincipal();
//        if(Objects.nonNull(principal)){
//            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(principal));
//            LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//            loginContextInfoDTO.setTenantId(Long.valueOf(jsonObject.get("tenantId").toString()));
//            loginContextInfoDTO.setAuthUserId(Long.valueOf(jsonObject.get("id").toString()));
//            loginContextInfoDTO.setPhone(jsonObject.get("phone").toString());
//            ThreadTokenHolder.setToken(loginContextInfoDTO);
//        }
//
//        filterChain.doFilter(servletRequest, servletResponse);
//    }
//
//    @Override
//    public void destroy() {
//        ThreadTokenHolder.clearMerchantInfo();
//    }
//}
