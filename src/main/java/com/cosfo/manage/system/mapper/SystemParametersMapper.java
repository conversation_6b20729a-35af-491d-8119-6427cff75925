package com.cosfo.manage.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.system.model.po.SystemParameters;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface SystemParametersMapper extends BaseMapper<SystemParameters> {


    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(SystemParameters record);

    /**
     * 查询
     * @param id
     * @return
     */
    SystemParameters selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SystemParameters record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(SystemParameters record);

    /**
     * 查询所有配置
     * @return
     */
    List<SystemParameters> selectAll();
    /**
     * 查询三方平台ticket
     * @return
     */
    SystemParameters selectByTpTicket(String ticketKey);
    /**
     * 查询所属服务域名
     * @return
     */
    SystemParameters selectByParameterKey(String parameter);

    /***
     * 修改第三方token
     * @param paramKey
     * @param tokenJson
     * @return
     */
    int updateByTokenJson(@Param("paramKey")String paramKey,@Param("tokenJson")String tokenJson);

    /**
     * 根据key查询
     * @param key
     * @return
     */
    SystemParameters selectByKey(String key);

    /**
     * 根据账号id和key查询
     * @param accountId 账号id
     * @param paramKey key
     * @return SystemParameters
     */
    SystemParameters selectByAccountIdAndKey(@Param("accountId") Long accountId, @Param("paramKey") String paramKey);
}
