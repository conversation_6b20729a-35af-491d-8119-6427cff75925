package com.cosfo.manage.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.manage.client.pos.openapi.QimaiPosOrderOpenProvider;
import com.cosfo.manage.client.pos.req.QimaiPosAfterSaleOrderParams;
import com.cosfo.manage.client.pos.req.QimaiPosAfterSaleOrderReq;
import com.cosfo.manage.client.pos.req.QimaiPosOrderReq;
import com.cosfo.manage.client.pos.req.RefundItemList;
import com.cosfo.manage.client.pos.resp.FailPosResp;
import com.cosfo.manage.common.context.PosOrderChannelTypeEnum;
import com.cosfo.manage.common.context.PosOrderTransOptTypeEnum;
import com.cosfo.manage.pos.model.dto.PosOrderDTO;
import com.cosfo.manage.pos.model.dto.PosOrderItemDTO;
import com.cosfo.manage.pos.model.dto.PostOrderItemRefundDTO;
import com.cosfo.manage.pos.model.po.PosOrderTransLog;
import com.cosfo.manage.pos.model.vo.FailPosOrderVO;
import com.cosfo.manage.pos.repository.PosOrderTransLogRepository;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import com.cosfo.manage.provider.convert.QimaiPosOrderConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@DubboService
@Component
@Slf4j
public class QimaiPosOrderOpenProviderImpl implements QimaiPosOrderOpenProvider {

    @Resource
    private PosOrderDomainService posOrderDomainService;
    @Resource
    private PosOrderTransLogRepository posOrderTransLogRepository;

    @Override
    public DubboResponse<FailPosResp> saveOrUpdatePosOrder(String reqString) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        FailPosResp failPosResp =null;
        PosOrderTransLog posOrderTransLog = savePosOrderTransLog (tenantId, reqString, PosOrderTransOptTypeEnum.ORDER);

        try {
            QimaiPosOrderReq req = JSON.parseObject (reqString,QimaiPosOrderReq.class);
    //        订单状态 20=已支付；70已完成；90=已关闭
            List<Integer> successOrderStatusList = Lists.newArrayList (20, 70);
            List<Integer> deleteOrderStatusList = Lists.newArrayList (90);

            int status = req.getParams ().getStatus ();
            if(successOrderStatusList.contains (status)) {
                PosOrderDTO posOrderDTO = QimaiPosOrderConvert.INSTANCE.req2PosOrderDTO (req.getParams (), tenantId);
                List<PosOrderItemDTO> orderItemDTOList = posOrderDTO.getOrderItemDTOList ();
                if (CollectionUtil.isEmpty (orderItemDTOList)) {
                    return DubboResponse.getOK ();
                }
                posOrderDTO.setChannelType (PosOrderChannelTypeEnum.QI_MAI.getCode ());
                posOrderDTO.setDetailInfo (JSON.toJSONString (req));
                List<FailPosOrderVO> failPosOrderVOS = posOrderDomainService.savePosOrder (Collections.singletonList (posOrderDTO), PosOrderChannelTypeEnum.QI_MAI.getCode (), tenantId);
                if (!CollectionUtil.isEmpty (failPosOrderVOS)) {
                    failPosResp = QimaiPosOrderConvert.INSTANCE.failVO2Resp (failPosOrderVOS).get (0);
                }
            }
        }catch (Exception e){
            log.warn ("savePosOrderTransLog失败，reqString={}",reqString);
            posOrderTransLog.setSuccessFlag (false);
            posOrderTransLog.setResp (e.getMessage ());
            posOrderTransLogRepository.updateById (posOrderTransLog);
        }
//        if(deleteOrderStatusList.contains (status)) {
//            //整单退
//            String orderNo = req.getParams ().getOrderNo ();
//            String shopCode = req.getParams ().getShopCode ();
//            posOrderService.removeByOrderNoList (Collections.singletonList (orderNo),PosOrderChannelTypeEnum.QI_MAI.getCode (), shopCode, tenantId);
//            return DubboResponse.getOK ();
//        }
        return DubboResponse.getOK (failPosResp);
    }

    private PosOrderTransLog savePosOrderTransLog(Long tenantId, String reqString, PosOrderTransOptTypeEnum posOrderTransOptTypeEnum) {
        String orderNo = "";
        try {
            if(posOrderTransOptTypeEnum.getCode ().equals (PosOrderTransOptTypeEnum.ORDER.getCode ())) {
                QimaiPosOrderReq req = JSON.parseObject (reqString, QimaiPosOrderReq.class);
                orderNo = req.getParams ().getOrderNo ();
            } else if (posOrderTransOptTypeEnum.getCode ().equals (PosOrderTransOptTypeEnum.REFUND.getCode ())) {
                QimaiPosAfterSaleOrderReq req = JSON.parseObject (reqString, QimaiPosAfterSaleOrderReq.class);
                orderNo = req.getParams ().getOrderNo ();
            }
        }catch (Exception e){
            log.warn ("savePosOrderTransLog解析order失败，reqString={}",reqString,e);
        }
        PosOrderTransLog posOrderTransLog = new PosOrderTransLog ();
        posOrderTransLog.setChannelType(PosOrderChannelTypeEnum.QI_MAI.getCode ());
        posOrderTransLog.setOptType(posOrderTransOptTypeEnum.getCode ());
        posOrderTransLog.setTenantId(tenantId);
        posOrderTransLog.setOrderNo(orderNo);
        posOrderTransLog.setReq(reqString);
        posOrderTransLogRepository.save (posOrderTransLog);
        return posOrderTransLog;
    }


    @Override
    public DubboResponse<FailPosResp> saveOrUpdatePosAfterSaleOrder(@Valid String reqString) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);

        PosOrderTransLog posOrderTransLog = savePosOrderTransLog (tenantId, reqString, PosOrderTransOptTypeEnum.REFUND);
        try {
            QimaiPosAfterSaleOrderReq qimaiPosAfterSaleOrderReq = JSON.parseObject (reqString, QimaiPosAfterSaleOrderReq.class);

            QimaiPosAfterSaleOrderParams qimaiPosAfterSaleOrder = qimaiPosAfterSaleOrderReq.getParams ();
            String orderNo = qimaiPosAfterSaleOrder.getOrderNo ();
            Long shopCode = qimaiPosAfterSaleOrder.getShopCode ();
            List<RefundItemList> refundItemList = qimaiPosAfterSaleOrder.getRefundItemList ();
            if (CollectionUtil.isNotEmpty (refundItemList)) {
                List<PostOrderItemRefundDTO> postOrderItemRefundDTOS = QimaiPosOrderConvert.INSTANCE.req2ItemRefundDTOs (refundItemList.stream ().filter (r -> StringUtils.isNotEmpty (r.getItemSign ())).collect (Collectors.toList ()));
                posOrderDomainService.updatePosOrder4Refund (orderNo, shopCode, PosOrderChannelTypeEnum.QI_MAI.getCode (), tenantId, postOrderItemRefundDTOS);
            }
        }catch (Exception e){
            log.warn ("saveOrUpdatePosAfterSaleOrder失败，reqString={}",reqString);
            posOrderTransLog.setSuccessFlag (false);
            posOrderTransLog.setResp (e.getMessage ());
            posOrderTransLogRepository.updateById (posOrderTransLog);
        }
        return DubboResponse.getOK ();
    }
}
