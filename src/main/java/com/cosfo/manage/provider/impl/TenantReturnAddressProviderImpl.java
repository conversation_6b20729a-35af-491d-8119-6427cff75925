package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.tenant.TenantReturnAddressProvider;
import com.cosfo.manage.client.tenant.resp.TenantReturnAddressResp;
import com.cosfo.manage.order.model.vo.ReturnAddressVO;
import com.cosfo.manage.tenant.dao.TenantReturnAddressDao;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * @author: xiaowk
 * @date: 2023/5/30 下午4:58
 */
@DubboService
@Component
@Slf4j
public class TenantReturnAddressProviderImpl implements TenantReturnAddressProvider {
    @Resource
    private TenantReturnAddressDao tenantReturnAddressDao;

    @Override
    public DubboResponse<TenantReturnAddressResp> getTenantReturnAddress(Long returnAddressId) {
        ReturnAddressVO vo = tenantReturnAddressDao.getByPrimaryKey(returnAddressId);
        if(vo == null){
            return DubboResponse.getOK();
        }
        TenantReturnAddressResp resp = new TenantReturnAddressResp();
        resp.setId(vo.getId());
        resp.setTenantId(vo.getTenantId());
        resp.setProvince(vo.getProvince());
        resp.setCity(vo.getCity());
        resp.setArea(vo.getArea());
        resp.setAddress(vo.getAddress());
        resp.setHouseNo(vo.getHouseNo());
        resp.setPoiNote(vo.getPoiNote());
        resp.setContactName(vo.getContactName());
        resp.setContactPhone(vo.getContactPhone());
        resp.setCreateTime(vo.getCreateTime());
        resp.setUpdateTime(vo.getUpdateTime());

        return DubboResponse.getOK(resp);

    }
}
