package com.cosfo.manage.keruyun.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.keruyun.dto.OrderDetailQueryDTO;
import com.cosfo.manage.keruyun.dto.OrderListQueryDTO;
import com.cosfo.manage.keruyun.facade.AuthType;
import com.cosfo.manage.keruyun.facade.KryAPI;
import com.cosfo.manage.keruyun.facade.KryCallFacade;
import com.cosfo.manage.keruyun.vo.KryOrderDetailVO;
import com.cosfo.manage.keruyun.vo.KryOrderListVO;
import com.cosfo.manage.keruyun.vo.KryPageVO;
import com.cosfo.manage.keruyun.vo.KryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class KryServiceOnPos {

    @Autowired
    private KryCallFacade kryCallService;
    public List<KryOrderDetailVO> orderDetail(Long shopId, List<Long> orderIds) {
        if(CollectionUtil.isEmpty (orderIds)){
            return Collections.emptyList ();
        }
        String token = kryCallService.getToken(shopId);
        OrderDetailQueryDTO queryDTO = new OrderDetailQueryDTO ();
        queryDTO.setShopIdenty(shopId);
        queryDTO.setIds(orderIds);
        log.info ("请求pos订单详情，shopid={},orderIds={}", shopId, orderIds);
        String response = kryCallService.postCall (KryAPI.ORDER_DETAIL_ONPOS, AuthType.SHOP, shopId, token, queryDTO);
        log.info ("请求pos订单详情，shopid={},orderIds={},reponse={}", shopId, orderIds,response);
        KryResponse kryResponse = JSON.parseObject (response, KryResponse.class);
        if(kryResponse.getCode () == 0){
            List<KryOrderDetailVO> kryOrderDetailVOS = JSON.parseArray (JSON.toJSONString (kryResponse.getResult ()), KryOrderDetailVO.class);
            kryOrderDetailVOS.forEach (e-> e.setDetailInfo (JSON.toJSONString (e)));
            return kryOrderDetailVOS;
        }else{
            log.error ("请求pos订单详情失败，shopid={},orderIds={},reponse={}", shopId, orderIds,response);
            return Collections.emptyList ();
        }
    }

    public KryPageVO<KryOrderListVO> orderList(Long shopId, Long startTime, Long endTime, Integer pageNo) {
        String token = kryCallService.getToken(shopId);
        OrderListQueryDTO queryDTO = new OrderListQueryDTO ();
        queryDTO.setStartTime(startTime);
        queryDTO.setEndTime(endTime);
        queryDTO.setTimeType(2);
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(100);
        queryDTO.setShopIdenty(shopId);
        log.info ("请求第{}页pos订单，shopid={},startTime={},endTime={}", pageNo, shopId, startTime, endTime);
        String response = kryCallService.postCall(KryAPI.ORDER_LIST_ONPOS, AuthType.SHOP, shopId, token, queryDTO);
        log.info ("请求第{}页pos订单，shopid={},startTime={},endTime={},response={}", pageNo, shopId, startTime, endTime,response);
        KryResponse kryResponse = JSON.parseObject (response, KryResponse.class);
        if(kryResponse.getCode () == 0){
            KryPageVO kryPageVO = JSON.parseObject (JSON.toJSONString (kryResponse.getResult ()), KryPageVO.class);
            kryPageVO.setItems (JSON.parseArray (JSON.toJSONString (kryPageVO.getItems ()),KryOrderListVO.class));;
            return kryPageVO;
        }else{
            log.error ("请求第{}页pos订单失败，shopid={},startTime={},endTime={},response={}", pageNo, shopId, startTime, endTime,response);
            return null;
        }
    }
}
