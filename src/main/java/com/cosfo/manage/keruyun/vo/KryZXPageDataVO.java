package com.cosfo.manage.keruyun.vo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class KryZXPageDataVO {

    private String totalCount;
    private String pageNo;
    private String pageSize;
    private String totalPage;
    private List<KryZXOrderListVO> list;
    private String prevPage;
    private String nextPage;
    private boolean emptyForList;

    public boolean hasNext(String totalRowsString,int pageSize,int pageIndex) {
        if (StringUtils.isBlank (totalRowsString)) {
            return false;
        }
        int totalRows = Integer.parseInt (totalRowsString);
        int totalPages = (totalRows / pageSize);
        // 处理总页数的余数
        if (totalRows % pageSize != 0) {
            totalPages++;
        }
        // 判断当前页是否小于总页数
        return pageIndex < totalPages;
    }
}