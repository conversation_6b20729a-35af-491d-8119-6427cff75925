/**
  * Copyright 2023 bejson.com 
  */
package com.cosfo.manage.keruyun.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Auto-generated: 2023-11-29 18:57:11
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Data
public class Promotions {

    private long tradeId;
    private long paymentItemId;
    private int isReconciliation;
    private int statusFlag;
    private int source;
    private BigDecimal mount;
    private BigDecimal platformAmount;
    private int shopActivityAmount;
    private int serviceCharge;
    private String shopActualAmount;
    private int unitActualAmount;

}