<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.wechat.mapper.WechatTemplatePackageMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.wechat.model.po.WechatTemplatePackage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="audit_id" jdbcType="VARCHAR" property="auditId" />
    <result column="pkg_status" jdbcType="INTEGER" property="pkgStatus" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pkg_desc" jdbcType="VARCHAR" property="pkgDesc" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, appid, audit_id, pkg_status, version, remark, pkg_desc, template_id, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wechat_template_package
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectPackageListByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_template_package
    where
    <if test="statusList != null  and statusList.size()&gt;0">
     pkg_status IN
      <foreach close=")" collection="statusList" item="status" open="(" separator=",">
        #{status}
      </foreach>
    </if>
    and appid = #{appId,jdbcType=VARCHAR}
  </select>
  <select id="selectPackageByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_template_package
    where pkg_status = #{status,jdbcType=INTEGER}
    and appid = #{appId,jdbcType=VARCHAR}
  </select>

  <select id="selectPagListByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_template_package
    where
    <if test="statusList != null  and statusList.size()&gt;0">
     pkg_status IN
      <foreach close=")" collection="statusList" item="status" open="(" separator=",">
        #{status}
      </foreach>
    </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wechat_template_package
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatTemplatePackage" useGeneratedKeys="true">
    insert into wechat_template_package (appid, audit_id, pkg_status, 
      version, remark, pkg_desc, 
      template_id, create_time, update_time
      )
    values (#{appid,jdbcType=VARCHAR}, #{auditId,jdbcType=VARCHAR}, #{pkgStatus,jdbcType=INTEGER}, 
      #{version,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{pkgDesc,jdbcType=VARCHAR}, 
      #{templateId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatTemplatePackage" useGeneratedKeys="true">
    insert into wechat_template_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="auditId != null">
        audit_id,
      </if>
      <if test="pkgStatus != null">
        pkg_status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="pkgDesc != null">
        pkg_desc,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid,jdbcType=VARCHAR},
      </if>
      <if test="auditId != null">
        #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="pkgStatus != null">
        #{pkgStatus,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pkgDesc != null">
        #{pkgDesc,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.wechat.model.po.WechatTemplatePackage">
    update wechat_template_package
    <set>
      <if test="appid != null">
        appid = #{appid,jdbcType=VARCHAR},
      </if>
      <if test="auditId != null">
        audit_id = #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="pkgStatus != null">
        pkg_status = #{pkgStatus,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pkgDesc != null">
        pkg_desc = #{pkgDesc,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.wechat.model.po.WechatTemplatePackage">
    update wechat_template_package
    set appid = #{appid,jdbcType=VARCHAR},
      audit_id = #{auditId,jdbcType=VARCHAR},
      pkg_status = #{pkgStatus,jdbcType=INTEGER},
      version = #{version,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      pkg_desc = #{pkgDesc,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>