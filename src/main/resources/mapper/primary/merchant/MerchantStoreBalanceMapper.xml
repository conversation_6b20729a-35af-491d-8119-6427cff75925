<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreBalanceMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStoreBalance">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="store_no" jdbcType="VARCHAR" property="storeNo"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="account_type" jdbcType="TINYINT" property="accountType"/>
        <result column="fund_account_id" jdbcType="BIGINT" property="fundAccountId"/>
    </resultMap>

    <sql id="Base_Column_List">id, tenant_id, store_id, store_no, balance, create_time, update_time, account_type, fund_account_id</sql>

    <insert id="batchInsert">
        insert into merchant_store_balance (tenant_id, store_id, store_no, balance, account_type, fund_account_id)
        values
        <foreach collection="balanceList" item="record" separator=",">
            (#{record.tenantId}, #{record.storeId}, #{record.storeNo}, #{record.balance}, #{record.accountType}, #{record.fundAccountId})
        </foreach>
    </insert>

    <update id="updateBalanceByStoreId">
        update merchant_store_balance set balance = balance + #{changeBalance}
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT}
        AND balance + #{changeBalance} >=0 and account_type = #{accountType,jdbcType=TINYINT}
        <if test="fundAccountId != null">
            and fund_account_id = #{fundAccountId,jdbcType=BIGINT}
        </if>
    </update>

    <select id="selectByStoreId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT} and account_type = #{accountType,jdbcType=TINYINT}
        <if test="fundAccountId != null">
            and fund_account_id = #{fundAccountId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectByStoreIdForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT} for update
    </select>

    <select id="selectByStoreIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND account_type = #{accountType,jdbcType=TINYINT}
        AND store_id IN
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="balanceComposition" resultType="com.cosfo.manage.merchant.model.vo.balance.BalanceCompositionVO">
        select
        id, tenant_id tenantId, store_id storeId, store_no storeNo, balance, account_type accountType, fund_account_id fundAccountId,
        create_time createTime, update_time updateTime
        from merchant_store_balance
        <where>
            <if test="storeId != null">
                AND store_id = #{storeId,jdbcType=BIGINT}
            </if>
            <if test="storeNo != null">
                AND store_no = #{storeNo,jdbcType=VARCHAR}
            </if>
            <if test="fundAccountId != null and fundAccountId > 0">
                AND fund_account_id = #{fundAccountId,jdbcType=BIGINT} and account_type = 1
            </if>
            <if test="fundAccountId != null and fundAccountId == 0">
                AND account_type = 0
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId,jdbcType=BIGINT}
            </if>
        </where>
        order by update_time desc
    </select>
    <select id="queryBalanceTotal" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        <where>
            <if test="storeId != null">
                AND store_id = #{storeId,jdbcType=BIGINT}
            </if>
            <if test="storeNo != null">
                AND store_no = #{storeNo,jdbcType=VARCHAR}
            </if>
            <if test="fundAccountId != null and fundAccountId > 0">
                AND fund_account_id = #{fundAccountId,jdbcType=BIGINT} and account_type = 1
            </if>
            <if test="fundAccountId != null and fundAccountId == 0">
                AND account_type = 0
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>
