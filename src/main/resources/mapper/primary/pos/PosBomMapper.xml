<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.pos.mapper.PosBomMapper">
<insert id="batchInsert" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true" >
    insert into pos_bom
    (tenant_id, channel_type,target_type, target_value, out_menu_specification ,out_menu_code, merchant_store_id,out_menu_name,price,available_date )
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.tenantId}, #{item.channelType},#{item.targetType}, #{item.targetValue}, #{item.outMenuSpecification},#{item.outMenuCode},#{item.merchantStoreId}, #{item.outMenuName}, #{item.price}, #{item.availableDate})
    </foreach>
    ON DUPLICATE KEY UPDATE
    out_menu_specification = VALUES(out_menu_specification),
    out_menu_name = VALUES(out_menu_name),
    price = VALUES(price)
</insert>

    <select id="listLatestAvailableBom" resultType="com.cosfo.manage.pos.model.po.PosBom">
        select
        id, tenant_id, channel_type, target_type, target_value, out_menu_specification ,out_menu_code, merchant_store_id,out_menu_name,price,available_date
        from pos_bom
        where tenant_id = #{tenantId}
        and available_date = (select max(available_date) from pos_bom where tenant_id = #{tenantId})
    </select>

    <select id="listTenantIds" resultType="java.lang.Long">
        select distinct tenant_id from pos_bom
    </select>
</mapper>
