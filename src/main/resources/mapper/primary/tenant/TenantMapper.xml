<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.Tenant">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="admin_id" property="adminId"/>
        <result column="op_uid" jdbcType="BIGINT" property="opUid"/>
        <result column="op_uname"  jdbcType="VARCHAR" property="opUname"/>
        <result column="operator"  jdbcType="VARCHAR" property="operator"/>
        <result column="profit_sharing_switch" jdbcType="INTEGER" property="profitSharingSwitch"/>
        <result column="online_pay_channel" jdbcType="INTEGER" property="onlinePayChannel"/>
    </resultMap>

    <resultMap id="SupplierResultMap" type="com.cosfo.manage.tenant.model.vo.SupplierTenantVO">
        <id column="tenantId" jdbcType="BIGINT" property="tenantId"/>
        <result column="companyName" jdbcType="VARCHAR" property="companyName"/>
        <result column="accountName" jdbcType="VARCHAR" property="accountName"/>
        <result column="accountNumber" jdbcType="VARCHAR" property="accountNumber"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, phone, password, tenant_name, type, status, create_time, update_time, admin_id,op_uid,op_uname, operator, profit_sharing_switch, online_pay_channel
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tenant
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tenant
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.cosfo.manage.tenant.model.po.Tenant">
        insert into tenant (id, phone, password,
                            tenant_name, type, status,
                            create_time, update_time,op_uid,op_uname,
                            operator,profit_sharing_switch,online_pay_channel)
        values (#{id,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
                #{tenantName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{opUid,jdbcType=BIGINT}, #{opUname,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR},#{profitSharingSwitch,jdbcType=INTEGER},#{onlinePayChannel,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.cosfo.manage.tenant.model.po.Tenant">
        insert into tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="opUid != null">
                op_uid,
            </if>
            <if test="opUname != null">
                op_uname,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="profitSharingSwitch != null">
                profit_sharing_switch,
            </if>
            <if test="onlinePayChannel != null">
                online_pay_channel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="opUid != null">
                #{opUid,jdbcType=BIGINT},
            </if>
            <if test="opUname != null">
                #{opUname,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="profitSharingSwitch != null">
                #{profitSharingSwitch,jdbcType=VARCHAR},
            </if>
            <if test="onlinePayChannel != null">
                #{onlinePayChannel,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.Tenant">
        update tenant
        <set>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                tenant_name = #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="opUid != null">
                op_uid = #{opUid,jdbcType=BIGINT},
            </if>
            <if test="opUname != null">
                op_uname = #{opUname,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="profitSharingSwitch != null">
                profit_sharing_switch = #{profitSharingSwitch,jdbcType=VARCHAR},
            </if>
            <if test="onlinePayChannel != null">
                online_pay_channel = #{onlinePayChannel,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.Tenant">
        update tenant
        set phone       = #{phone,jdbcType=VARCHAR},
            password    = #{password,jdbcType=VARCHAR},
            tenant_name = #{tenantName,jdbcType=VARCHAR},
            type        = #{type,jdbcType=INTEGER},
            status      = #{status,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            op_uid = #{opUid,jdbcType=BIGINT},
            op_uname = #{opUname,jdbcType=VARCHAR},
            operator = #{operator,jdbcType=VARCHAR},
            profit_sharing_switch = #{profitSharingSwitch,jdbcType=VARCHAR},
            online_pay_channel = #{onlinePayChannel,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from tenant
        where
        phone = #{phone}
    </select>

    <select id="querySupplierInfoBySupplierTenantIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from tenant
        <where>
            <if test="supplierTenantIds != null">
                id in
                <foreach collection="supplierTenantIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryTenantInfoByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        tenant
        where type = #{type}
    </select>
    <select id="queryEnableTenantInfos" resultType="com.cosfo.manage.tenant.model.po.Tenant">
        select
        <include refid="Base_Column_List"></include>
        from
        tenant
        where online_pay_channel = 1
        and profit_sharing_switch = 1
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        tenant
        <where>
            type = 0
            <if test="adminId != null">
                and admin_id = #{adminId}
            </if>
            <if test="tenantId != null">
                and id = #{tenantId}
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                and id in
                <foreach close=")" collection="tenantIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from tenant
        where id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listSupplier" resultType="com.cosfo.manage.tenant.model.vo.SupplierTenantVO">
        SELECT
            t.id AS tenantId,
            tc.company_name AS companyName
        FROM
            tenant t
                LEFT JOIN tenant_company tc ON t.id = tc.tenant_id
        WHERE
            t. `type` = 1
    </select>


    <select id="listSupplierByIds" resultMap="SupplierResultMap">
        SELECT
            t.id AS tenantId,
            tc.company_name AS companyName
        FROM
            tenant t
                LEFT JOIN tenant_company tc ON t.id = tc.tenant_id
        where t.id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByAdminId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"></include>
        from
            tenant
        where admin_id = #{adminId}
    </select>
</mapper>