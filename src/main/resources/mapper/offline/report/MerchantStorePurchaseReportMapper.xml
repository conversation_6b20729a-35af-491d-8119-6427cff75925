<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MerchantStorePurchaseReportMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStorePurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />

    <result column="store_no" jdbcType="BIGINT" property="storeNo" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="store_group" jdbcType="VARCHAR" property="storeGroup" />
    <result column="product_no" jdbcType="BIGINT" property="productNo" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="market_classification" jdbcType="VARCHAR" property="marketClassification" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="received_quantity" jdbcType="INTEGER" property="receivedQuantity" />
    <result column="reissue_quantity" jdbcType="INTEGER" property="reissueQuantity" />
    <result column="after_sale_amount" jdbcType="DECIMAL" property="afterSaleAmount" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />

    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_no, store_code,store_name, type, store_group, product_no, product_name, specification, specification_unit, warehouse_type, market_classification,
    category, brand, quantity, amount, delivery_time, received_quantity, reissue_quantity, after_sale_amount, create_time, update_time
  </sql>

  <select id="listAll" parameterType="com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportQueryDTO" resultType="com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportResultDTO">
    select id, tenant_id, store_no, store_code,store_name, type, store_group, product_no, product_name, specification, specification_unit, warehouse_type warehouseType, market_classification,
    category, brand, sum(quantity) as quantity, sum(amount) as amount, delivery_time, MAX(delivery_time) deliveryTimeMax, MIN(delivery_time) deliveryTimeMin, sum(received_quantity) as received_quantity , sum(reissue_quantity) as reissue_quantity,
    sum(after_sale_amount) as after_sale_amount, delivery_type, goods_type goodsType from merchant_store_purchase_report
    <where>
      tenant_id = #{tenantId}
      <if test="startTime != null and endTime != null">
        and delivery_time between #{startTime} and #{endTime}
      </if>
      <if test="productNo != null">
        and product_no = #{productNo}
      </if>
      <if test="productName != null and productName != '' ">
        and product_name like concat('%', #{productName} ,'%')
      </if>
      <if test="warehouseType != null">
        and warehouse_type = #{warehouseType}
      </if>
      <if test="deliveryType != null">
        and delivery_type = #{deliveryType}
      </if>
      <if test="goodsType != null">
        and goods_type = #{goodsType}
      </if>
      <if test="marketClassification != null and marketClassification!=''">
        and market_classification like concat(#{marketClassification},'%')
      </if>
      <if test="category != null and category!=''">
        and category like concat(#{category},'%')
      </if>
      <if test="brand != null and brand!=''">
        and brand like concat('%', #{brand} ,'%')
      </if>
      <if test="storeName != null and storeName!=''">
        and store_name like concat('%', #{storeName} ,'%')
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="storeGroup != null and storeGroup!=''">
        and store_group like concat('%', #{storeGroup} ,'%')
      </if>
        group by product_no,store_code
      <if test="sortWord != null and sortType != null">
        order by ${sortWord} ${sortType}
      </if>
      <if test="offset != null">
        limit #{offset},500
      </if>
    </where>
  </select>


  <select id="exportAll" parameterType="com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportQueryDTO" resultType="com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportResultDTO" fetchSize="1000" >
    select id, tenant_id, store_no, store_code,store_name, type, store_group, product_no, product_name, specification, specification_unit, warehouse_type warehouseType, market_classification,
    category, brand, sum(quantity) as quantity, sum(amount) as amount, delivery_time, MAX(delivery_time) deliveryTimeMax, MIN(delivery_time) deliveryTimeMin, sum(received_quantity) as received_quantity , sum(reissue_quantity) as reissue_quantity,
    sum(after_sale_amount) as after_sale_amount, delivery_type, goods_type goodsType from merchant_store_purchase_report
    <where>
      tenant_id = #{tenantId}
      <if test="startTime != null and endTime != null">
        and delivery_time between #{startTime} and #{endTime}
      </if>
      <if test="productNo != null">
        and product_no = #{productNo}
      </if>
      <if test="productName != null and productName != '' ">
        and product_name like concat('%', #{productName} ,'%')
      </if>
      <if test="warehouseType != null">
        and warehouse_type = #{warehouseType}
      </if>
      <if test="deliveryType != null">
        and delivery_type = #{deliveryType}
      </if>
      <if test="goodsType != null">
        and goods_type = #{goodsType}
      </if>
      <if test="marketClassification != null and marketClassification!=''">
        and market_classification like concat(#{marketClassification},'%')
      </if>
      <if test="category != null and category!=''">
        and category like concat(#{category},'%')
      </if>
      <if test="brand != null and brand!=''">
        and brand like concat('%', #{brand} ,'%')
      </if>
      <if test="storeName != null and storeName!=''">
        and store_name like concat('%', #{storeName} ,'%')
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="storeGroup != null and storeGroup!=''">
        and store_group like concat('%', #{storeGroup} ,'%')
      </if>
      group by product_no,store_code
      <if test="sortWord != null and sortType != null">
        order by ${sortWord} ${sortType}
      </if>
    </where>
  </select>
</mapper>
