<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.OrderItemDetailSummaryMapper">
    <select id="queryStoreCounts" resultType="com.cosfo.manage.report.model.dto.OrderPaymentStatistics">
        select count(distinct store_name) storeCounts, pay_type payType
        from order_item_detail_summary
        <where>
            tenant_id = #{tenantId}
            <if test="startTime != null and endTime != null">
                and time_tag between #{startTime} and #{endTime}
            </if>
            <if test="goodsType != null">
                and goods_type = #{goodsType}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
        </where>
        group by pay_type
    </select>

    <select id="querySummary" resultType="com.cosfo.manage.report.model.dto.OrderSummaryDTO">
        select ifnull(sum(amount), 0)            orderItemCount,
               ifnull(count(distinct order_no), 0) orderCount,
               ifnull(count(distinct item_id), 0)  skuCount
        from order_item_detail_summary
        <where>
            tenant_id = #{tenantId} and time_tag between #{startTime} and #{endTime}
            <if test="goodsType != null">
                and goods_type = #{goodsType}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
        </where>
    </select>
</mapper>
