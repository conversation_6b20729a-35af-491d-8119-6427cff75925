<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.ProductDetailSalesMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductDetailSales">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="supply_price" jdbcType="DECIMAL" property="supplyPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="sales_num" jdbcType="INTEGER" property="salesNum" />
    <result column="sales_price" jdbcType="DECIMAL" property="salesPrice" />
    <result column="after_sale_num" jdbcType="INTEGER" property="afterSaleNum" />
    <result column="after_sale_price" jdbcType="DECIMAL" property="afterSalePrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
    <result property="storeType" column="store_type" jdbcType="INTEGER"/>
    <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
    <result property="address" column="address" jdbcType="VARCHAR"/>
    <result property="itemId" column="item_id" jdbcType="INTEGER"/>
  </resultMap>
  <resultMap id="DTOResultMap" type="com.cosfo.manage.product.model.dto.ProductDetailSalesDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="supply_price" jdbcType="DECIMAL" property="supplyPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="sales_num" jdbcType="INTEGER" property="salesNum" />
    <result column="sales_price" jdbcType="DECIMAL" property="salesPrice" />
    <result column="after_sale_num" jdbcType="INTEGER" property="afterSaleNum" />
    <result column="after_sale_price" jdbcType="DECIMAL" property="afterSalePrice" />
    <result column="warehouse_type_desc" jdbcType="VARCHAR" property="warehouseTypeDesc" />
    <result column="delivery_type_desc" jdbcType="VARCHAR" property="deliveryTypeDesc" />
    <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
    <result property="storeType" column="store_type" jdbcType="INTEGER"/>
    <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
    <result property="address" column="address" jdbcType="VARCHAR"/>
    <result property="itemId" column="item_id" jdbcType="INTEGER"/>
    <result property="goodsTypeDesc" column="goods_type_desc" jdbcType="INTEGER"/>
    <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
    <result property="afterSaleUnit" column="after_sale_unit" jdbcType="VARCHAR"/>
    <result column="after_sale_apply_price" jdbcType="DECIMAL" property="afterSaleApplyPrice" />
    <result column="after_sale_total_price" jdbcType="DECIMAL" property="afterSaleTotalPrice" />

  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, time_tag, sku_id, warehouse_type, delivery_type, title, specification,
    category, brand_name, supply_price, price, sales_num, sales_price, after_sale_num,
    after_sale_price, create_time, update_time, category_id, store_type, store_name, address, item_id
  </sql>
  <select id="queryAll" resultMap="DTOResultMap" fetchSize="1000" >
    select id,
           tenant_id,
           time_tag,
           sku_id,
           warehouse_type,
           delivery_type,
           title,
           specification,
           category,
           brand_name,
           supply_price,
           price,
           sales_num,
           sales_price,
           after_sale_num,
           after_sale_price,
           case when goods_type = 0 then '供应商直发货品'
                when goods_type = 1 then '供应商直发货品'
                when goods_type = 2 then '自营货品'
           end goods_type_desc,
           supplier_name,
           after_sale_unit,
           after_sale_apply_price,
           after_sale_total_price,
           item_id
    from product_detail_sales
    where tenant_id = #{tenantId}
      and time_tag &gt;= #{startTime}
      and time_tag &lt;= #{endTime}
      <if test="goodsType != null">
          and goods_type = #{goodsType}
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
          and category_id in
          <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
              #{item}
          </foreach>
      </if>
      <if test="storeTypes != null and storeTypes.size() > 0">
          and store_type in
          <foreach collection="storeTypes" open="(" close=")" separator="," item="item">
              #{item}
          </foreach>
      </if>
      <if test="addressList != null and addressList.size() > 0">
          and address in
          <foreach collection="addressList" open="(" close=")" separator="," item="item">
              #{item}
          </foreach>
      </if>
      <if test="storeName != null">
          and store_name like concat('%', #{storeName}, '%')
      </if>
      <if test="title != null and title != ''">
          and title like concat('%', #{title}, '%')
      </if>
      <if test="itemId != null">
          and item_id = #{itemId}
      </if>
  </select>

</mapper>
