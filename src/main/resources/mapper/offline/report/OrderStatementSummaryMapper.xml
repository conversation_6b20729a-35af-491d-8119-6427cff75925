<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.OrderStatementSummaryMapper">

    <select id="queryByConditionSum" resultType="com.cosfo.manage.report.model.po.OrderStatementSummary">
        select
            ifnull(sum(total_price), 0) totalPrice,
            ifnull(sum(wechat_pay_total_price), 0) wechatPayTotalPrice,
            ifnull(sum(bill_balance_pay_total_price), 0) billBalancePayTotalPrice,
            ifnull(sum(supply_total_price), 0) supplyTotalPrice,
            ifnull(sum(supply_delivery_fee), 0) supplyDeliveryFee,
            ifnull(sum(refund_price_deducted_supply), 0) refundPriceDeductedSupply,
            ifnull(sum(total_difference), 0) totalDifference,
            ifnull(sum(goods_agent_fee), 0) goodsAgentFee,
            ifnull(sum(goods_agent_refund_fee_supply_responsibility), 0) goodsAgentRefundFeeSupplyResponsibility,
            ifnull(sum(sales_and_supply_difference), 0) salesAndSupplyDifference,
            ifnull(sum(refund_sales_and_supply_difference), 0) refundSalesAndSupplyDifference,
            ifnull(sum(gross_profit), 0) grossProfit,
            ifnull(sum(wechat_pay_sales_and_supply_difference_deducted_refund), 0) wechatPaySalesAndSupplyDifferenceDeductedRefund,
            ifnull(sum(bill_balance_pay_sales_and_supply_difference_deducted_refund), 0) billBalancePaySalesAndSupplyDifferenceDeductedRefund,
            ifnull(sum(order_count), 0) orderCount,
            ifnull(sum(order_item_count), 0) orderItemCount,
            ifnull(sum(wechat_pay_total_price_deducted_delivery_fee), 0) wechatPayTotalPriceDeductedDeliveryFee,
            ifnull(sum(bill_balance_pay_total_price_deducted_delivery_fee), 0) billBalancePayTotalPriceDeductedDeliveryFee,
            ifnull(sum(wechat_pay_supply_delivery_fee), 0) wechatPaySupplyDeliveryFee,
            ifnull(sum(bill_balance_pay_supply_delivery_fee), 0) billBalancePaySupplyDeliveryFee
        from order_statement_summary
        where tenant_id = #{tenantId} and time_tag between #{startTime} and #{endTime}
    </select>
</mapper>
