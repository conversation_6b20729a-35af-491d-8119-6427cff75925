# 数据库配置
spring:
  datasource:
    hikari: 180000
    username: cosfodb
    password: cosfo612022-
    #?serverTimezone=UTC解决时区的报错
    url: *****************************************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    offline:
      username: cosfo_db
      password: Cosfo619
      url: **************************************************************************************************************************************************************
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: f03ca705-0c69-467a-bc48-466e82b64955
    groupId: saas-manage-pro
    appKey: LvBnp4EVrwB18BDZbf7xNA==
  # redis配置
  redis:
    host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
    port: 6379
    password: summerfarm0619#
    timeout: 5000
    database: 0
  # auth服务依赖
  authRedis:
    host: **************
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 3
saasmall:
  api-host: http://cosfo-mall-svc

redisson:
  address: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com:6379
  password: summerfarm0619#
  type: STANDALONE
  enabled: true
  database: 0

summerfarm:
  mall:
    api-host: http://mall-svc
  api-host: http://manage-svc

rocketmq:
  #name-server: **************:9876
  name-server: http://MQ_INST_1788664839736465_BXz3eVFS.cn-hangzhou.mq-internal.aliyuncs.com:8080
  producer:
    enable-msg-trace: off
    group: GID_saas-manage
    send-message-timeout: 10000
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************
  consumer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************

tenant:
  # 是否开启租户模式
  enable: true
  # 需要排除的多租户的表
  exclusionTable:
    - "system_parameters"
    - "area_city_group"
    - "area_city_group_mapping"
    - "brand_category_mapping"
    - "category"
    - "common_location_city"
    - "file_download_record"
    - "location_province"
    - "order_self_lifting"
    - "product_pricing_supply_city_mapping"
    - "sms_scene"
    - "supplier_delivery_info"
    - "system_admin"
    - "system_admin_role"
    - "system_menu"
    - "system_parameters"
    - "system_permission"
    - "system_role"
    - "system_role_menu"
    - "system_role_permission"
    - "tenant"
    - "tenant_agreement"
    - "user"
    - "wechat_authorizer"
    - "wechat_lite_config"
    - "wechat_template_package"
    - "brand"
    - "tenant_guide_info"
  # 租户字段名称
  column: tenant_id

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://mse-c5bf59b0-nacos-ans.mse.aliyuncs.com:8848
    parameters:
      namespace: 93cffb5c-f43b-4456-8e2b-13312acd2c7c
  protocol:
    id: dubbo
    name: dubbo
    port: 20881
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0

H5:
  mall:
    url: https://mall.confo.cn#/pages/loading/index?token=