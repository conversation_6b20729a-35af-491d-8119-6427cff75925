package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.manage.bill.model.dto.BillProfitSharingQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class BillProfitSharingMapperTest {

    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;

    @Test
    void listAll() {
        BillProfitSharingQueryDTO queryDTO = new BillProfitSharingQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setStartTime(LocalDateTime.now());
        queryDTO.setEndTime(LocalDateTime.now());
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);


        List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingMapper.listAll(queryDTO);
        System.out.println(billProfitSharingDTOS);
    }
}