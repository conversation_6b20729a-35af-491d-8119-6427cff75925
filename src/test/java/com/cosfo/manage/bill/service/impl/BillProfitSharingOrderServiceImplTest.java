package com.cosfo.manage.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.manage.bill.model.dto.BillProfitSharingQueryDTO;
import com.cosfo.manage.bill.model.dto.ReceiveAndOutMoneyDTO;
import com.cosfo.manage.bill.service.BillProfitSharingOrderService;
import com.cosfo.manage.bill.service.FinancialBillService;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BillProfitSharingOrderServiceImplTest {

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Resource
    private FinancialBillService financialBillService;

    @Test
    public void testGetProfitSharingList() {
        CommonResult<PageInfo<BillProfitSharingDTO>> result = billProfitSharingOrderService.listAllProfits(getDefault(), getDefaultLoginContext());
        log.info(JSON.toJSONString(result, true));
        Assert.assertTrue(CollectionUtils.isNotEmpty(result.getData().getList()));
    }

    @Test
    public void testListAllProfitsCountMoney() {
        BillProfitSharingQueryDTO queryDTO = getDefault();
        LoginContextInfoDTO loginContext = getDefaultLoginContext();
        CommonResult<ReceiveAndOutMoneyDTO> result = billProfitSharingOrderService.listAllProfitsCountMoney(queryDTO, loginContext);
        log.info(JSON.toJSONString(result, true));
        Assert.assertFalse(Objects.isNull(result.getData()));

        // 测试分页参数；
        queryDTO.setPageSize(2);
        queryDTO.setPageIndex(1);
        CommonResult<ReceiveAndOutMoneyDTO> result2 = billProfitSharingOrderService.listAllProfitsCountMoney(queryDTO, loginContext);
        log.info(JSON.toJSONString(result2, true));
        Assert.assertFalse(Objects.isNull(result2.getData()));
        Assert.assertFalse(result2.getData().equals(result.getData()));

        // 测试分页参数 & 状态参数；
        queryDTO.setStatus(0);// 0:处理中；1:成功; 2:取消；
        CommonResult<ReceiveAndOutMoneyDTO> result3 = billProfitSharingOrderService.listAllProfitsCountMoney(queryDTO, loginContext);
        log.info(JSON.toJSONString(result3, true));
        Assert.assertFalse(Objects.isNull(result3.getData()));
        Assert.assertFalse(result3.getData().equals(result.getData()));
    }

    @Test
    public void testExportProfitSharingRecords() throws InterruptedException {
        BillProfitSharingQueryDTO queryDTO = getDefault();
        queryDTO.setStartTime(LocalDateTime.parse("2023-03-01T00:00:00"));
        queryDTO.setEndTime(LocalDateTime.parse("2023-05-01T23:59:59"));
        CommonResult commonResult = financialBillService.exportProfitList(getDefaultLoginContext(), queryDTO);
        log.info(JSON.toJSONString(commonResult, true));
        Assert.assertTrue(commonResult != null);
        // 此接口需要异步查询并上传到七牛；
        TimeUnit.MINUTES.sleep(10L);
    }

    private static BillProfitSharingQueryDTO getDefault() {
        BillProfitSharingQueryDTO queryDTO = new BillProfitSharingQueryDTO();
        queryDTO.setStartTime(LocalDateTime.parse("2022-10-31T00:00:00"));
        queryDTO.setEndTime(LocalDateTime.parse("2023-07-14T23:59:59"));
        return queryDTO;
    }

    private static LoginContextInfoDTO getDefaultLoginContext() {
        LoginContextInfoDTO loginContext = new LoginContextInfoDTO();
        loginContext.setTenantId(2L);
        return loginContext;
    }

}
