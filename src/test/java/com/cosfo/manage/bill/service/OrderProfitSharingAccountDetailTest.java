package com.cosfo.manage.bill.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.model.bo.OrderProfitSharingAccountDetailBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单分账账户维度明细测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OrderProfitSharingAccountDetailTest {

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Test
    public void testQueryOrderProfitSharingAccountDetail() {
        List<OrderProfitSharingAccountDetailBO> orderProfitSharingAccountDetailBOS = billProfitSharingOrderService.queryOrderProfitSharingAccountDetail(2L, 137064L);
        // 验证结果
        log.info("orderProfitSharingAccountDetailBOS: {}", JSON.toJSONString(orderProfitSharingAccountDetailBOS));
    }
}
