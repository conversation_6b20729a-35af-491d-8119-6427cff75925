package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.OrderProfitSharingAccountDetailDTO;
import com.cosfo.manage.bill.model.dto.OrderProfitSharingAccountDetailQueryDTO;
import com.cosfo.manage.bill.service.BillProfitSharingOrderService;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 订单分账账户维度明细测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderProfitSharingAccountDetailTest {

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Test
    public void testQueryOrderProfitSharingAccountDetail() {
        // 创建测试请求
        OrderProfitSharingAccountDetailQueryDTO queryDTO = new OrderProfitSharingAccountDetailQueryDTO();
        queryDTO.setTenantId(1L); // 测试租户ID
        queryDTO.setOrderIds(Arrays.asList(12345L, 12346L)); // 测试订单ID

        // 调用方法
        CommonResult<List<OrderProfitSharingAccountDetailDTO>> result = 
            billProfitSharingOrderService.queryOrderProfitSharingAccountDetail(queryDTO);

        // 验证结果
        System.out.println("查询结果: " + result);
        if (result.isSuccess() && result.getData() != null) {
            for (OrderProfitSharingAccountDetailDTO detail : result.getData()) {
                System.out.println("订单ID: " + detail.getOrderId());
                System.out.println("分账方名称: " + detail.getAccountName());
                System.out.println("商品金额: " + detail.getProductAmount());
                System.out.println("运费金额: " + detail.getDeliveryAmount());
                System.out.println("手续费金额: " + detail.getServiceFeeAmount());
                System.out.println("总金额: " + detail.getTotalAmount());
                System.out.println("---");
            }
        }
    }
}
