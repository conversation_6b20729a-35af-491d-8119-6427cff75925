package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-08-03
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserCenterRpcProviderImplTest {


    @Resource
    private TenantProviderImpl tenantProviderImpl;

//    @Resource
//    private TenantProviderOldImpl tenantProviderOldImpl;

    @Test
    public void getTenantInfo() {
        String token = "cosfo-manage__190d0b2c-1e1d-476c-8986-7d51c6649a78";
        DubboResponse<TenantInfoResp> newData = tenantProviderImpl.getTenantInfo(token);
//        DubboResponse<TenantInfoResp> oldData = tenantProviderOldImpl.getTenantInfo(token);
//
//        System.err.println(JSON.toJSONString(newData).equals(JSON.toJSONString(oldData)));
    }

    @Test
    public void list() {
        Long adminId = null;
        Long tenantId = 2L;
        List<Long> tenantIdList = Lists.newArrayList();
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setAdminId(adminId);
        tenantQueryReq.setTenantId(tenantId);
        tenantQueryReq.setTenantIds(tenantIdList);
        DubboResponse<List<TenantResp>> newList = tenantProviderImpl.list(tenantQueryReq);
//        DubboResponse<List<TenantResp>> oldList = tenantProviderOldImpl.list(tenantQueryReq);
//
//        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));

        adminId = 4599L;
        tenantId = null;
        tenantIdList = Lists.newArrayList();
        tenantQueryReq.setAdminId(adminId);
        tenantQueryReq.setTenantId(tenantId);
        tenantQueryReq.setTenantIds(tenantIdList);
        newList = tenantProviderImpl.list(tenantQueryReq);
//        oldList = tenantProviderOldImpl.list(tenantQueryReq);
//
//        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));


        adminId = null;
        tenantId = null;
        tenantIdList = Lists.newArrayList(2L, 1024L);
        tenantQueryReq.setAdminId(adminId);
        tenantQueryReq.setTenantId(tenantId);
        tenantQueryReq.setTenantIds(tenantIdList);
        newList = tenantProviderImpl.list(tenantQueryReq);
//        oldList = tenantProviderOldImpl.list(tenantQueryReq);
//
//        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));

    }

    @Test
    public void listAddress() {
        Long tenantId = 2L;
        DubboResponse<List<String>> newList = tenantProviderImpl.listAddress(tenantId);
//        DubboResponse<List<String>> oldList = tenantProviderOldImpl.listAddress(tenantId);
//
//        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));

        tenantId = 1024L;
        newList = tenantProviderImpl.listAddress(tenantId);
//        oldList = tenantProviderOldImpl.listAddress(tenantId);
//
//        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));
    }
}
