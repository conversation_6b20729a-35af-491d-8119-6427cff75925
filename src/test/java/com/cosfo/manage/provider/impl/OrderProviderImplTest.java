package com.cosfo.manage.provider.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.cosfo.manage.client.order.OrderProvider;
import com.cosfo.manage.client.order.req.OrderReq;
import com.cosfo.manage.facade.ordercenter.OrderFacade;
import com.cosfo.manage.order.mapper.OrderMapper;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.model.po.Order;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderProviderImplTest {

    @Resource
    private OrderProvider orderProvider;

    @Resource
    private OrderBusinessService orderBusinessService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderFacade orderFacade;

    @Test
    public void testQueryOrder() {
        OrderReq req = new OrderReq();
        req.setOrderNo("OR169261251488480");
        System.err.println (orderProvider.queryOrderInfo (req).getData());
    }

    @Test
    public void testQueryOrder2() {
        Long tenantId = 2L;
        LocalDateTime startTime = LocalDateTimeUtil.now().plusDays(-10);
        LocalDateTime endTime = LocalDateTime.now();
        List<OrderDTO> list1 = orderBusinessService.queryBillOrderByStartTimeAndEndTime (tenantId, startTime, endTime);

        List<Order> orders = orderMapper.queryBillOrderByStartTimeAndEndTime(tenantId, startTime, endTime);

        System.err.println("list1.size=" + list1.size() + "  orders.size=" + orders.size());

    }
}
