package com.cosfo.manage.facade.compare.command;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleRuleCommandReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: xiaowk
 * @time: 2024/5/8 下午6:02
 */
@SpringBootTest
public class OrderAfterSaleRuleCommandProviderTest {

    @DubboReference
    private OrderAfterSaleRuleCommandProvider orderAfterSaleRuleCommandProvider;


    @Test
    public void add(){
        OrderAfterSaleRuleCommandReq req = new OrderAfterSaleRuleCommandReq();
        req.setTenantId(22222L);
        req.setType(0);
        req.setDefaultFlag(0);
        req.setRule("{\"applyEndTime\":48,\"autoFinishedTime\":7,\"orderStatusType\":5}");
        DubboResponse<Long> response = orderAfterSaleRuleCommandProvider.add(req);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void updateRule(){
        OrderAfterSaleRuleCommandReq req = new OrderAfterSaleRuleCommandReq();
        req.setId(825L);
        req.setTenantId(222223L);
        req.setType(1);
        req.setDefaultFlag(1);
        req.setRule("{\"applyEndTime\":48,\"autoFinishedTime\":7,\"orderStatusType\":11}");
        DubboResponse<Boolean> response = orderAfterSaleRuleCommandProvider.updateRule(req);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void deleteRule(){
        DubboResponse<Boolean> response = orderAfterSaleRuleCommandProvider.deleteRule(825L);
        System.err.println(JSON.toJSONString(response));
    }
}
