package com.cosfo.manage.facade;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class OfcFacadeTest {

    @Resource
    OfcFacade ofcFacade;

    @Test
    void queryGoodsSupplyOrderDetail() {
        ofcFacade.queryGoodsSupplyOrderDetail(Lists.newArrayList("OR169943482334227", "OR169943529907209", "OR169943536962471", "OR169944116667791"));
    }
}