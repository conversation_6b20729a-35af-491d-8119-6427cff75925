package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAfterSaleRuleQueryFacadeTest {

    @Resource
    private OrderAfterSaleRuleQueryFacade orderAfterSaleRuleQueryFacade;

    @Test
    void queryByTenantId() {
        List<OrderAfterSaleRuleResp> resps = orderAfterSaleRuleQueryFacade.queryByTenantId(2L);
        System.out.println(resps);

    }
}