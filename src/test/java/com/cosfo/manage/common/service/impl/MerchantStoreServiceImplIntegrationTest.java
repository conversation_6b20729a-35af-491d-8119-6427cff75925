package com.cosfo.manage.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
/**
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev4")
class MerchantStoreServiceImplIntegrationTest {

    @Autowired
    private MerchantStoreService merchantStoreService;

    @Test
    void testSaveStoreCodeMapping() throws IOException {
        // Given
        String originalFilename = "testFile1.xlsx";
        String contentType = "text/xlsx";
        File file = ResourceUtils.getFile("/Users/<USER>/Downloads/pos_store_demo.xlsx");
        FileInputStream input = new FileInputStream(file);
        MockMultipartFile multipartFile = new MockMultipartFile("file",
                originalFilename, contentType, input);
        Long tenantId = 2L;

        // When
        ExcelImportResDTO result = merchantStoreService.saveStoreCodeMapping(multipartFile, tenantId);

        // Then
        System.out.println(result);
        assertNotNull(result);
        // Add more assertions based on your business requirements
    }


    @DisplayName("测试三方门店映射字段展示")
    @Test
    void listAllWithThirdCodeMappingTest() {
        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        LoginContextInfoDTO contextInfoDTO = new LoginContextInfoDTO();
        contextInfoDTO.setTenantId(2L);
        merchantStoreQueryDTO.setStoreNo("2345");
        merchantStoreQueryDTO.setPageIndex(1);
        merchantStoreQueryDTO.setPageSize(10);
        PageInfo<MerchantStoreDTO> resultDTO = merchantStoreService.listAll(merchantStoreQueryDTO, contextInfoDTO);
        System.out.println(JSON.toJSONString(resultDTO));
    }

}