package com.cosfo.manage.file.service.impl;

import com.cosfo.manage.file.service.FileDownloadRecordService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/7 12:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileDownloadRecordServiceImplTest {

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Test
    void testDeleteExpiredFile() {
        fileDownloadRecordService.deleteExpiredFileTask();
    }
}
