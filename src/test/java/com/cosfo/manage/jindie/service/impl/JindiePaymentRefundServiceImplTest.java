package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.jindie.service.JindiePaymentRefundService;
import com.cosfo.manage.order.mapper.payment.RefundMapper;
import com.cosfo.manage.order.model.po.payment.Refund;
import com.kingdee.service.data.entity.SaveReply;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.Assert.*;

/**
 * 金蝶退款单推送测试类
 *
 * 注意：运行此测试需要确保以下条件：
 * 1. 已配置好金蝶API的相关参数（ClientApiConfig中的clientId和clientSecret）
 * 2. JindieFacade能够正确获取token
 * 3. 数据库中有可用的退款单数据（如果想使用真实数据）
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JindiePaymentRefundServiceImplTest {

    @Autowired
    private JindiePaymentRefundService jindiePaymentRefundService;
    @Resource
    private RefundMapper refundMapper;

    /**
     * 测试推送退款单到金蝶
     * 此测试方法创建一个模拟的退款单对象，并调用推送方法将其推送到金蝶系统
     * 如果需要使用真实数据，可以通过RefundMapper查询数据库中的退款单
     */
    @Test
    public void pushPaymentRefundToJindie() {
        // 创建一个测试用的Refund对象
        Refund refund = refundMapper.selectByRefundNo("R1920365480791355392");

        try {
            // 调用推送方法
            SaveReply saveReply = jindiePaymentRefundService.pushPaymentRefundToJindie(refund);

            // 打印结果
            System.out.println("推送结果: " + saveReply);

            // 简单断言，确保返回结果不为空
            assertNotNull("推送结果不应为空", saveReply);

            // 如果需要更详细的断言
            if (saveReply != null && saveReply.getIds() != null && !saveReply.getIds().isEmpty()) {
                System.out.println("推送成功，返回的ID: " + saveReply.getIds());
            } else {
                System.out.println("推送可能失败，请检查日志");
            }
        } catch (Exception e) {
            // 捕获异常并打印，便于调试
            System.err.println("推送过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            fail("推送过程中发生异常: " + e.getMessage());
        }
    }
}