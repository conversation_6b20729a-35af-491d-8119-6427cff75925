package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.jindie.service.JindieSalesReturnService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.kingdee.service.data.entity.SaveReply;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 金蝶销售退货单服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JindieSalesReturnServiceImplTest {

    @Autowired
    private JindieSalesReturnService jindieSalesReturnService;

    @Autowired
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    /**
     * 测试推送销售退货单到金蝶
     */
    @Test
    public void pushSalesReturnToJindie() {
        // 查询最近的售后单
        List<OrderAfterSaleResp> afterSaleList = orderAfterSaleQueryFacade.queryByIds(Collections.singletonList(30761L));

        if (CollectionUtils.isEmpty(afterSaleList)) {
            System.out.println("未找到符合条件的售后单，请检查查询条件");
            return;
        }

        // 取第一个售后单进行测试
        OrderAfterSaleResp afterSaleResp = afterSaleList.get(0);
        System.out.println("获取到售后单: " + afterSaleResp.getAfterSaleOrderNo());

        try {
            // 调用推送方法
            SaveReply saveReply = jindieSalesReturnService.pushSalesReturnToJindie(afterSaleResp);

            // 打印结果
            System.out.println("推送结果: " + saveReply);

            // 简单断言，确保返回结果不为空
            assertNotNull("推送结果不应为空", saveReply);

            // 如果需要更详细的断言
            if (saveReply.getIds() != null && !saveReply.getIds().isEmpty()) {
                System.out.println("推送成功，返回的ID: " + saveReply.getIds());
            } else {
                System.out.println("推送可能失败，请检查日志");
            }
        } catch (Exception e) {
            // 捕获异常并打印，便于调试
            System.err.println("推送过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            fail("推送过程中发生异常: " + e.getMessage());
        }
    }
}