package com.cosfo.manage.merchant.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.merchant.MerchantStoreProvider;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/6/9 16:44
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantStoreProviderTest {
    @Resource
    private MerchantStoreProvider merchantStoreProvider;

    @Test
    void batchQueryStoreAddress() {
        DubboResponse<List<MerchantStoreAddressResp>> listDubboResponse = merchantStoreProvider.batchQueryStoreAddress(2L);
        System.out.println(JSON.toJSONString(listDubboResponse));
    }

}
