package com.cosfo.manage.pos.mapper;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.cosfo.manage.pos.model.po.PosBomLog;
import com.google.common.collect.Lists;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.BeforeClass;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class PosBomLogMapperTest {
    private static PosBomLogMapper mapper;

    @BeforeClass
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new MybatisSqlSessionFactoryBuilder().build(PosBomLogMapperTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/PosBomLogMapperTestConfiguration.xml"));
        final MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        builder.getConfiguration().addInterceptor(interceptor);
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(PosBomLogMapper.class, builder.openSession(true));

    }

    @Test
    public void testInsertList() {
        PosBomLog posBomLog = new PosBomLog();
        posBomLog.setId(0L);
        posBomLog.setCreateTime(LocalDateTime.now());
        posBomLog.setUpdateTime(LocalDateTime.now());
        posBomLog.setChannelType(0);
        posBomLog.setTenantId(0L);
//        posBomLog.setOutStoreCode("");
        posBomLog.setOutMenuSpecification("");
        posBomLog.setOutMenuCode("");
        posBomLog.setMerchantStoreId(0L);
        posBomLog.setOutMenuName("");
        posBomLog.setPrice(new BigDecimal("0"));
        posBomLog.setAvailableDate(LocalDate.now());
        posBomLog.setOptType(0);
        posBomLog.setPosBomItem("");


        mapper.insertList(Lists.newArrayList(posBomLog));
    }
}
