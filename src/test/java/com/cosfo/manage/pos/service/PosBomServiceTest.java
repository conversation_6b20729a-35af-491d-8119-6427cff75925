package com.cosfo.manage.pos.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.pos.model.dto.PosBomDTO;
import com.cosfo.manage.pos.model.dto.PosBomQueryDTO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev4")
class PosBomServiceTest {

    @Resource
    private PosBomService posBomService;

    @Test
    void listPage() {
        PosBomQueryDTO posBomQueryDTO = new PosBomQueryDTO();
        posBomQueryDTO.setTenantId(2L);
        posBomQueryDTO.setPageIndex(1);
        posBomQueryDTO.setPageSize(10);
        PageInfo<PosBomDTO> pageInfo = posBomService.listPage(posBomQueryDTO);
        System.out.println(JSON.toJSONString(pageInfo));
    }

    @Test
    void delete() {
        Boolean delete = posBomService.delete(100L);
    }

    @Test
    void detail() {
        PosBomDTO detail = posBomService.detail(101L);
        System.out.println(JSON.toJSONString(detail));
    }

    @Test
    void importExcel() throws IOException {
        String originalFilename = "testFile.xlsx";
        String contentType = "text/xlsx";
        String filePath = "/Users/<USER>/Downloads/bom_test.xlsx"; // replace with your file path
        FileInputStream input = new FileInputStream(Paths.get(filePath).toFile());
        MockMultipartFile multipartFile = new MockMultipartFile("file",
                originalFilename, contentType, input);
        ExcelImportResDTO excelImportResDTO = posBomService.importExcel(multipartFile, 2L);
        System.out.println(JSON.toJSONString(excelImportResDTO));
    }
}