package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.product.service.ProductCategoryService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class ProductCategoryServiceImplTest {
    @Resource
    private ProductCategoryService productCategoryService;

    @Test
    void synchronizedXianmuCategory() {
        productCategoryService.synchronizedXianmuCategory(null);
    }
}