package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.market.model.po.MarketAreaItemStorePriceMapping;
import com.cosfo.manage.market.repository.MarketAreaItemStorePriceMappingRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/6/1 11:33
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AreaItemTest {
    @Resource
    private MarketAreaItemStorePriceMappingRepository mappingRepository;

    @Test
    public void insertBatch(){
        /**
         * 执行前改表名！！！！！！
         */
        List<Long> storeIds = Arrays.asList(2872L,2861L,2860L,2855L,2840L,2835L,2832L,2802L,2798L,2796L,2786L,2779L,2777L,2761L,2759L,2755L,2748L,2747L,2740L,2729L,2728L,2719L,2715L,2710L,2704L,2701L,2691L,2685L,2677L,2661L,2659L,2652L,2644L,2627L,2626L,2624L,2622L,2619L,2616L,2609L,2607L,2509L,2508L,2502L,2488L,2432L,2414L,2383L,2366L,2365L,2361L,2359L,2357L,2350L,2347L,2343L,2332L,2288L,2287L,2280L,2274L,2265L,2260L,2242L,2241L,2240L,2222L,2211L,2144L,2130L,2123L,2119L,2115L,2111L,2109L,2103L,2102L,2100L,2095L,2094L,2075L,2070L,2064L,2056L,2055L,2049L,2048L,2045L,2040L,2032L,1948L,1940L,1927L,1866L,1860L,1859L,1858L,1848L,1338L,1325L,1291L,1225L,1223L,1220L,1218L,1215L,1083L,1070L,1056L,1040L,889L,886L,879L,873L,870L,869L,867L,860L,858L,854L,852L,848L,847L,846L,845L,843L,842L,839L,838L,834L,832L,830L,827L,826L,824L,822L,821L,819L,818L,817L,814L,812L,810L,809L,807L,805L,803L,802L,801L,799L,798L,797L,795L,793L,792L,791L,789L,788L,787L,784L,783L,779L,778L,774L,773L,772L,770L,769L,760L,759L,758L,757L,753L,747L,746L,743L,742L,741L,740L,739L,738L,733L,732L,731L,730L,729L,728L,727L,720L,719L,718L,715L,714L,707L,704L,701L,700L,698L,695L,693L,691L,690L,688L,687L,686L,685L,684L,682L,681L,680L,679L,677L,676L,674L,673L,672L,671L,670L,668L,667L,666L,663L,657L,656L,655L,654L,653L,652L,649L,648L,647L,646L,643L,642L,641L,640L,639L,637L,635L,633L,632L,630L,624L,623L,619L,618L,617L,615L,613L,610L,606L,605L,597L,594L,592L,586L,585L,583L,573L,568L,567L,563L,560L,551L,549L,548L,537L,536L,535L,529L,524L,523L,519L,517L,513L,508L,505L,503L,489L,487L,483L,477L,451L,450L,449L,448L,447L,446L,445L,444L,443L,442L,441L,440L,438L,437L,435L,434L,433L,432L,431L,430L,427L,426L,423L,422L,420L,419L,418L,417L,416L,415L,414L,408L,407L,406L,404L,402L,401L,395L,393L,392L,391L,389L,387L,386L,385L,383L,382L,381L,377L,376L,375L,374L,373L,372L,371L,369L,368L,367L,366L,363L,361L,360L,359L,358L,357L,356L,354L,353L,352L,351L,350L,349L,348L,347L,344L,343L,341L,340L,339L,337L,336L,335L,334L,333L,332L,329L,328L,327L,326L,324L,323L,322L,321L,320L,317L,315L,314L,313L,312L,309L,308L,307L,306L,305L,303L,302L,301L,300L,299L,298L,297L,295L,293L,292L,291L,289L,287L,285L,284L,282L,281L,280L,279L,274L,269L,268L,264L,262L,261L,259L,258L,257L,256L,255L,253L,252L,251L,250L,248L,247L,246L,245L,244L,242L,241L,240L,239L,237L,236L,232L,230L,229L,228L,227L,226L,221L,220L,217L,216L,215L,214L,213L,212L,211L,210L,209L,207L,206L,205L,204L,203L,202L,199L,197L,196L,195L,194L,192L,191L,190L,189L,188L,187L,186L,185L,184L,183L,182L,180L,179L,176L,175L,174L,173L,172L,170L,169L,168L,167L,166L,165L,164L,162L,160L,159L,158L,157L,156L,155L,154L,153L,152L,151L,150L,149L,148L,147L,146L,145L,144L,142L,141L,139L,138L,136L,135L,134L,133L,132L,131L,130L,129L,128L,127L,125L,124L,121L,120L,119L,118L,117L,116L,115L,114L,113L,110L,109L,105L,104L,102L,100L,99L,98L,97L,96L,95L,94L,30L,16L);
        List<MarketAreaItemStorePriceMapping> records = new ArrayList<>();
        for (Long storeId : storeIds) {
            MarketAreaItemStorePriceMapping mapping = new MarketAreaItemStorePriceMapping();
            mapping.setTenantId(4L);
            mapping.setStoreId(storeId);
            mapping.setAreaItemMappingId(9999L);
            records.add(mapping);
        }
        long startTime = System.currentTimeMillis();
        System.out.println("startTime:" + startTime);
        mappingRepository.saveBatch(records);
        long endTime = System.currentTimeMillis();
        System.out.println("startTime:"+endTime);
        // 24279  ms
        System.out.println("插入数据消耗时间：" + (endTime - startTime));

    }
}
